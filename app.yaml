runtime: nodejs18 # Specify the Node.js runtime version you are using
env: standard # Use 'standard' for Standard Environment or 'flex' for Flexible Environment

instance_class: F1 # For Standard: Choose an instance class (F1, F2, etc.)
# service: default # Optional: specify a service name if you are using microservices

automatic_scaling: # Only for Standard Environment
  max_instances: 5 # Set the maximum number of instances
  min_instances: 1 # Set the minimum number of instances (optional)
  target_cpu_utilization: 0.65 # CPU utilization target (optional)
  target_throughput_utilization: 0.75 # Throughput utilization target (optional)
  max_concurrent_requests: 10 # Maximum number of concurrent requests (optional)

# resources: # Only for Flexible Environment
#   cpu: 1
#   memory_gb: 0.5
#   disk_size_gb: 10

handlers: # URL handlers
  - url: /.*
    secure: always # Redirect HTTP to HTTPS
    script: auto

env_variables: # Set environment variables here (if needed)
  NODE_ENV: 'production'
  GOOGLE_RUNTIME_VERSION: "18"

