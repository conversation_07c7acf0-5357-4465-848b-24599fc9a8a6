{"name": "product", "version": "0.1.0", "private": true, "scripts": {"dev": "npm install && next dev", "dev:with-lint-format": "npm run lint && npm run format && next dev", "build": "next build", "start": "next start", "lint": "eslint --fix \"src/**/*.ts\" \"src/**/*.tsx\"", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css}'", "test": "jest", "test:playwright": "npx ts-node tests/saveAuthState.ts && npx playwright test"}, "dependencies": {"@ai-sdk/openai": "^1.2.0", "@ai-sdk/react": "^1.1.20", "@auth0/nextjs-auth0": "^3.5.0", "@aws-sdk/client-s3": "^3.832.0", "@floating-ui/react": "^0.27.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@liveblocks/client": "^2.12.2", "@liveblocks/node": "^2.12.2", "@liveblocks/react": "^2.12.2", "@liveblocks/react-tiptap": "^2.12.2", "@liveblocks/react-ui": "^2.12.2", "@microsoft/fetch-event-source": "^2.0.1", "@novu/node": "^2.0.6", "@novu/notification-center": "^2.0.0-canary.0", "@playwright/test": "^1.48.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.0", "@segment/analytics-next": "^1.61.0", "@segment/analytics-node": "^1.1.3", "@sentry/nextjs": "^8.12.0", "@stripe/stripe-js": "^3.4.1", "@tiptap/extension-character-count": "^2.10.3", "@tiptap/extension-collaboration": "^2.10.3", "@tiptap/extension-collaboration-cursor": "^2.10.3", "@tiptap/extension-highlight": "^2.10.3", "@tiptap/extension-image": "^2.10.3", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-placeholder": "^2.10.3", "@tiptap/extension-task-list": "^2.10.3", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/extension-typography": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/react": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "ai": "^4.1.52", "auth0": "^4.10.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.8.5", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "firebase": "^11.0.2", "framer-motion": "^11.2.10", "fuse.js": "^7.0.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "js-cookies": "^1.0.4", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.363.0", "marked": "^15.0.7", "next": "^15.2.4", "nextjs-toploader": "^1.6.12", "openai": "^4.86.1", "product": "file:", "react": "^18.2.0", "react-confetti": "^6.2.2", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-force-graph-3d": "^1.24.2", "react-markdown": "^10.1.0", "react-modal": "^3.16.1", "react-pdftotext": "^1.3.4", "react-vega": "^7.6.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.2", "stripe": "^15.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.163.0", "tiptap-extension-resize-image": "^1.2.1", "ts-jest": "^29.1.1", "unipile-node-sdk": "^1.9.2", "uuid": "^11.1.0", "vega": "^5.28.0", "vega-lite": "^5.17.0", "zod": "^3.24.2", "zustand": "^4.5.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.1", "@types/auth0": "^3.3.10", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@types/three": "^0.163.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "autoprefixer": "^10.4.16", "encoding": "^0.1.13", "eslint": "^8.57.0", "eslint-config-next": "13.5.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "postcss": "^8.4.31", "prettier": "^3.2.5", "resize-observer-polyfill": "^1.5.1", "tailwindcss": "^3.3.5", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}