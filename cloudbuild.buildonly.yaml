steps:
  - id: "Pull image for cache"
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args:
      [
        "-c",
        "docker pull us-docker.pkg.dev/subconsciousai-shared/holodeck/frontend:latest || exit 0",
      ]

  - id: "Build Docker image"
    name: "gcr.io/cloud-builders/docker"
    env:
      - DOCKER_BUILDKIT=1
    args:
      [
        "build",
        "-t",
        "us-docker.pkg.dev/subconsciousai-shared/holodeck/frontend:$COMMIT_SHA",
        "-t",
        "us-docker.pkg.dev/subconsciousai-shared/holodeck/frontend:latest",
        "--build-arg",
        "BUILDKIT_INLINE_CACHE=1",
        "--build-arg",
        "DOPPLER_TOKEN_ARG=${_DOPPLER_TOKEN}",
        "--cache-from",
        "us-docker.pkg.dev/subconsciousai-shared/holodeck/frontend:latest",
        ".",
      ]
options:
  logging: CLOUD_LOGGING_ONLY
