"use client";
import React, { ReactNode, useState, useEffect } from "react";
import Navigation from "@/app/_components/_layout/Navigation";
import MobileView from "@/app/_components/_mobile/MobileView";

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [isMobileView, setIsMobileView] = useState<boolean>(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth <= 768); // Adjust this threshold as needed
    };

    // Initial check
    handleResize();

    // Event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="flex flex-row w-full h-screen overflow-hidden">
      {isMobileView ? (
        <MobileView /> // Render MobileView component if isMobileView is true
      ) : (
        <>
          <Navigation />
          <div
            className="w-full h-fill mt-3 flex-col bg-background overflow-y-auto"
            style={{ borderTopLeftRadius: "24px" }}
          >
            <div className="h-full overflow-auto">{children}</div>
          </div>
        </>
      )}
    </div>
  );
};

export default MainLayout;
