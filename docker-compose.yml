version: "3.8"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        - DOPPLER_TOKEN_ARG=${DOPPLER_TOKEN}
    container_name: product-v2
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - DOPPLER_TOKEN=${DOPPLER_TOKEN}
    # Restart policy for development
    restart: unless-stopped
volumes:
  node_modules:
