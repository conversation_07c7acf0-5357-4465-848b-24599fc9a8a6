export {};
declare global {
  interface Window {
    pendo: {
      guideId: null;
      initialize: (options: any) => void;
      identify: (visitor: any) => void;
      track: (eventName: string, metadata?: any) => void;
      isReady: {
        onGuideDismissed: (arg: any) => void;
        showGuideById: (id: string) => void;
      };
      onGuideDismissed: (arg: any) => void;
      showGuideById: (id: string) => void;
    };
  }
}
