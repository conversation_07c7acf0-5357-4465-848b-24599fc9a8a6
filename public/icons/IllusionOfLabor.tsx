const IllusionBlank= () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="120" height="120" fill="white"/>
        </svg>


    );
}

const Illusion1a = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M32.3212 4.125C18.0899 4.125 6.51367 15.7012 6.51367 29.9325C6.51367 44.1637 18.0899 55.74 32.3212 55.74C46.5524 55.74 58.1287 44.1637 58.1287 29.9325C58.1287 15.7012 46.5524 4.125 32.3212 4.125ZM32.3212 48.99C21.8137 48.99 13.2637 40.44 13.2637 29.9325C13.2637 19.425 21.8137 10.875 32.3212 10.875C42.8287 10.875 51.3787 19.425 51.3787 29.9325C51.3787 40.44 42.8287 48.99 32.3212 48.99Z" fill="#312E81"/>
        <path d="M37.8336 23.482L29.5461 30.1608L27.3524 27.3595C26.7999 26.6549 25.9901 26.1986 25.1012 26.091C24.2123 25.9834 23.317 26.2333 22.6124 26.7858C21.9077 27.3383 21.4514 28.148 21.3438 29.0369C21.2362 29.9259 21.4861 30.8211 22.0386 31.5258L26.3399 37.0158C26.6161 37.3663 26.959 37.6587 27.3488 37.8762C27.7385 38.0936 28.1675 38.2317 28.6109 38.2826C29.0543 38.3335 29.5034 38.2962 29.9323 38.1727C30.3612 38.0492 30.7614 37.8421 31.1099 37.5633L42.0599 28.7395C42.7207 28.1676 43.1339 27.3618 43.2126 26.4914C43.2913 25.6211 43.0293 24.7541 42.4818 24.073C41.9343 23.3919 41.1439 22.9497 40.277 22.8395C39.41 22.7292 38.5342 22.9596 37.8336 23.482Z" fill="#312E81"/>
        </svg>


    )
}

const Illusion1b = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M67.2637 24.4766H110.111C111.006 24.4766 111.865 24.121 112.498 23.488C113.131 22.8551 113.486 21.9967 113.486 21.1016C113.486 20.2065 113.131 19.348 112.498 18.7151C111.865 18.0821 111.006 17.7266 110.111 17.7266H67.2637C66.3686 17.7266 65.5101 18.0821 64.8772 18.7151C64.2442 19.348 63.8887 20.2065 63.8887 21.1016C63.8887 21.9967 64.2442 22.8551 64.8772 23.488C65.5101 24.121 66.3686 24.4766 67.2637 24.4766ZM67.2637 42.1353H88.6874C89.5825 42.1353 90.441 41.7797 91.0739 41.1468C91.7068 40.5139 92.0624 39.6554 92.0624 38.7603C92.0624 37.8652 91.7068 37.0068 91.0739 36.3738C90.441 35.7409 89.5825 35.3853 88.6874 35.3853H67.2637C66.3686 35.3853 65.5101 35.7409 64.8772 36.3738C64.2442 37.0068 63.8887 37.8652 63.8887 38.7603C63.8887 39.6554 64.2442 40.5139 64.8772 41.1468C65.5101 41.7797 66.3686 42.1353 67.2637 42.1353Z" fill="#667085"/>
        <path d="M32.3212 4.125C18.0899 4.125 6.51367 15.7012 6.51367 29.9325C6.51367 44.1637 18.0899 55.74 32.3212 55.74C46.5524 55.74 58.1287 44.1637 58.1287 29.9325C58.1287 15.7012 46.5524 4.125 32.3212 4.125ZM32.3212 48.99C21.8137 48.99 13.2637 40.44 13.2637 29.9325C13.2637 19.425 21.8137 10.875 32.3212 10.875C42.8287 10.875 51.3787 19.425 51.3787 29.9325C51.3787 40.44 42.8287 48.99 32.3212 48.99Z" fill="#312E81"/>
        <path d="M37.8336 23.482L29.5461 30.1608L27.3524 27.3595C26.7999 26.6549 25.9901 26.1986 25.1012 26.091C24.2123 25.9834 23.317 26.2333 22.6124 26.7858C21.9077 27.3383 21.4514 28.148 21.3438 29.0369C21.2362 29.9259 21.4861 30.8211 22.0386 31.5258L26.3399 37.0158C26.6161 37.3663 26.959 37.6587 27.3488 37.8762C27.7385 38.0936 28.1675 38.2317 28.6109 38.2826C29.0543 38.3335 29.5034 38.2962 29.9323 38.1727C30.3612 38.0492 30.7614 37.8421 31.1099 37.5633L42.0599 28.7395C42.7207 28.1676 43.1339 27.3618 43.2126 26.4914C43.2913 25.6211 43.0293 24.7541 42.4818 24.073C41.9343 23.3919 41.1439 22.9497 40.277 22.8395C39.41 22.7292 38.5342 22.9596 37.8336 23.482Z" fill="#312E81"/>
        </svg>


    )
}

const Illusion1c = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M67.2632 24.4766H110.111C111.006 24.4766 111.864 24.121 112.497 23.488C113.13 22.8551 113.486 21.9967 113.486 21.1016C113.486 20.2065 113.13 19.348 112.497 18.7151C111.864 18.0821 111.006 17.7266 110.111 17.7266H67.2632C66.3681 17.7266 65.5096 18.0821 64.8767 18.7151C64.2438 19.348 63.8882 20.2065 63.8882 21.1016C63.8882 21.9967 64.2438 22.8551 64.8767 23.488C65.5096 24.121 66.3681 24.4766 67.2632 24.4766ZM67.2632 42.1353H88.6869C89.582 42.1353 90.4405 41.7797 91.0734 41.1468C91.7064 40.5139 92.0619 39.6554 92.0619 38.7603C92.0619 37.8652 91.7064 37.0068 91.0734 36.3738C90.4405 35.7409 89.582 35.3853 88.6869 35.3853H67.2632C66.3681 35.3853 65.5096 35.7409 64.8767 36.3738C64.2438 37.0068 63.8882 37.8652 63.8882 38.7603C63.8882 39.6554 64.2438 40.5139 64.8767 41.1468C65.5096 41.7797 66.3681 42.1353 67.2632 42.1353Z" fill="#667085"/>
        <path d="M32.3207 4.125C18.0894 4.125 6.51318 15.7012 6.51318 29.9325C6.51318 44.1637 18.0894 55.74 32.3207 55.74C46.5519 55.74 58.1282 44.1637 58.1282 29.9325C58.1282 15.7012 46.5519 4.125 32.3207 4.125ZM32.3207 48.99C21.8132 48.99 13.2632 40.44 13.2632 29.9325C13.2632 19.425 21.8132 10.875 32.3207 10.875C42.8282 10.875 51.3782 19.425 51.3782 29.9325C51.3782 40.44 42.8282 48.99 32.3207 48.99Z" fill="#312E81"/>
        <path d="M37.8334 23.482L29.5459 30.1608L27.3521 27.3595C26.7997 26.6549 25.9899 26.1986 25.101 26.091C24.212 25.9834 23.3168 26.2333 22.6121 26.7858C21.9075 27.3383 21.4512 28.148 21.3436 29.0369C21.236 29.9259 21.4859 30.8211 22.0384 31.5258L26.3396 37.0158C26.6159 37.3663 26.9588 37.6587 27.3485 37.8762C27.7383 38.0936 28.1672 38.2317 28.6106 38.2826C29.054 38.3335 29.5031 38.2962 29.932 38.1727C30.3609 38.0492 30.7611 37.8421 31.1096 37.5633L42.0596 28.7395C42.7204 28.1676 43.1336 27.3618 43.2123 26.4914C43.291 25.6211 43.029 24.7541 42.4815 24.073C41.934 23.3919 41.1436 22.9497 40.2767 22.8395C39.4098 22.7292 38.5339 22.9596 37.8334 23.482Z" fill="#312E81"/>
        <path d="M32.3207 64.2656C18.0894 64.2656 6.51318 75.8456 6.51318 90.0769C6.51318 104.308 18.0894 115.881 32.3207 115.881C46.5519 115.881 58.1282 104.304 58.1282 90.0731C58.1282 75.8419 46.5519 64.2656 32.3207 64.2656ZM32.3207 109.131C27.2685 109.126 22.4246 107.117 18.8518 103.545C15.279 99.9726 13.2691 95.1291 13.2632 90.0769C13.2632 79.5656 21.8132 71.0194 32.3207 71.0194C42.8282 71.0194 51.3782 79.5656 51.3782 90.0769C51.3712 95.1288 49.361 99.9717 45.7885 103.544C42.2159 107.115 37.3726 109.125 32.3207 109.131Z" fill="#312E81"/>
        <path d="M40.1357 82.2558C39.8229 81.9419 39.4512 81.6929 39.0419 81.523C38.6327 81.3531 38.1939 81.2656 37.7507 81.2656C37.3076 81.2656 36.8688 81.3531 36.4596 81.523C36.0503 81.6929 35.6786 81.9419 35.3657 82.2558L32.3207 85.3008L29.2757 82.2558C28.6382 81.6455 27.7869 81.3092 26.9044 81.319C26.0219 81.3288 25.1783 81.6839 24.5545 82.3082C23.9306 82.9325 23.5762 83.7764 23.5671 84.6589C23.558 85.5415 23.8949 86.3925 24.5057 87.0295L27.547 90.0708L24.5057 93.112C24.1812 93.4227 23.9219 93.7951 23.7432 94.2073C23.5644 94.6195 23.4698 95.0632 23.4648 95.5125C23.4598 95.9618 23.5446 96.4075 23.7141 96.8236C23.8837 97.2397 24.1346 97.6177 24.4522 97.9355C24.7698 98.2534 25.1476 98.5046 25.5635 98.6745C25.9795 98.8443 26.4252 98.9295 26.8744 98.9248C27.3237 98.9202 27.7675 98.8259 28.1799 98.6475C28.5922 98.469 28.9648 98.2101 29.2757 97.8858L32.3207 94.8408L35.3657 97.8858C35.6783 98.2001 36.05 98.4495 36.4593 98.6197C36.8686 98.7899 37.3075 98.8775 37.7507 98.8775C38.194 98.8775 38.6329 98.7899 39.0422 98.6197C39.4515 98.4495 39.8232 98.2001 40.1357 97.8858C40.4493 97.5724 40.698 97.2002 40.8676 96.7907C41.0373 96.3812 41.1246 95.9422 41.1246 95.4989C41.1246 95.0556 41.0373 94.6166 40.8676 94.2071C40.698 93.7975 40.4493 93.4254 40.1357 93.112L37.0945 90.0708L40.1357 87.0295C40.4493 86.7161 40.698 86.344 40.8676 85.9345C41.0373 85.5249 41.1246 85.0859 41.1246 84.6426C41.1246 84.1993 41.0373 83.7604 40.8676 83.3508C40.698 82.9413 40.4493 82.5692 40.1357 82.2558Z" fill="#312E81"/>
        </svg>


    )
}

const Illusion1d = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M67.2632 24.4766H110.111C111.006 24.4766 111.864 24.121 112.497 23.488C113.13 22.8551 113.486 21.9967 113.486 21.1016C113.486 20.2065 113.13 19.348 112.497 18.7151C111.864 18.0821 111.006 17.7266 110.111 17.7266H67.2632C66.3681 17.7266 65.5096 18.0821 64.8767 18.7151C64.2438 19.348 63.8882 20.2065 63.8882 21.1016C63.8882 21.9967 64.2438 22.8551 64.8767 23.488C65.5096 24.121 66.3681 24.4766 67.2632 24.4766ZM67.2632 42.1353H88.6869C89.582 42.1353 90.4405 41.7797 91.0734 41.1468C91.7064 40.5139 92.0619 39.6554 92.0619 38.7603C92.0619 37.8652 91.7064 37.0068 91.0734 36.3738C90.4405 35.7409 89.582 35.3853 88.6869 35.3853H67.2632C66.3681 35.3853 65.5096 35.7409 64.8767 36.3738C64.2438 37.0068 63.8882 37.8652 63.8882 38.7603C63.8882 39.6554 64.2438 40.5139 64.8767 41.1468C65.5096 41.7797 66.3681 42.1353 67.2632 42.1353Z" fill="#667085"/>
        <path d="M32.3207 4.125C18.0894 4.125 6.51318 15.7012 6.51318 29.9325C6.51318 44.1637 18.0894 55.74 32.3207 55.74C46.5519 55.74 58.1282 44.1637 58.1282 29.9325C58.1282 15.7012 46.5519 4.125 32.3207 4.125ZM32.3207 48.99C21.8132 48.99 13.2632 40.44 13.2632 29.9325C13.2632 19.425 21.8132 10.875 32.3207 10.875C42.8282 10.875 51.3782 19.425 51.3782 29.9325C51.3782 40.44 42.8282 48.99 32.3207 48.99Z" fill="#312E81"/>
        <path d="M37.8334 23.482L29.5459 30.1608L27.3521 27.3595C26.7997 26.6549 25.9899 26.1986 25.101 26.091C24.212 25.9834 23.3168 26.2333 22.6121 26.7858C21.9075 27.3383 21.4512 28.148 21.3436 29.0369C21.236 29.9259 21.4859 30.8211 22.0384 31.5258L26.3396 37.0158C26.6159 37.3663 26.9588 37.6587 27.3485 37.8762C27.7383 38.0936 28.1672 38.2317 28.6106 38.2826C29.054 38.3335 29.5031 38.2962 29.932 38.1727C30.3609 38.0492 30.7611 37.8421 31.1096 37.5633L42.0596 28.7395C42.7204 28.1676 43.1336 27.3618 43.2123 26.4914C43.291 25.6211 43.029 24.7541 42.4815 24.073C41.934 23.3919 41.1436 22.9497 40.2767 22.8395C39.4098 22.7292 38.5339 22.9596 37.8334 23.482Z" fill="#312E81"/>
        <path d="M110.107 77.8672H67.2633C66.3682 77.8672 65.5097 78.2228 64.8768 78.8557C64.2439 79.4886 63.8883 80.3471 63.8883 81.2422C63.8883 82.1373 64.2439 82.9957 64.8768 83.6287C65.5097 84.2616 66.3682 84.6172 67.2633 84.6172H110.111C110.554 84.6169 110.993 84.5294 111.402 84.3596C111.812 84.1897 112.183 83.9409 112.497 83.6274C112.81 83.3138 113.058 82.9416 113.228 82.532C113.397 82.1224 113.484 81.6835 113.484 81.2403C113.484 80.7971 113.396 80.3583 113.226 79.9489C113.056 79.5395 112.808 79.1676 112.494 78.8544C112.18 78.5412 111.808 78.2928 111.399 78.1234C110.989 77.954 110.55 77.8669 110.107 77.8672ZM88.6833 95.5259H67.2595C66.3644 95.5259 65.506 95.8815 64.873 96.5145C64.2401 97.1474 63.8845 98.0058 63.8845 98.9009C63.8845 99.796 64.2401 100.654 64.873 101.287C65.506 101.92 66.3644 102.276 67.2595 102.276H88.6833C89.5784 102.276 90.4368 101.92 91.0698 101.287C91.7027 100.654 92.0583 99.796 92.0583 98.9009C92.0583 98.0058 91.7027 97.1474 91.0698 96.5145C90.4368 95.8815 89.5784 95.5259 88.6833 95.5259Z" fill="#667085"/>
        <path d="M32.3207 64.2656C18.0894 64.2656 6.51318 75.8456 6.51318 90.0769C6.51318 104.308 18.0894 115.881 32.3207 115.881C46.5519 115.881 58.1282 104.304 58.1282 90.0731C58.1282 75.8419 46.5519 64.2656 32.3207 64.2656ZM32.3207 109.131C27.2685 109.126 22.4246 107.117 18.8518 103.545C15.279 99.9726 13.2691 95.1291 13.2632 90.0769C13.2632 79.5656 21.8132 71.0194 32.3207 71.0194C42.8282 71.0194 51.3782 79.5656 51.3782 90.0769C51.3712 95.1288 49.361 99.9717 45.7885 103.544C42.2159 107.115 37.3726 109.125 32.3207 109.131Z" fill="#312E81"/>
        <path d="M40.1357 82.2558C39.8229 81.9419 39.4512 81.6929 39.0419 81.523C38.6327 81.3531 38.1939 81.2656 37.7507 81.2656C37.3076 81.2656 36.8688 81.3531 36.4596 81.523C36.0503 81.6929 35.6786 81.9419 35.3657 82.2558L32.3207 85.3008L29.2757 82.2558C28.6382 81.6455 27.7869 81.3092 26.9044 81.319C26.0219 81.3288 25.1783 81.6839 24.5545 82.3082C23.9306 82.9325 23.5762 83.7764 23.5671 84.6589C23.558 85.5415 23.8949 86.3925 24.5057 87.0295L27.547 90.0708L24.5057 93.112C24.1812 93.4227 23.9219 93.7951 23.7432 94.2073C23.5644 94.6195 23.4698 95.0632 23.4648 95.5125C23.4598 95.9618 23.5446 96.4075 23.7141 96.8236C23.8837 97.2397 24.1346 97.6177 24.4522 97.9355C24.7698 98.2534 25.1476 98.5046 25.5635 98.6745C25.9795 98.8443 26.4252 98.9295 26.8744 98.9248C27.3237 98.9202 27.7675 98.8259 28.1799 98.6475C28.5922 98.469 28.9648 98.2101 29.2757 97.8858L32.3207 94.8408L35.3657 97.8858C35.6783 98.2001 36.05 98.4495 36.4593 98.6197C36.8686 98.7899 37.3075 98.8775 37.7507 98.8775C38.194 98.8775 38.6329 98.7899 39.0422 98.6197C39.4515 98.4495 39.8232 98.2001 40.1357 97.8858C40.4493 97.5724 40.698 97.2002 40.8676 96.7907C41.0373 96.3812 41.1246 95.9422 41.1246 95.4989C41.1246 95.0556 41.0373 94.6166 40.8676 94.2071C40.698 93.7975 40.4493 93.4254 40.1357 93.112L37.0945 90.0708L40.1357 87.0295C40.4493 86.7161 40.698 86.344 40.8676 85.9345C41.0373 85.5249 41.1246 85.0859 41.1246 84.6426C41.1246 84.1993 41.0373 83.7604 40.8676 83.3508C40.698 82.9413 40.4493 82.5692 40.1357 82.2558Z" fill="#312E81"/>
        </svg>
    )
}

const Illusion2a = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.31875 55.0416C14.3438 56.0016 19.4813 56.4891 24.5888 56.4891C29.6963 56.4891 34.8337 56.0016 39.8625 55.0416C41.3246 54.7576 42.6423 53.9735 43.5893 52.824C44.5364 51.6744 45.0537 50.231 45.0525 48.7416V46.7878C45.0525 40.2066 41.2612 34.5478 35.7825 31.7091C38.5954 28.8173 40.1706 24.9432 40.1737 20.9091C40.1737 12.3141 33.18 5.32031 24.585 5.32031C15.99 5.32031 9 12.3178 9 20.9128C9 25.1091 10.6838 28.9078 13.3913 31.7128C7.91625 34.5478 4.125 40.2066 4.125 46.7916V48.7453C4.12485 50.2346 4.64311 51.6776 5.59082 52.8264C6.53852 53.9753 7.85658 54.7585 9.31875 55.0416ZM24.5888 12.0741C26.932 12.077 29.1785 13.0092 30.8354 14.6662C32.4923 16.3231 33.4245 18.5695 33.4275 20.9128C33.4275 25.7841 29.46 29.7478 24.5888 29.7478C19.7175 29.7478 15.75 25.7841 15.75 20.9128C15.753 18.5695 16.6852 16.3231 18.3421 14.6662C19.999 13.0092 22.2455 12.077 24.5888 12.0741ZM10.875 46.7916C10.878 44.0624 11.9635 41.4459 13.8933 39.5161C15.8231 37.5863 18.4396 36.5008 21.1688 36.4978H28.0125C30.7417 36.5008 33.3582 37.5863 35.288 39.5161C37.2178 41.4459 38.3033 44.0624 38.3062 46.7916V48.4678C29.2687 50.1591 19.905 50.1591 10.8787 48.4678L10.875 46.7916Z" fill="#312E81"/>
        </svg>
    );
}

const Illusion2b = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.31875 55.0416C14.3438 56.0016 19.4813 56.4891 24.5888 56.4891C29.6963 56.4891 34.8337 56.0016 39.8625 55.0416C41.3246 54.7576 42.6423 53.9735 43.5893 52.824C44.5364 51.6744 45.0537 50.231 45.0525 48.7416V46.7878C45.0525 40.2066 41.2612 34.5478 35.7825 31.7091C38.5954 28.8173 40.1706 24.9432 40.1737 20.9091C40.1737 12.3141 33.18 5.32031 24.585 5.32031C15.99 5.32031 9 12.3178 9 20.9128C9 25.1091 10.6838 28.9078 13.3913 31.7128C7.91625 34.5478 4.125 40.2066 4.125 46.7916V48.7453C4.12485 50.2346 4.64311 51.6776 5.59082 52.8264C6.53852 53.9753 7.85658 54.7585 9.31875 55.0416ZM24.5888 12.0741C26.932 12.077 29.1785 13.0092 30.8354 14.6662C32.4923 16.3231 33.4245 18.5695 33.4275 20.9128C33.4275 25.7841 29.46 29.7478 24.5888 29.7478C19.7175 29.7478 15.75 25.7841 15.75 20.9128C15.753 18.5695 16.6852 16.3231 18.3421 14.6662C19.999 13.0092 22.2455 12.077 24.5888 12.0741ZM10.875 46.7916C10.878 44.0624 11.9635 41.4459 13.8933 39.5161C15.8231 37.5863 18.4396 36.5008 21.1688 36.4978H28.0125C30.7417 36.5008 33.3582 37.5863 35.288 39.5161C37.2178 41.4459 38.3033 44.0624 38.3062 46.7916V48.4678C29.2687 50.1591 19.905 50.1591 10.8787 48.4678L10.875 46.7916Z" fill="#312E81"/>
            <path d="M104.258 8.45312H63.0489C59.969 8.45709 57.0163 9.68235 54.8385 11.8602C52.6606 14.038 51.4354 16.9907 51.4314 20.0706V32.2319C51.4354 35.3118 52.6606 38.2645 54.8385 40.4423C57.0163 42.6201 59.969 43.8454 63.0489 43.8494H67.6839V47.9369C67.6839 50.0369 68.8576 51.9081 70.7476 52.8269C71.6654 53.2739 72.6906 53.4529 73.7055 53.3434C74.7204 53.234 75.6838 52.8405 76.4851 52.2081L87.1089 43.8494H104.254C107.334 43.8454 110.287 42.6203 112.466 40.4425C114.644 38.2648 115.87 35.3122 115.875 32.2319V20.0706C115.871 16.9907 114.646 14.038 112.468 11.8602C110.29 9.68235 107.338 8.45709 104.258 8.45312ZM109.125 32.2281C109.123 33.5185 108.61 34.7554 107.697 35.6678C106.785 36.5802 105.548 37.0936 104.258 37.0956H85.9426C85.1857 37.0968 84.4508 37.3501 83.8539 37.8156L74.4376 45.2256V40.4706C74.4376 39.5755 74.0821 38.7171 73.4491 38.0841C72.8162 37.4512 71.9578 37.0956 71.0626 37.0956H63.0489C61.7586 37.0936 60.5216 36.5802 59.6092 35.6678C58.6968 34.7554 58.1834 33.5185 58.1814 32.2281V20.0706C58.1834 18.7803 58.6968 17.5434 59.6092 16.631C60.5216 15.7186 61.7586 15.2051 63.0489 15.2031H104.254C105.544 15.2051 106.781 15.7186 107.694 16.631C108.606 17.5434 109.119 18.7803 109.121 20.0706L109.125 32.2281Z" fill="#667085"/>
            <path d="M94.4439 22.7656H72.8701C71.975 22.7656 71.1166 23.1212 70.4836 23.7541C69.8507 24.3871 69.4951 25.2455 69.4951 26.1406C69.4951 27.0357 69.8507 27.8942 70.4836 28.5271C71.1166 29.16 71.975 29.5156 72.8701 29.5156H94.4439C95.339 29.5156 96.1974 29.16 96.8304 28.5271C97.4633 27.8942 97.8189 27.0357 97.8189 26.1406C97.8189 25.2455 97.4633 24.3871 96.8304 23.7541C96.1974 23.1212 95.339 22.7656 94.4439 22.7656Z" fill="#667085"/>
        </svg>
    );
}

const Illusion2c = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.31875 55.0416C14.3438 56.0016 19.4813 56.4891 24.5888 56.4891C29.6963 56.4891 34.8337 56.0016 39.8625 55.0416C41.3246 54.7576 42.6423 53.9735 43.5893 52.824C44.5364 51.6744 45.0537 50.231 45.0525 48.7416V46.7878C45.0525 40.2066 41.2612 34.5478 35.7825 31.7091C38.5954 28.8173 40.1706 24.9432 40.1737 20.9091C40.1737 12.3141 33.18 5.32031 24.585 5.32031C15.99 5.32031 9 12.3178 9 20.9128C9 25.1091 10.6838 28.9078 13.3913 31.7128C7.91625 34.5478 4.125 40.2066 4.125 46.7916V48.7453C4.12485 50.2346 4.64311 51.6776 5.59082 52.8264C6.53852 53.9753 7.85658 54.7585 9.31875 55.0416ZM24.5888 12.0741C26.932 12.077 29.1785 13.0092 30.8354 14.6662C32.4923 16.3231 33.4245 18.5695 33.4275 20.9128C33.4275 25.7841 29.46 29.7478 24.5888 29.7478C19.7175 29.7478 15.75 25.7841 15.75 20.9128C15.753 18.5695 16.6852 16.3231 18.3421 14.6662C19.999 13.0092 22.2455 12.077 24.5888 12.0741ZM10.875 46.7916C10.878 44.0624 11.9635 41.4459 13.8933 39.5161C15.8231 37.5863 18.4396 36.5008 21.1688 36.4978H28.0125C30.7417 36.5008 33.3582 37.5863 35.288 39.5161C37.2178 41.4459 38.3033 44.0624 38.3062 46.7916V48.4678C29.2687 50.1591 19.905 50.1591 10.8787 48.4678L10.875 46.7916Z" fill="#312E81"/>
            <path d="M104.258 8.45312H63.0489C59.969 8.45709 57.0163 9.68235 54.8385 11.8602C52.6606 14.038 51.4354 16.9907 51.4314 20.0706V32.2319C51.4354 35.3118 52.6606 38.2645 54.8385 40.4423C57.0163 42.6201 59.969 43.8454 63.0489 43.8494H67.6839V47.9369C67.6839 50.0369 68.8576 51.9081 70.7476 52.8269C71.6654 53.2739 72.6906 53.4529 73.7055 53.3434C74.7204 53.234 75.6838 52.8405 76.4851 52.2081L87.1089 43.8494H104.254C107.334 43.8454 110.287 42.6203 112.466 40.4425C114.644 38.2648 115.87 35.3122 115.875 32.2319V20.0706C115.871 16.9907 114.646 14.038 112.468 11.8602C110.29 9.68235 107.338 8.45709 104.258 8.45312ZM109.125 32.2281C109.123 33.5185 108.61 34.7554 107.697 35.6678C106.785 36.5802 105.548 37.0936 104.258 37.0956H85.9426C85.1857 37.0968 84.4508 37.3501 83.8539 37.8156L74.4376 45.2256V40.4706C74.4376 39.5755 74.0821 38.7171 73.4491 38.0841C72.8162 37.4512 71.9578 37.0956 71.0626 37.0956H63.0489C61.7586 37.0936 60.5216 36.5802 59.6092 35.6678C58.6968 34.7554 58.1834 33.5185 58.1814 32.2281V20.0706C58.1834 18.7803 58.6968 17.5434 59.6092 16.631C60.5216 15.7186 61.7586 15.2051 63.0489 15.2031H104.254C105.544 15.2051 106.781 15.7186 107.694 16.631C108.606 17.5434 109.119 18.7803 109.121 20.0706L109.125 32.2281Z" fill="#667085"/>
            <path d="M94.4439 22.7656H72.8701C71.975 22.7656 71.1166 23.1212 70.4836 23.7541C69.8507 24.3871 69.4951 25.2455 69.4951 26.1406C69.4951 27.0357 69.8507 27.8942 70.4836 28.5271C71.1166 29.16 71.975 29.5156 72.8701 29.5156H94.4439C95.339 29.5156 96.1974 29.16 96.8304 28.5271C97.4633 27.8942 97.8189 27.0357 97.8189 26.1406C97.8189 25.2455 97.4633 24.3871 96.8304 23.7541C96.1974 23.1212 95.339 22.7656 94.4439 22.7656Z" fill="#667085"/>
            <path d="M106.605 89.8888C109.419 86.9974 110.995 83.1234 111 79.0887C111 70.4937 104.006 63.5 95.411 63.5C86.816 63.5 79.826 70.4937 79.826 79.0887C79.826 83.285 81.5098 87.0875 84.2173 89.8925C78.7385 92.7312 74.9473 98.3862 74.9473 104.971V106.925C74.9473 110 77.1335 112.651 80.141 113.225C90.2312 115.151 100.595 115.151 110.685 113.225C112.148 112.942 113.466 112.158 114.414 111.008C115.362 109.859 115.88 108.415 115.879 106.925V104.971C115.875 98.3825 112.084 92.7238 106.605 89.8888ZM95.4073 70.2462C100.282 70.2462 104.242 74.2137 104.242 79.085C104.242 83.9562 100.279 87.9237 95.4073 87.9237C90.536 87.9237 86.576 83.9562 86.576 79.085C86.576 74.2137 90.5398 70.2462 95.4073 70.2462ZM109.125 106.644C100.114 108.331 90.7198 108.335 81.701 106.644V104.967C81.703 102.239 82.7876 99.6222 84.7169 97.6922C86.6461 95.7623 89.2622 94.6767 91.991 94.6737H98.8348C101.564 94.6767 104.18 95.7623 106.109 97.6922C108.038 99.6222 109.123 102.239 109.125 104.967V106.644Z" fill="#312E81"/>
        </svg>
    );
}

const Illusion2d = () => {
    return (
    <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.31875 55.0416C14.3438 56.0016 19.4813 56.4891 24.5888 56.4891C29.6963 56.4891 34.8337 56.0016 39.8625 55.0416C41.3246 54.7576 42.6423 53.9735 43.5893 52.824C44.5364 51.6744 45.0537 50.231 45.0525 48.7416V46.7878C45.0525 40.2066 41.2612 34.5478 35.7825 31.7091C38.5954 28.8173 40.1706 24.9432 40.1737 20.9091C40.1737 12.3141 33.18 5.32031 24.585 5.32031C15.99 5.32031 9 12.3178 9 20.9128C9 25.1091 10.6838 28.9078 13.3913 31.7128C7.91625 34.5478 4.125 40.2066 4.125 46.7916V48.7453C4.12485 50.2346 4.64311 51.6776 5.59082 52.8264C6.53852 53.9753 7.85658 54.7585 9.31875 55.0416ZM24.5888 12.0741C26.932 12.077 29.1785 13.0092 30.8354 14.6662C32.4923 16.3231 33.4245 18.5695 33.4275 20.9128C33.4275 25.7841 29.46 29.7478 24.5888 29.7478C19.7175 29.7478 15.75 25.7841 15.75 20.9128C15.753 18.5695 16.6852 16.3231 18.3421 14.6662C19.999 13.0092 22.2455 12.077 24.5888 12.0741ZM10.875 46.7916C10.878 44.0624 11.9635 41.4459 13.8933 39.5161C15.8231 37.5863 18.4396 36.5008 21.1688 36.4978H28.0125C30.7417 36.5008 33.3582 37.5863 35.288 39.5161C37.2178 41.4459 38.3033 44.0624 38.3062 46.7916V48.4678C29.2687 50.1591 19.905 50.1591 10.8787 48.4678L10.875 46.7916Z" fill="#312E81"/>
        <path d="M104.258 8.45312H63.0489C59.969 8.45709 57.0163 9.68235 54.8385 11.8602C52.6606 14.038 51.4354 16.9907 51.4314 20.0706V32.2319C51.4354 35.3118 52.6606 38.2645 54.8385 40.4423C57.0163 42.6201 59.969 43.8454 63.0489 43.8494H67.6839V47.9369C67.6839 50.0369 68.8576 51.9081 70.7476 52.8269C71.6654 53.2739 72.6906 53.4529 73.7055 53.3434C74.7204 53.234 75.6838 52.8405 76.4851 52.2081L87.1089 43.8494H104.254C107.334 43.8454 110.287 42.6203 112.466 40.4425C114.644 38.2648 115.87 35.3122 115.875 32.2319V20.0706C115.871 16.9907 114.646 14.038 112.468 11.8602C110.29 9.68235 107.338 8.45709 104.258 8.45312ZM109.125 32.2281C109.123 33.5185 108.61 34.7554 107.697 35.6678C106.785 36.5802 105.548 37.0936 104.258 37.0956H85.9426C85.1857 37.0968 84.4508 37.3501 83.8539 37.8156L74.4376 45.2256V40.4706C74.4376 39.5755 74.0821 38.7171 73.4491 38.0841C72.8162 37.4512 71.9578 37.0956 71.0626 37.0956H63.0489C61.7586 37.0936 60.5216 36.5802 59.6092 35.6678C58.6968 34.7554 58.1834 33.5185 58.1814 32.2281V20.0706C58.1834 18.7803 58.6968 17.5434 59.6092 16.631C60.5216 15.7186 61.7586 15.2051 63.0489 15.2031H104.254C105.544 15.2051 106.781 15.7186 107.694 16.631C108.606 17.5434 109.119 18.7803 109.121 20.0706L109.125 32.2281Z" fill="#667085"/>
        <path d="M94.4439 22.7656H72.8701C71.975 22.7656 71.1166 23.1212 70.4836 23.7541C69.8507 24.3871 69.4951 25.2455 69.4951 26.1406C69.4951 27.0357 69.8507 27.8942 70.4836 28.5271C71.1166 29.16 71.975 29.5156 72.8701 29.5156H94.4439C95.339 29.5156 96.1974 29.16 96.8304 28.5271C97.4633 27.8942 97.8189 27.0357 97.8189 26.1406C97.8189 25.2455 97.4633 24.3871 96.8304 23.7541C96.1974 23.1212 95.339 22.7656 94.4439 22.7656Z" fill="#667085"/>
        <path d="M106.605 89.8888C109.419 86.9974 110.995 83.1234 111 79.0887C111 70.4937 104.006 63.5 95.411 63.5C86.816 63.5 79.826 70.4937 79.826 79.0887C79.826 83.285 81.5098 87.0875 84.2173 89.8925C78.7385 92.7312 74.9473 98.3862 74.9473 104.971V106.925C74.9473 110 77.1335 112.651 80.141 113.225C90.2312 115.151 100.595 115.151 110.685 113.225C112.148 112.942 113.466 112.158 114.414 111.008C115.362 109.859 115.88 108.415 115.879 106.925V104.971C115.875 98.3825 112.084 92.7238 106.605 89.8888ZM95.4073 70.2462C100.282 70.2462 104.242 74.2137 104.242 79.085C104.242 83.9562 100.279 87.9237 95.4073 87.9237C90.536 87.9237 86.576 83.9562 86.576 79.085C86.576 74.2137 90.5398 70.2462 95.4073 70.2462ZM109.125 106.644C100.114 108.331 90.7198 108.335 81.701 106.644V104.967C81.703 102.239 82.7876 99.6222 84.7169 97.6922C86.6461 95.7623 89.2622 94.6767 91.991 94.6737H98.8348C101.564 94.6767 104.18 95.7623 106.109 97.6922C108.038 99.6222 109.123 102.239 109.125 104.967V106.644Z" fill="#312E81"/>
        <path d="M56.9512 66.6172H15.7425C12.6626 66.6212 9.70991 67.8464 7.53207 70.0243C5.35423 72.2021 4.12897 75.1548 4.125 78.2347V90.3922C4.12897 93.4721 5.35423 96.4248 7.53207 98.6026C9.70991 100.78 12.6626 102.006 15.7425 102.01H32.8913L43.515 110.368C44.3138 111.004 45.2766 111.4 46.2917 111.511C47.3067 111.621 48.3321 111.441 49.2487 110.991C50.1689 110.549 50.9448 109.854 51.4864 108.989C52.028 108.123 52.3132 107.122 52.3088 106.101V102.013H56.9475C60.0278 102.009 62.9808 100.784 65.1593 98.6066C67.3377 96.4289 68.5638 93.4762 68.5687 90.3959V78.2384C68.5658 75.1579 67.341 72.2042 65.163 70.0256C62.9851 67.8469 60.0318 66.6212 56.9512 66.6172ZM61.8188 90.3922C61.8168 91.6825 61.3033 92.9194 60.3909 93.8318C59.4785 94.7442 58.2416 95.2577 56.9512 95.2597H48.9375C48.0424 95.2597 47.1839 95.6153 46.551 96.2482C45.9181 96.8811 45.5625 97.7396 45.5625 98.6347V103.39L36.15 95.9834C35.5546 95.5148 34.819 95.2599 34.0613 95.2597H15.7425C14.4522 95.2577 13.2153 94.7442 12.3028 93.8318C11.3904 92.9194 10.877 91.6825 10.875 90.3922V78.2347C10.877 76.9444 11.3904 75.7074 12.3028 74.795C13.2153 73.8826 14.4522 73.3692 15.7425 73.3672H56.9512C58.2416 73.3692 59.4785 73.8826 60.3909 74.795C61.3033 75.7074 61.8168 76.9444 61.8188 78.2347V90.3922Z" fill="#667085"/>
        <path d="M47.1338 80.9375H25.5601C24.665 80.9375 23.8065 81.2931 23.1736 81.926C22.5406 82.5589 22.1851 83.4174 22.1851 84.3125C22.1851 85.2076 22.5406 86.0661 23.1736 86.699C23.8065 87.3319 24.665 87.6875 25.5601 87.6875H47.1338C48.0289 87.6875 48.8874 87.3319 49.5203 86.699C50.1532 86.0661 50.5088 85.2076 50.5088 84.3125C50.5088 83.4174 50.1532 82.5589 49.5203 81.926C48.8874 81.2931 48.0289 80.9375 47.1338 80.9375Z" fill="#667085"/>
    </svg>
    );
}

const Illusion3a = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M79.0011 111.562H40.9986C40.1743 111.563 39.3623 111.362 38.6338 110.976C37.9053 110.59 37.2826 110.031 36.82 109.349C36.3574 108.666 36.0692 107.881 35.9805 107.061C35.8918 106.242 36.0053 105.413 36.3111 104.647L37.6311 101.355C38.3833 99.4705 39.5382 97.7731 41.0151 96.3817C42.492 94.9903 44.2552 93.9385 46.1811 93.3L49.8936 92.0625C47.3632 88.8865 46.0021 84.9369 46.0386 80.8762C46.0386 71.9512 52.3011 64.6875 59.9999 64.6875C67.6986 64.6875 73.9611 71.9512 73.9611 80.8762C73.996 84.9361 72.6336 88.8844 70.1024 92.0588L73.8224 93.2962C75.7485 93.9358 77.5115 94.9889 78.9878 96.3816C80.4641 97.7744 81.618 99.4731 82.3686 101.359L83.6849 104.644C83.9914 105.409 84.1056 106.238 84.0175 107.058C83.9293 107.878 83.6415 108.663 83.1793 109.346C82.7171 110.029 82.0945 110.588 81.3661 110.974C80.6377 111.361 79.8257 111.563 79.0011 111.562ZM41.8574 105.938H78.1424L77.1449 103.451C76.6968 102.325 76.0078 101.31 75.1261 100.479C74.2445 99.6469 73.1915 99.018 72.0411 98.6362L65.7224 96.5287C65.0607 96.3098 64.4698 95.9173 64.0114 95.3923C63.553 94.8673 63.2439 94.2289 63.1161 93.5437C62.9862 92.8422 63.0475 92.1186 63.2936 91.4488C63.5398 90.7791 63.9616 90.188 64.5149 89.7375C65.7577 88.627 66.7434 87.2589 67.4033 85.7285C68.0633 84.1981 68.3816 82.5423 68.3361 80.8762C68.5931 78.3771 67.8663 75.8762 66.31 73.904C64.7537 71.9319 62.4903 70.6436 59.9999 70.3125C57.5095 70.6436 55.2461 71.9319 53.6898 73.904C52.1335 75.8762 51.4067 78.3771 51.6636 80.8762C51.6181 82.5432 51.9367 84.2 52.5973 85.7311C53.258 87.2622 54.2447 88.6307 55.4886 89.7412C56.0406 90.1918 56.4614 90.7825 56.7068 91.4515C56.9522 92.1205 57.0133 92.8431 56.8836 93.5437C56.7559 94.2305 56.4459 94.8702 55.986 95.396C55.5262 95.9218 54.9335 96.3144 54.2699 96.5325L47.9586 98.6325C46.8084 99.0145 45.7555 99.6434 44.8739 100.475C43.9923 101.307 43.3032 102.321 42.8549 103.448L41.8574 105.938Z" fill="#312E81"/>
        </svg>

    );
}

const Illusion3b = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M79.0011 111.562H40.9986C40.1743 111.563 39.3623 111.362 38.6338 110.976C37.9053 110.59 37.2826 110.031 36.82 109.349C36.3574 108.666 36.0692 107.881 35.9805 107.061C35.8918 106.242 36.0053 105.413 36.3111 104.647L37.6311 101.355C38.3833 99.4705 39.5382 97.7731 41.0151 96.3817C42.492 94.9903 44.2552 93.9385 46.1811 93.3L49.8936 92.0625C47.3632 88.8865 46.0021 84.9369 46.0386 80.8762C46.0386 71.9512 52.3011 64.6875 59.9999 64.6875C67.6986 64.6875 73.9611 71.9512 73.9611 80.8762C73.996 84.9361 72.6336 88.8844 70.1024 92.0588L73.8224 93.2962C75.7485 93.9358 77.5115 94.9889 78.9878 96.3816C80.4641 97.7744 81.618 99.4731 82.3686 101.359L83.6849 104.644C83.9914 105.409 84.1056 106.238 84.0175 107.058C83.9293 107.878 83.6415 108.663 83.1793 109.346C82.7171 110.029 82.0945 110.588 81.3661 110.974C80.6377 111.361 79.8257 111.563 79.0011 111.562ZM41.8574 105.938H78.1424L77.1449 103.451C76.6968 102.325 76.0078 101.31 75.1261 100.479C74.2445 99.6469 73.1915 99.018 72.0411 98.6362L65.7224 96.5287C65.0607 96.3098 64.4698 95.9173 64.0114 95.3923C63.553 94.8673 63.2439 94.2289 63.1161 93.5437C62.9862 92.8422 63.0475 92.1186 63.2936 91.4488C63.5398 90.7791 63.9616 90.188 64.5149 89.7375C65.7577 88.627 66.7434 87.2589 67.4033 85.7285C68.0633 84.1981 68.3816 82.5423 68.3361 80.8762C68.5931 78.3771 67.8663 75.8762 66.31 73.904C64.7537 71.9319 62.4903 70.6436 59.9999 70.3125C57.5095 70.6436 55.2461 71.9319 53.6898 73.904C52.1335 75.8762 51.4067 78.3771 51.6636 80.8762C51.6181 82.5432 51.9367 84.2 52.5973 85.7311C53.258 87.2622 54.2447 88.6307 55.4886 89.7412C56.0406 90.1918 56.4614 90.7825 56.7068 91.4515C56.9522 92.1205 57.0133 92.8431 56.8836 93.5437C56.7559 94.2305 56.4459 94.8702 55.986 95.396C55.5262 95.9218 54.9335 96.3144 54.2699 96.5325L47.9586 98.6325C46.8084 99.0145 45.7555 99.6434 44.8739 100.475C43.9923 101.307 43.3032 102.321 42.8549 103.448L41.8574 105.938Z" fill="#312E81"/>
            <path d="M46.0048 98.4375H13.1773C12.4033 98.4377 11.641 98.2481 10.9571 97.8855C10.2733 97.5228 9.68872 96.998 9.25466 96.3571C8.82059 95.7162 8.55024 94.9787 8.46724 94.2091C8.38425 93.4394 8.49115 92.6612 8.7786 91.9425L9.91485 89.1C10.586 87.4207 11.6155 85.908 12.9316 84.6677C14.2477 83.4274 15.8187 82.4892 17.5348 81.9187L20.2311 81.0187C18.2079 78.2797 17.1264 74.9589 17.1486 71.5537C16.9159 68.0039 18.0914 64.5053 20.4204 61.8161C22.7494 59.127 26.0444 57.464 29.5911 57.1875C33.1372 57.4649 36.4311 59.1284 38.7594 61.8174C41.0876 64.5064 42.2626 68.0045 42.0298 71.5537C42.0529 74.9579 40.9742 78.2781 38.9548 81.0187L41.6473 81.9187C43.3631 82.4899 44.9338 83.4283 46.2498 84.6685C47.5658 85.9088 48.5956 87.4211 49.2673 89.1L50.3998 91.9425C50.6872 92.6609 50.7941 93.4388 50.7113 94.2081C50.6285 94.9774 50.3585 95.7147 49.9248 96.3556C49.4912 96.9964 48.9072 97.5212 48.2238 97.8842C47.5405 98.2471 46.7786 98.4371 46.0048 98.4375ZM14.4861 92.8125H44.6923L44.0436 91.1887C43.6759 90.2685 43.112 89.4395 42.3911 88.7595C41.6703 88.0795 40.8099 87.5648 39.8698 87.2513L34.4098 85.4363C33.7737 85.225 33.2058 84.8472 32.7652 84.342C32.3246 83.8368 32.0276 83.2227 31.9048 82.5638C31.7813 81.8909 31.8403 81.1972 32.0758 80.5548C32.3112 79.9125 32.7145 79.3449 33.2436 78.9112C34.273 77.9879 35.0891 76.8514 35.635 75.5809C36.1809 74.3103 36.4436 72.9361 36.4048 71.5537C36.6245 69.4975 36.0366 67.4364 34.7653 65.8055C33.494 64.1745 31.6387 63.1014 29.5911 62.8125C27.5428 63.1005 25.6866 64.1733 24.4145 65.8043C23.1424 67.4354 22.554 69.497 22.7736 71.5537C22.7353 72.936 22.9983 74.3101 23.5442 75.5806C24.09 76.851 24.9058 77.9876 25.9348 78.9112C26.4637 79.3436 26.8671 79.9097 27.1032 80.5507C27.3393 81.1916 27.3994 81.8842 27.2773 82.5562C27.1548 83.2167 26.8574 83.8323 26.4162 84.3388C25.975 84.8453 25.406 85.2243 24.7686 85.4363L19.3123 87.255C18.3714 87.5676 17.51 88.0819 16.7884 88.7621C16.0669 89.4422 15.5025 90.2716 15.1348 91.1925L14.4861 92.8125Z" fill="#312E81"/>
        </svg>
    );
}

const Illusion3c = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M79.0011 111.562H40.9986C40.1743 111.563 39.3623 111.362 38.6338 110.976C37.9053 110.59 37.2826 110.031 36.82 109.349C36.3574 108.666 36.0692 107.881 35.9805 107.061C35.8918 106.242 36.0053 105.413 36.3111 104.647L37.6311 101.355C38.3833 99.4705 39.5382 97.7731 41.0151 96.3817C42.492 94.9903 44.2552 93.9385 46.1811 93.3L49.8936 92.0625C47.3632 88.8865 46.0021 84.9369 46.0386 80.8762C46.0386 71.9512 52.3011 64.6875 59.9999 64.6875C67.6986 64.6875 73.9611 71.9512 73.9611 80.8762C73.996 84.9361 72.6336 88.8844 70.1024 92.0588L73.8224 93.2962C75.7485 93.9358 77.5115 94.9889 78.9878 96.3816C80.4641 97.7744 81.618 99.4731 82.3686 101.359L83.6849 104.644C83.9914 105.409 84.1056 106.238 84.0175 107.058C83.9293 107.878 83.6415 108.663 83.1793 109.346C82.7171 110.029 82.0945 110.588 81.3661 110.974C80.6377 111.361 79.8257 111.563 79.0011 111.562ZM41.8574 105.938H78.1424L77.1449 103.451C76.6968 102.325 76.0078 101.31 75.1261 100.479C74.2445 99.6469 73.1915 99.018 72.0411 98.6362L65.7224 96.5287C65.0607 96.3098 64.4698 95.9173 64.0114 95.3923C63.553 94.8673 63.2439 94.2289 63.1161 93.5437C62.9862 92.8422 63.0475 92.1186 63.2936 91.4488C63.5398 90.7791 63.9616 90.188 64.5149 89.7375C65.7577 88.627 66.7434 87.2589 67.4033 85.7285C68.0633 84.1981 68.3816 82.5423 68.3361 80.8762C68.5931 78.3771 67.8663 75.8762 66.31 73.904C64.7537 71.9319 62.4903 70.6436 59.9999 70.3125C57.5095 70.6436 55.2461 71.9319 53.6898 73.904C52.1335 75.8762 51.4067 78.3771 51.6636 80.8762C51.6181 82.5432 51.9367 84.2 52.5973 85.7311C53.258 87.2622 54.2447 88.6307 55.4886 89.7412C56.0406 90.1918 56.4614 90.7825 56.7068 91.4515C56.9522 92.1205 57.0133 92.8431 56.8836 93.5437C56.7559 94.2305 56.4459 94.8702 55.986 95.396C55.5262 95.9218 54.9335 96.3144 54.2699 96.5325L47.9586 98.6325C46.8084 99.0145 45.7555 99.6434 44.8739 100.475C43.9923 101.307 43.3032 102.321 42.8549 103.448L41.8574 105.938Z" fill="#312E81"/>
            <path d="M46.0048 98.4375H13.1773C12.4033 98.4377 11.641 98.2481 10.9571 97.8855C10.2733 97.5228 9.68872 96.998 9.25466 96.3571C8.82059 95.7162 8.55024 94.9787 8.46724 94.2091C8.38425 93.4394 8.49115 92.6612 8.7786 91.9425L9.91485 89.1C10.586 87.4207 11.6155 85.908 12.9316 84.6677C14.2477 83.4274 15.8187 82.4892 17.5348 81.9187L20.2311 81.0187C18.2079 78.2797 17.1264 74.9589 17.1486 71.5537C16.9159 68.0039 18.0914 64.5053 20.4204 61.8161C22.7494 59.127 26.0444 57.464 29.5911 57.1875C33.1372 57.4649 36.4311 59.1284 38.7594 61.8174C41.0876 64.5064 42.2626 68.0045 42.0298 71.5537C42.0529 74.9579 40.9742 78.2781 38.9548 81.0187L41.6473 81.9187C43.3631 82.4899 44.9338 83.4283 46.2498 84.6685C47.5658 85.9088 48.5956 87.4211 49.2673 89.1L50.3998 91.9425C50.6872 92.6609 50.7941 93.4388 50.7113 94.2081C50.6285 94.9774 50.3585 95.7147 49.9248 96.3556C49.4912 96.9964 48.9072 97.5212 48.2238 97.8842C47.5405 98.2471 46.7786 98.4371 46.0048 98.4375ZM14.4861 92.8125H44.6923L44.0436 91.1887C43.6759 90.2685 43.112 89.4395 42.3911 88.7595C41.6703 88.0795 40.8099 87.5648 39.8698 87.2513L34.4098 85.4363C33.7737 85.225 33.2058 84.8472 32.7652 84.342C32.3246 83.8368 32.0276 83.2227 31.9048 82.5638C31.7813 81.8909 31.8403 81.1972 32.0758 80.5548C32.3112 79.9125 32.7145 79.3449 33.2436 78.9112C34.273 77.9879 35.0891 76.8514 35.635 75.5809C36.1809 74.3103 36.4436 72.9361 36.4048 71.5537C36.6245 69.4975 36.0366 67.4364 34.7653 65.8055C33.494 64.1745 31.6387 63.1014 29.5911 62.8125C27.5428 63.1005 25.6866 64.1733 24.4145 65.8043C23.1424 67.4354 22.554 69.497 22.7736 71.5537C22.7353 72.936 22.9983 74.3101 23.5442 75.5806C24.09 76.851 24.9058 77.9876 25.9348 78.9112C26.4637 79.3436 26.8671 79.9097 27.1032 80.5507C27.3393 81.1916 27.3994 81.8842 27.2773 82.5562C27.1548 83.2167 26.8574 83.8323 26.4162 84.3388C25.975 84.8453 25.406 85.2243 24.7686 85.4363L19.3123 87.255C18.3714 87.5676 17.51 88.0819 16.7884 88.7621C16.0669 89.4422 15.5025 90.2716 15.1348 91.1925L14.4861 92.8125ZM106.822 98.4375H73.9948C73.2211 98.4371 72.4592 98.2471 71.7759 97.8842C71.0925 97.5212 70.5085 96.9964 70.0749 96.3556C69.6412 95.7147 69.3712 94.9774 69.2884 94.2081C69.2056 93.4388 69.3125 92.6609 69.5998 91.9425L70.7323 89.1C71.4039 87.4209 72.4336 85.9085 73.7496 84.6682C75.0657 83.428 76.6364 82.4897 78.3523 81.9187L81.0448 81.0187C79.0255 78.2781 77.9468 74.9579 77.9698 71.5537C77.7371 68.0045 78.9121 64.5064 81.2403 61.8174C83.5686 59.1284 86.8625 57.4649 90.4086 57.1875C93.9553 57.464 97.2502 59.127 99.5793 61.8161C101.908 64.5053 103.084 68.0039 102.851 71.5537C102.874 74.9579 101.796 78.2783 99.7761 81.0187L102.465 81.9187C104.181 82.4892 105.752 83.4274 107.068 84.6677C108.384 85.908 109.414 87.4207 110.085 89.1L111.221 91.9425C111.509 92.6612 111.615 93.4394 111.532 94.2091C111.449 94.9787 111.179 95.7162 110.745 96.3571C110.311 96.998 109.726 97.5228 109.043 97.8855C108.359 98.2481 107.596 98.4377 106.822 98.4375ZM75.3074 92.8125H105.514L104.865 91.1887C104.497 90.268 103.933 89.4387 103.211 88.7586C102.489 88.0785 101.628 87.5641 100.687 87.2513L95.2273 85.4363C94.5906 85.2237 94.0225 84.8445 93.5819 84.338C93.1414 83.8315 92.8446 83.2163 92.7223 82.5562C92.6012 81.8829 92.6625 81.1895 92.8999 80.5478C93.1373 79.9062 93.5421 79.3398 94.0723 78.9075C95.0988 77.9839 95.9121 76.8482 96.456 75.5791C96.9999 74.3099 97.2614 72.9377 97.2224 71.5575C97.4431 69.5007 96.8557 67.4386 95.5842 65.8068C94.3128 64.1751 92.4569 63.1014 90.4086 62.8125C88.361 63.1014 86.5057 64.1745 85.2344 65.8055C83.963 67.4364 83.3752 69.4975 83.5948 71.5537C83.5566 72.936 83.8195 74.3101 84.3654 75.5806C84.9113 76.851 85.7271 77.9876 86.7561 78.9112C87.2855 79.3447 87.689 79.9122 87.9245 80.5546C88.1599 81.197 88.2188 81.8909 88.0948 82.5638C87.9721 83.2227 87.675 83.8368 87.2345 84.342C86.7939 84.8472 86.226 85.225 85.5898 85.4363L80.1298 87.255C79.1897 87.5683 78.3291 88.0829 77.6083 88.763C76.8874 89.443 76.3236 90.2722 75.9561 91.1925L75.3074 92.8125Z" fill="#312E81"/>
        </svg>

    );
}

const Illusion3d = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M79.0011 111.562H40.9986C40.1743 111.563 39.3623 111.362 38.6338 110.976C37.9053 110.59 37.2826 110.031 36.82 109.349C36.3574 108.666 36.0692 107.881 35.9805 107.061C35.8918 106.242 36.0053 105.413 36.3111 104.647L37.6311 101.355C38.3833 99.4705 39.5382 97.7731 41.0151 96.3817C42.492 94.9903 44.2552 93.9385 46.1811 93.3L49.8936 92.0625C47.3632 88.8865 46.0021 84.9369 46.0386 80.8762C46.0386 71.9512 52.3011 64.6875 59.9999 64.6875C67.6986 64.6875 73.9611 71.9512 73.9611 80.8762C73.996 84.9361 72.6336 88.8844 70.1024 92.0588L73.8224 93.2962C75.7485 93.9358 77.5115 94.9889 78.9878 96.3816C80.4641 97.7744 81.618 99.4731 82.3686 101.359L83.6849 104.644C83.9914 105.409 84.1056 106.238 84.0175 107.058C83.9293 107.878 83.6415 108.663 83.1793 109.346C82.7171 110.029 82.0945 110.588 81.3661 110.974C80.6377 111.361 79.8257 111.563 79.0011 111.562ZM41.8574 105.938H78.1424L77.1449 103.451C76.6968 102.325 76.0078 101.31 75.1261 100.479C74.2445 99.6469 73.1915 99.018 72.0411 98.6362L65.7224 96.5287C65.0607 96.3098 64.4698 95.9173 64.0114 95.3923C63.553 94.8673 63.2439 94.2289 63.1161 93.5437C62.9862 92.8422 63.0475 92.1186 63.2936 91.4488C63.5398 90.7791 63.9616 90.188 64.5149 89.7375C65.7577 88.627 66.7434 87.2589 67.4033 85.7285C68.0633 84.1981 68.3816 82.5423 68.3361 80.8762C68.5931 78.3771 67.8663 75.8762 66.31 73.904C64.7537 71.9319 62.4903 70.6436 59.9999 70.3125C57.5095 70.6436 55.2461 71.9319 53.6898 73.904C52.1335 75.8762 51.4067 78.3771 51.6636 80.8762C51.6181 82.5432 51.9367 84.2 52.5973 85.7311C53.258 87.2622 54.2447 88.6307 55.4886 89.7412C56.0406 90.1918 56.4614 90.7825 56.7068 91.4515C56.9522 92.1205 57.0133 92.8431 56.8836 93.5437C56.7559 94.2305 56.4459 94.8702 55.986 95.396C55.5262 95.9218 54.9335 96.3144 54.2699 96.5325L47.9586 98.6325C46.8084 99.0145 45.7555 99.6434 44.8739 100.475C43.9923 101.307 43.3032 102.321 42.8549 103.448L41.8574 105.938Z" fill="#312E81"/>
            <path d="M46.0048 98.4375H13.1773C12.4033 98.4377 11.641 98.2481 10.9571 97.8855C10.2733 97.5228 9.68872 96.998 9.25466 96.3571C8.82059 95.7162 8.55024 94.9787 8.46724 94.2091C8.38425 93.4394 8.49115 92.6612 8.7786 91.9425L9.91485 89.1C10.586 87.4207 11.6155 85.908 12.9316 84.6677C14.2477 83.4274 15.8187 82.4892 17.5348 81.9187L20.2311 81.0187C18.2079 78.2797 17.1264 74.9589 17.1486 71.5537C16.9159 68.0039 18.0914 64.5053 20.4204 61.8161C22.7494 59.127 26.0444 57.464 29.5911 57.1875C33.1372 57.4649 36.4311 59.1284 38.7594 61.8174C41.0876 64.5064 42.2626 68.0045 42.0298 71.5537C42.0529 74.9579 40.9742 78.2781 38.9548 81.0187L41.6473 81.9187C43.3631 82.4899 44.9338 83.4283 46.2498 84.6685C47.5658 85.9088 48.5956 87.4211 49.2673 89.1L50.3998 91.9425C50.6872 92.6609 50.7941 93.4388 50.7113 94.2081C50.6285 94.9774 50.3585 95.7147 49.9248 96.3556C49.4912 96.9964 48.9072 97.5212 48.2238 97.8842C47.5405 98.2471 46.7786 98.4371 46.0048 98.4375ZM14.4861 92.8125H44.6923L44.0436 91.1887C43.6759 90.2685 43.112 89.4395 42.3911 88.7595C41.6703 88.0795 40.8099 87.5648 39.8698 87.2513L34.4098 85.4363C33.7737 85.225 33.2058 84.8472 32.7652 84.342C32.3246 83.8368 32.0276 83.2227 31.9048 82.5638C31.7813 81.8909 31.8403 81.1972 32.0758 80.5548C32.3112 79.9125 32.7145 79.3449 33.2436 78.9112C34.273 77.9879 35.0891 76.8514 35.635 75.5809C36.1809 74.3103 36.4436 72.9361 36.4048 71.5537C36.6245 69.4975 36.0366 67.4364 34.7653 65.8055C33.494 64.1745 31.6387 63.1014 29.5911 62.8125C27.5428 63.1005 25.6866 64.1733 24.4145 65.8043C23.1424 67.4354 22.554 69.497 22.7736 71.5537C22.7353 72.936 22.9983 74.3101 23.5442 75.5806C24.09 76.851 24.9058 77.9876 25.9348 78.9112C26.4637 79.3436 26.8671 79.9097 27.1032 80.5507C27.3393 81.1916 27.3994 81.8842 27.2773 82.5562C27.1548 83.2167 26.8574 83.8323 26.4162 84.3388C25.975 84.8453 25.406 85.2243 24.7686 85.4363L19.3123 87.255C18.3714 87.5676 17.51 88.0819 16.7884 88.7621C16.0669 89.4422 15.5025 90.2716 15.1348 91.1925L14.4861 92.8125ZM106.822 98.4375H73.9948C73.2211 98.4371 72.4592 98.2471 71.7759 97.8842C71.0925 97.5212 70.5085 96.9964 70.0749 96.3556C69.6412 95.7147 69.3712 94.9774 69.2884 94.2081C69.2056 93.4388 69.3125 92.6609 69.5998 91.9425L70.7323 89.1C71.4039 87.4209 72.4336 85.9085 73.7496 84.6682C75.0657 83.428 76.6364 82.4897 78.3523 81.9187L81.0448 81.0187C79.0255 78.2781 77.9468 74.9579 77.9698 71.5537C77.7371 68.0045 78.9121 64.5064 81.2403 61.8174C83.5686 59.1284 86.8625 57.4649 90.4086 57.1875C93.9553 57.464 97.2502 59.127 99.5793 61.8161C101.908 64.5053 103.084 68.0039 102.851 71.5537C102.874 74.9579 101.796 78.2783 99.7761 81.0187L102.465 81.9187C104.181 82.4892 105.752 83.4274 107.068 84.6677C108.384 85.908 109.414 87.4207 110.085 89.1L111.221 91.9425C111.509 92.6612 111.615 93.4394 111.532 94.2091C111.449 94.9787 111.179 95.7162 110.745 96.3571C110.311 96.998 109.726 97.5228 109.043 97.8855C108.359 98.2481 107.596 98.4377 106.822 98.4375ZM75.3074 92.8125H105.514L104.865 91.1887C104.497 90.268 103.933 89.4387 103.211 88.7586C102.489 88.0785 101.628 87.5641 100.687 87.2513L95.2273 85.4363C94.5906 85.2237 94.0225 84.8445 93.5819 84.338C93.1414 83.8315 92.8446 83.2163 92.7223 82.5562C92.6012 81.8829 92.6625 81.1895 92.8999 80.5478C93.1373 79.9062 93.5421 79.3398 94.0723 78.9075C95.0988 77.9839 95.9121 76.8482 96.456 75.5791C96.9999 74.3099 97.2614 72.9377 97.2224 71.5575C97.4431 69.5007 96.8557 67.4386 95.5842 65.8068C94.3128 64.1751 92.4569 63.1014 90.4086 62.8125C88.361 63.1014 86.5057 64.1745 85.2344 65.8055C83.963 67.4364 83.3752 69.4975 83.5948 71.5537C83.5566 72.936 83.8195 74.3101 84.3654 75.5806C84.9113 76.851 85.7271 77.9876 86.7561 78.9112C87.2855 79.3447 87.689 79.9122 87.9245 80.5546C88.1599 81.197 88.2188 81.8909 88.0948 82.5638C87.9721 83.2227 87.675 83.8368 87.2345 84.342C86.7939 84.8472 86.226 85.225 85.5898 85.4363L80.1298 87.255C79.1897 87.5683 78.3291 88.0829 77.6083 88.763C76.8874 89.443 76.3236 90.2722 75.9561 91.1925L75.3074 92.8125Z" fill="#312E81"/>
            <path d="M59.9998 53.4375C56.1059 53.4375 52.2996 52.2829 49.062 50.1196C45.8244 47.9563 43.301 44.8815 41.8109 41.2841C40.3208 37.6867 39.9309 33.7282 40.6906 29.9092C41.4502 26.0902 43.3253 22.5822 46.0786 19.8288C48.8319 17.0755 52.3399 15.2004 56.1589 14.4408C59.9779 13.6812 63.9364 14.071 67.5338 15.5611C71.1313 17.0512 74.206 19.5746 76.3693 22.8122C78.5326 26.0498 79.6873 29.8562 79.6873 33.75C79.6813 38.9696 77.6052 43.9738 73.9144 47.6646C70.2235 51.3554 65.2194 53.4316 59.9998 53.4375ZM59.9998 19.6875C57.2185 19.6875 54.4996 20.5123 52.1871 22.0575C49.8745 23.6027 48.0721 25.7989 47.0077 28.3685C45.9434 30.9381 45.6649 33.7656 46.2075 36.4935C46.7501 39.2213 48.0894 41.727 50.0561 43.6937C52.0228 45.6604 54.5285 46.9997 57.2563 47.5423C59.9842 48.0849 62.8117 47.8064 65.3813 46.7421C67.9508 45.6777 70.1471 43.8753 71.6923 41.5627C73.2375 39.2501 74.0623 36.5313 74.0623 33.75C74.0583 30.0216 72.5754 26.4471 69.9391 23.8107C67.3027 21.1743 63.7282 19.6915 59.9998 19.6875Z" fill="#667085"/>
            <path d="M59.9998 37.5C62.0708 37.5 63.7498 35.8211 63.7498 33.75C63.7498 31.6789 62.0708 30 59.9998 30C57.9287 30 56.2498 31.6789 56.2498 33.75C56.2498 35.8211 57.9287 37.5 59.9998 37.5Z" fill="#667085"/>
            <path d="M59.9998 25.3125C59.2538 25.3125 58.5385 25.0162 58.011 24.4887C57.4836 23.9613 57.1873 23.2459 57.1873 22.5V11.25C57.1873 10.5041 57.4836 9.78871 58.011 9.26126C58.5385 8.73382 59.2538 8.4375 59.9998 8.4375C60.7457 8.4375 61.4611 8.73382 61.9885 9.26126C62.5159 9.78871 62.8123 10.5041 62.8123 11.25V22.5C62.8123 23.2459 62.5159 23.9613 61.9885 24.4887C61.4611 25.0162 60.7457 25.3125 59.9998 25.3125ZM82.4998 36.5625H71.2498C70.5038 36.5625 69.7885 36.2662 69.261 35.7387C68.7336 35.2113 68.4373 34.4959 68.4373 33.75C68.4373 33.0041 68.7336 32.2887 69.261 31.7613C69.7885 31.2338 70.5038 30.9375 71.2498 30.9375H82.4998C83.2457 30.9375 83.961 31.2338 84.4885 31.7613C85.0159 32.2887 85.3123 33.0041 85.3123 33.75C85.3123 34.4959 85.0159 35.2113 84.4885 35.7387C83.961 36.2662 83.2457 36.5625 82.4998 36.5625ZM59.9998 59.0625C59.2538 59.0625 58.5385 58.7662 58.011 58.2387C57.4836 57.7113 57.1873 56.9959 57.1873 56.25V45C57.1873 44.2541 57.4836 43.5387 58.011 43.0113C58.5385 42.4838 59.2538 42.1875 59.9998 42.1875C60.7457 42.1875 61.4611 42.4838 61.9885 43.0113C62.5159 43.5387 62.8123 44.2541 62.8123 45V56.25C62.8123 56.9959 62.5159 57.7113 61.9885 58.2387C61.4611 58.7662 60.7457 59.0625 59.9998 59.0625ZM48.7498 36.5625H37.4998C36.7538 36.5625 36.0385 36.2662 35.511 35.7387C34.9836 35.2113 34.6873 34.4959 34.6873 33.75C34.6873 33.0041 34.9836 32.2887 35.511 31.7613C36.0385 31.2338 36.7538 30.9375 37.4998 30.9375H48.7498C49.4957 30.9375 50.211 31.2338 50.7385 31.7613C51.2659 32.2887 51.5623 33.0041 51.5623 33.75C51.5623 34.4959 51.2659 35.2113 50.7385 35.7387C50.211 36.2662 49.4957 36.5625 48.7498 36.5625Z" fill="#667085"/>
        </svg>
    );
}

const Illusion4a = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M55.0571 112.499C53.4154 112.499 51.7646 112.416 50.1046 112.249C38.2278 111.019 27.2498 105.362 19.3561 96.403C11.4624 87.4443 7.23208 75.8414 7.50726 63.9043C7.78244 51.9672 12.543 40.5716 20.8411 31.9861C29.1392 23.4007 40.3663 18.2551 52.2871 17.5739C52.9528 17.5276 53.6209 17.6225 54.2474 17.8526C54.8738 18.0827 55.4446 18.4427 55.9221 18.9089C56.4199 19.3804 56.8164 19.9482 57.0876 20.5779C57.3589 21.2075 57.499 21.8858 57.4996 22.5714V63.9639L86.7696 93.2339C87.2535 93.7193 87.6336 94.2981 87.8867 94.9351C88.1399 95.572 88.2608 96.2538 88.2421 96.9389C88.2275 97.6063 88.0752 98.2635 87.7947 98.8692C87.5142 99.4749 87.1116 100.016 86.6121 100.459C77.9442 108.236 66.7025 112.524 55.0571 112.499ZM52.4996 22.5689C45.7341 22.9682 39.1621 24.9796 33.332 28.4352C27.5019 31.8909 22.5827 36.6906 18.9848 42.434C15.3869 48.1775 13.2146 54.698 12.6492 61.4517C12.0838 68.2053 13.1416 74.9963 15.7344 81.258C18.3272 87.5197 22.3799 93.0706 27.5542 97.4475C32.7285 101.825 38.8746 104.901 45.4795 106.419C52.0845 107.938 58.9568 107.855 65.5232 106.178C72.0897 104.5 78.1597 101.277 83.2271 96.7764L53.9646 67.4964C53.0296 66.5571 52.5031 65.2868 52.4996 63.9614V22.5689Z" fill="#312E81" stroke="#312E81" strokeWidth="2"/>
        </svg>

    );
}

const Illusion4b = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M55.0571 112.499C53.4154 112.499 51.7646 112.416 50.1046 112.249C38.2278 111.019 27.2498 105.362 19.3561 96.403C11.4624 87.4443 7.23208 75.8414 7.50726 63.9043C7.78244 51.9672 12.543 40.5716 20.8411 31.9861C29.1392 23.4007 40.3663 18.2551 52.2871 17.5739C52.9528 17.5276 53.6209 17.6225 54.2474 17.8526C54.8738 18.0827 55.4446 18.4427 55.9221 18.9089C56.4199 19.3804 56.8164 19.9482 57.0876 20.5779C57.3589 21.2075 57.499 21.8858 57.4996 22.5714V63.9639L86.7696 93.2339C87.2535 93.7193 87.6336 94.2981 87.8867 94.9351C88.1399 95.572 88.2608 96.2538 88.2421 96.9389C88.2275 97.6063 88.0752 98.2635 87.7947 98.8692C87.5142 99.4749 87.1116 100.016 86.6121 100.459C77.9442 108.236 66.7025 112.524 55.0571 112.499ZM52.4996 22.5689C45.7341 22.9682 39.1621 24.9796 33.332 28.4352C27.5019 31.8909 22.5827 36.6906 18.9848 42.434C15.3869 48.1775 13.2146 54.698 12.6492 61.4517C12.0838 68.2053 13.1416 74.9963 15.7344 81.258C18.3272 87.5197 22.3799 93.0706 27.5542 97.4475C32.7285 101.825 38.8746 104.901 45.4795 106.419C52.0845 107.938 58.9568 107.855 65.5232 106.178C72.0897 104.5 78.1597 101.277 83.2271 96.7764L53.9646 67.4964C53.0296 66.5571 52.5031 65.2868 52.4996 63.9614V22.5689Z" fill="#312E81" stroke="#312E81" strokeWidth="2"/>
            <path d="M97.8168 94.29C96.8155 94.2878 95.8557 93.8897 95.1468 93.1825L66.3643 64.4025C65.8395 63.878 65.4821 63.2097 65.3373 62.4821C65.1925 61.7544 65.2667 61.0001 65.5507 60.3147C65.8347 59.6293 66.3156 59.0435 66.9326 58.6314C67.5496 58.2194 68.2749 57.9997 69.0168 58H109.712C110.214 58.0002 110.712 58.1008 111.175 58.296C111.638 58.4912 112.058 58.777 112.409 59.1366C112.76 59.4962 113.036 59.9223 113.22 60.39C113.405 60.8577 113.493 61.3575 113.482 61.86C113.148 73.513 108.54 84.6388 100.537 93.115C100.195 93.481 99.782 93.7739 99.3235 93.9758C98.865 94.1778 98.3703 94.2847 97.8693 94.29H97.8168ZM72.0343 63L97.7693 88.735C104.112 81.5974 107.867 72.5321 108.429 63H72.0343Z" fill="#667085" stroke="#667085" strokeWidth="2"/>
        </svg>

    );
}

const Illusion4c = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M55.0573 112.499C53.4156 112.499 51.7648 112.416 50.1048 112.249C38.2281 111.019 27.2501 105.362 19.3564 96.403C11.4627 87.4443 7.23232 75.8414 7.5075 63.9043C7.78268 51.9672 12.5432 40.5716 20.8413 31.9861C29.1395 23.4007 40.3665 18.2551 52.2873 17.5739C52.9531 17.5276 53.6211 17.6225 54.2476 17.8526C54.8741 18.0827 55.4448 18.4427 55.9223 18.9089C56.4201 19.3804 56.8167 19.9482 57.0879 20.5779C57.3591 21.2075 57.4993 21.8858 57.4998 22.5714V63.9639L86.7698 93.2339C87.2537 93.7193 87.6338 94.2981 87.887 94.9351C88.1401 95.572 88.261 96.2538 88.2423 96.9389C88.2278 97.6063 88.0755 98.2635 87.795 98.8692C87.5145 99.4749 87.1118 100.016 86.6123 100.459C77.9444 108.236 66.7027 112.524 55.0573 112.499ZM52.4998 22.5689C45.7343 22.9682 39.1623 24.9796 33.3322 28.4352C27.5021 31.8909 22.5829 36.6906 18.985 42.434C15.3871 48.1775 13.2149 54.698 12.6494 61.4517C12.084 68.2053 13.1418 74.9963 15.7346 81.258C18.3275 87.5197 22.3801 93.0706 27.5544 97.4475C32.7288 101.825 38.8748 104.901 45.4798 106.419C52.0847 107.938 58.9571 107.855 65.5235 106.178C72.0899 104.5 78.16 101.277 83.2273 96.7764L53.9648 67.4964C53.0298 66.5571 52.5034 65.2868 52.4998 63.9614V22.5689Z" fill="#312E81" stroke="#312E81" strokeWidth="2"/>
            <path d="M97.8173 94.2883C96.816 94.2861 95.8562 93.888 95.1473 93.1808L66.3648 64.4008C65.84 63.8763 65.4825 63.208 65.3377 62.4804C65.1929 61.7527 65.2671 60.9984 65.5511 60.313C65.8351 59.6276 66.316 59.0418 66.933 58.6297C67.55 58.2177 68.2753 57.998 69.0173 57.9983H109.712C110.215 57.9985 110.712 58.0991 111.176 58.2943C111.639 58.4895 112.058 58.7753 112.41 59.1349C112.761 59.4945 113.037 59.9206 113.221 60.3883C113.405 60.856 113.494 61.3558 113.482 61.8583C113.148 73.5113 108.541 84.637 100.537 93.1133C100.195 93.4793 99.7824 93.7722 99.3239 93.9741C98.8654 94.1761 98.3707 94.283 97.8698 94.2883H97.8173ZM72.0348 62.9983L97.7698 88.7333C104.113 81.5957 107.868 72.5304 108.43 62.9983H72.0348ZM109.57 51.9983H67.2498C66.2556 51.997 65.3025 51.6015 64.5996 50.8985C63.8966 50.1955 63.5011 49.2425 63.4998 48.2483V8.2833C63.5015 7.77936 63.6036 7.28081 63.8002 6.8168C63.9968 6.35278 64.2839 5.9326 64.6448 5.5808C65.3769 4.88798 66.3493 4.5062 67.3573 4.5158C79.0186 4.82802 90.1553 9.42955 98.636 17.4397C107.117 25.4499 112.346 36.3062 113.322 47.9308C113.364 48.4489 113.297 48.97 113.128 49.4613C112.958 49.9526 112.688 50.4034 112.336 50.7854C111.983 51.1675 111.556 51.4725 111.08 51.6812C110.604 51.8899 110.09 51.9979 109.57 51.9983ZM68.4998 46.9983H108.205C107.05 37.084 102.436 27.8948 95.1729 21.0481C87.9101 14.2014 78.4648 10.1363 68.4998 9.5683V46.9983Z" fill="#667085" stroke="#667085" strokeWidth="2"/>
        </svg>
    );
}

const Illusion5a = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M85.2075 20.2473C83.7258 19.2101 82.0162 18.545 80.2232 18.308C78.4302 18.0711 76.6065 18.2694 74.9062 18.8861L44.3438 30.0011H18.75C12.5475 30.0011 7.5 35.0486 7.5 41.2511V56.2511C7.50825 59.0705 8.57702 61.7837 10.4939 63.8514C12.4108 65.919 15.0354 67.1898 17.8462 67.4111L13.32 87.7773C12.9568 89.3956 12.9634 91.075 13.3394 92.6904C13.7153 94.3059 14.4509 95.8156 15.4913 97.1073C16.5251 98.4045 17.839 99.4511 19.3346 100.169C20.8302 100.886 22.4687 101.256 24.1275 101.251C29.3587 101.251 33.8025 97.6848 34.9388 92.5811L40.5113 67.5011H44.3438L74.9062 78.6161C76.6062 79.2343 78.4302 79.4334 80.2235 79.1965C82.0168 78.9595 83.7265 78.2934 85.2075 77.2548C86.6879 76.2171 87.8964 74.8379 88.7306 73.2341C89.5648 71.6302 90.0003 69.8489 90 68.0411V29.4611C90 25.7936 88.2075 22.3511 85.2075 20.2473ZM18.75 60.0048C16.68 60.0048 15 58.3211 15 56.2548V41.2548C15 39.1886 16.68 37.5048 18.75 37.5048H41.25V60.0048H18.75ZM27.6112 90.9573C27.4316 91.7491 26.989 92.4567 26.3557 92.9647C25.7224 93.4727 24.9356 93.7512 24.1238 93.7548C23.5884 93.7568 23.0595 93.638 22.5764 93.4072C22.0933 93.1764 21.6685 92.8396 21.3337 92.4218C20.9989 92.0041 20.7626 91.5162 20.6425 90.9945C20.5224 90.4728 20.5215 89.9307 20.64 89.4086L25.5037 67.5048H32.82L27.6112 90.9573ZM82.5 68.0448C82.5042 68.6482 82.3609 69.2434 82.0825 69.7787C81.804 70.314 81.3989 70.7731 80.9025 71.1161C80.4106 71.4665 79.8402 71.6911 79.2414 71.7702C78.6426 71.8493 78.0336 71.7805 77.4675 71.5698L48.75 61.1261V36.3761L77.4675 25.9323C78.0339 25.7232 78.6426 25.6553 79.2411 25.7344C79.8396 25.8134 80.4099 26.0371 80.9025 26.3861C81.9187 27.0986 82.5 28.2161 82.5 29.4573V68.0448Z" fill="#312E81"/>
        </svg>

    );
}

const Illusion5b = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M85.2075 20.2473C83.7258 19.2101 82.0162 18.545 80.2232 18.308C78.4302 18.0711 76.6065 18.2694 74.9062 18.8861L44.3438 30.0011H18.75C12.5475 30.0011 7.5 35.0486 7.5 41.2511V56.2511C7.50825 59.0705 8.57702 61.7837 10.4939 63.8514C12.4108 65.919 15.0354 67.1898 17.8462 67.4111L13.32 87.7773C12.9568 89.3956 12.9634 91.075 13.3394 92.6904C13.7153 94.3059 14.4509 95.8156 15.4913 97.1073C16.5251 98.4045 17.839 99.4511 19.3346 100.169C20.8302 100.886 22.4687 101.256 24.1275 101.251C29.3587 101.251 33.8025 97.6848 34.9388 92.5811L40.5113 67.5011H44.3438L74.9062 78.6161C76.6062 79.2343 78.4302 79.4334 80.2235 79.1965C82.0168 78.9595 83.7265 78.2934 85.2075 77.2548C86.6879 76.2171 87.8964 74.8379 88.7306 73.2341C89.5648 71.6302 90.0003 69.8489 90 68.0411V29.4611C90 25.7936 88.2075 22.3511 85.2075 20.2473ZM18.75 60.0048C16.68 60.0048 15 58.3211 15 56.2548V41.2548C15 39.1886 16.68 37.5048 18.75 37.5048H41.25V60.0048H18.75ZM27.6112 90.9573C27.4316 91.7491 26.989 92.4567 26.3557 92.9647C25.7224 93.4727 24.9356 93.7512 24.1238 93.7548C23.5884 93.7568 23.0595 93.638 22.5764 93.4072C22.0933 93.1764 21.6685 92.8396 21.3337 92.4218C20.9989 92.0041 20.7626 91.5162 20.6425 90.9945C20.5224 90.4728 20.5215 89.9307 20.64 89.4086L25.5037 67.5048H32.82L27.6112 90.9573ZM82.5 68.0448C82.5042 68.6482 82.3609 69.2434 82.0825 69.7787C81.804 70.314 81.3989 70.7731 80.9025 71.1161C80.4106 71.4665 79.8402 71.6911 79.2414 71.7702C78.6426 71.8493 78.0336 71.7805 77.4675 71.5698L48.75 61.1261V36.3761L77.4675 25.9323C78.0339 25.7232 78.6426 25.6553 79.2411 25.7344C79.8396 25.8134 80.4099 26.0371 80.9025 26.3861C81.9187 27.0986 82.5 28.2161 82.5 29.4573V68.0448Z" fill="#312E81"/>
            <path d="M101.254 37.5026C100.408 37.5051 99.5855 37.2214 98.921 36.6975C98.2566 36.1737 97.7888 35.4405 97.5937 34.6172C97.3986 33.7939 97.4877 32.9288 97.8465 32.1625C98.2053 31.3962 98.8127 30.7738 99.57 30.3964L107.07 26.6464C107.96 26.2013 108.99 26.1279 109.933 26.4422C110.401 26.5979 110.833 26.844 111.205 27.1667C111.577 27.4893 111.882 27.8821 112.102 28.3226C112.323 28.7631 112.454 29.2427 112.489 29.734C112.524 30.2253 112.462 30.7187 112.307 31.186C112.151 31.6533 111.905 32.0854 111.582 32.4576C111.26 32.8297 110.867 33.1347 110.426 33.3551L102.926 37.1051C102.407 37.3651 101.834 37.4999 101.254 37.4988V37.5026Z" fill="#667085"/>
        </svg>
    );
}

const Illusion5c = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M85.2075 20.2473C83.7258 19.2101 82.0162 18.545 80.2232 18.308C78.4302 18.0711 76.6065 18.2694 74.9062 18.8861L44.3438 30.0011H18.75C12.5475 30.0011 7.5 35.0486 7.5 41.2511V56.2511C7.50825 59.0705 8.57702 61.7837 10.4939 63.8514C12.4108 65.919 15.0354 67.1898 17.8462 67.4111L13.32 87.7773C12.9568 89.3956 12.9634 91.075 13.3394 92.6904C13.7153 94.3059 14.4509 95.8156 15.4913 97.1073C16.5251 98.4045 17.839 99.4511 19.3346 100.169C20.8302 100.886 22.4687 101.256 24.1275 101.251C29.3587 101.251 33.8025 97.6848 34.9388 92.5811L40.5113 67.5011H44.3438L74.9062 78.6161C76.6062 79.2343 78.4302 79.4334 80.2235 79.1965C82.0168 78.9595 83.7265 78.2934 85.2075 77.2548C86.6879 76.2171 87.8964 74.8379 88.7306 73.2341C89.5648 71.6302 90.0003 69.8489 90 68.0411V29.4611C90 25.7936 88.2075 22.3511 85.2075 20.2473ZM18.75 60.0048C16.68 60.0048 15 58.3211 15 56.2548V41.2548C15 39.1886 16.68 37.5048 18.75 37.5048H41.25V60.0048H18.75ZM27.6112 90.9573C27.4316 91.7491 26.989 92.4567 26.3557 92.9647C25.7224 93.4727 24.9356 93.7512 24.1238 93.7548C23.5884 93.7568 23.0595 93.638 22.5764 93.4072C22.0933 93.1764 21.6685 92.8396 21.3337 92.4218C20.9989 92.0041 20.7626 91.5162 20.6425 90.9945C20.5224 90.4728 20.5215 89.9307 20.64 89.4086L25.5037 67.5048H32.82L27.6112 90.9573ZM82.5 68.0448C82.5042 68.6482 82.3609 69.2434 82.0825 69.7787C81.804 70.314 81.3989 70.7731 80.9025 71.1161C80.4106 71.4665 79.8402 71.6911 79.2414 71.7702C78.6426 71.8493 78.0336 71.7805 77.4675 71.5698L48.75 61.1261V36.3761L77.4675 25.9323C78.0339 25.7232 78.6426 25.6553 79.2411 25.7344C79.8396 25.8134 80.4099 26.0371 80.9025 26.3861C81.9187 27.0986 82.5 28.2161 82.5 29.4573V68.0448Z" fill="#312E81"/>
            <path d="M101.254 37.5026C100.408 37.5051 99.5855 37.2214 98.921 36.6975C98.2566 36.1737 97.7888 35.4405 97.5937 34.6172C97.3986 33.7939 97.4877 32.9288 97.8465 32.1625C98.2053 31.3962 98.8127 30.7738 99.57 30.3964L107.07 26.6464C107.96 26.2013 108.99 26.1279 109.933 26.4422C110.401 26.5979 110.833 26.844 111.205 27.1667C111.577 27.4893 111.882 27.8821 112.102 28.3226C112.323 28.7631 112.454 29.2427 112.489 29.734C112.524 30.2253 112.462 30.7187 112.307 31.186C112.151 31.6533 111.905 32.0854 111.582 32.4576C111.26 32.8297 110.867 33.1347 110.426 33.3551L102.926 37.1051C102.407 37.3651 101.834 37.4999 101.254 37.4988V37.5026ZM112.5 48.7526C112.5 47.758 112.105 46.8042 111.402 46.101C110.698 45.3977 109.745 45.0026 108.75 45.0026H101.25C100.255 45.0026 99.3016 45.3977 98.5983 46.101C97.8951 46.8042 97.5 47.758 97.5 48.7526C97.5 49.7472 97.8951 50.701 98.5983 51.4042C99.3016 52.1075 100.255 52.5026 101.25 52.5026H108.75C109.745 52.5026 110.698 52.1075 111.402 51.4042C112.105 50.701 112.5 49.7472 112.5 48.7526Z" fill="#667085"/>
        </svg>
    );
}

const Illusion5d = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M85.2075 20.2473C83.7258 19.2101 82.0162 18.545 80.2232 18.308C78.4302 18.0711 76.6065 18.2694 74.9062 18.8861L44.3438 30.0011H18.75C12.5475 30.0011 7.5 35.0486 7.5 41.2511V56.2511C7.50825 59.0705 8.57702 61.7837 10.4939 63.8514C12.4108 65.919 15.0354 67.1898 17.8462 67.4111L13.32 87.7773C12.9568 89.3956 12.9634 91.075 13.3394 92.6904C13.7153 94.3059 14.4509 95.8156 15.4913 97.1073C16.5251 98.4045 17.839 99.4511 19.3346 100.169C20.8302 100.886 22.4687 101.256 24.1275 101.251C29.3587 101.251 33.8025 97.6848 34.9388 92.5811L40.5113 67.5011H44.3438L74.9062 78.6161C76.6062 79.2343 78.4302 79.4334 80.2235 79.1965C82.0168 78.9595 83.7265 78.2934 85.2075 77.2548C86.6879 76.2171 87.8964 74.8379 88.7306 73.2341C89.5648 71.6302 90.0003 69.8489 90 68.0411V29.4611C90 25.7936 88.2075 22.3511 85.2075 20.2473ZM18.75 60.0048C16.68 60.0048 15 58.3211 15 56.2548V41.2548C15 39.1886 16.68 37.5048 18.75 37.5048H41.25V60.0048H18.75ZM27.6112 90.9573C27.4316 91.7491 26.989 92.4567 26.3557 92.9647C25.7224 93.4727 24.9356 93.7512 24.1238 93.7548C23.5884 93.7568 23.0595 93.638 22.5764 93.4072C22.0933 93.1764 21.6685 92.8396 21.3337 92.4218C20.9989 92.0041 20.7626 91.5162 20.6425 90.9945C20.5224 90.4728 20.5215 89.9307 20.64 89.4086L25.5037 67.5048H32.82L27.6112 90.9573ZM82.5 68.0448C82.5042 68.6482 82.3609 69.2434 82.0825 69.7787C81.804 70.314 81.3989 70.7731 80.9025 71.1161C80.4106 71.4665 79.8402 71.6911 79.2414 71.7702C78.6426 71.8493 78.0336 71.7805 77.4675 71.5698L48.75 61.1261V36.3761L77.4675 25.9323C78.0339 25.7232 78.6426 25.6553 79.2411 25.7344C79.8396 25.8134 80.4099 26.0371 80.9025 26.3861C81.9187 27.0986 82.5 28.2161 82.5 29.4573V68.0448Z" fill="#312E81"/>
            <path d="M101.254 37.5026C100.408 37.5051 99.5855 37.2214 98.921 36.6975C98.2566 36.1737 97.7888 35.4405 97.5937 34.6172C97.3986 33.7939 97.4877 32.9288 97.8465 32.1625C98.2053 31.3962 98.8127 30.7738 99.57 30.3964L107.07 26.6464C107.96 26.2013 108.99 26.1279 109.933 26.4422C110.401 26.5979 110.833 26.844 111.205 27.1667C111.577 27.4893 111.882 27.8821 112.102 28.3226C112.323 28.7631 112.454 29.2427 112.489 29.734C112.524 30.2253 112.462 30.7187 112.307 31.186C112.151 31.6533 111.905 32.0854 111.582 32.4576C111.26 32.8297 110.867 33.1347 110.426 33.3551L102.926 37.1051C102.407 37.3651 101.834 37.4999 101.254 37.4988V37.5026ZM112.5 48.7526C112.5 47.758 112.105 46.8042 111.402 46.101C110.698 45.3977 109.745 45.0026 108.75 45.0026H101.25C100.255 45.0026 99.3016 45.3977 98.5983 46.101C97.8951 46.8042 97.5 47.758 97.5 48.7526C97.5 49.7472 97.8951 50.701 98.5983 51.4042C99.3016 52.1075 100.255 52.5026 101.25 52.5026H108.75C109.745 52.5026 110.698 52.1075 111.402 51.4042C112.105 50.701 112.5 49.7472 112.5 48.7526ZM112.106 69.1789C112.327 68.7384 112.458 68.2588 112.493 67.7674C112.528 67.2761 112.466 66.7827 112.311 66.3153C112.155 65.848 111.909 65.4159 111.586 65.0438C111.263 64.6716 110.871 64.3666 110.43 64.1463L102.93 60.3963C102.046 59.9977 101.043 59.9565 100.129 60.2813C99.2156 60.6061 98.4633 61.2716 98.0295 62.1387C97.5958 63.0057 97.5143 64.0069 97.8021 64.9327C98.09 65.8584 98.7248 66.6369 99.5737 67.1051L107.074 70.8551C107.514 71.0754 107.994 71.2068 108.486 71.2416C108.977 71.2764 109.471 71.214 109.938 71.058C110.405 70.902 110.838 70.6554 111.21 70.3323C111.582 70.0092 111.886 69.616 112.106 69.1751V69.1789Z" fill="#667085"/>
        </svg>
    );
}

const Illusion6a = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M27.5 113.75H12.5C10.8424 113.75 9.25269 113.092 8.08058 111.919C6.90848 110.747 6.25 109.158 6.25 107.5V82.5C6.25 80.8424 6.90848 79.2527 8.08058 78.0806C9.25269 76.9085 10.8424 76.25 12.5 76.25H27.5C29.1576 76.25 30.7473 76.9085 31.9194 78.0806C33.0915 79.2527 33.75 80.8424 33.75 82.5V107.5C33.75 109.158 33.0915 110.747 31.9194 111.919C30.7473 113.092 29.1576 113.75 27.5 113.75ZM13.75 106.25H26.25V83.75H13.75V106.25Z" fill="#312E81"/>
        </svg>
    );
}

const Illusion6b = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M67.5 113.75H52.5C50.8424 113.75 49.2527 113.092 48.0806 111.919C46.9085 110.747 46.25 109.158 46.25 107.5V72.5C46.25 70.8424 46.9085 69.2527 48.0806 68.0806C49.2527 66.9085 50.8424 66.25 52.5 66.25H67.5C69.1576 66.25 70.7473 66.9085 71.9194 68.0806C73.0915 69.2527 73.75 70.8424 73.75 72.5V107.5C73.75 109.158 73.0915 110.747 71.9194 111.919C70.7473 113.092 69.1576 113.75 67.5 113.75ZM53.75 106.25H66.25V73.75H53.75V106.25ZM27.5 113.75H12.5C10.8424 113.75 9.25269 113.092 8.08058 111.919C6.90848 110.747 6.25 109.158 6.25 107.5V82.5C6.25 80.8424 6.90848 79.2527 8.08058 78.0806C9.25269 76.9085 10.8424 76.25 12.5 76.25H27.5C29.1576 76.25 30.7473 76.9085 31.9194 78.0806C33.0915 79.2527 33.75 80.8424 33.75 82.5V107.5C33.75 109.158 33.0915 110.747 31.9194 111.919C30.7473 113.092 29.1576 113.75 27.5 113.75ZM13.75 106.25H26.25V83.75H13.75V106.25Z" fill="#312E81"/>
        </svg>
    );
}

const Illusion6c = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M107.5 113.75H92.5003C90.8427 113.75 89.2529 113.092 88.0808 111.919C86.9087 110.747 86.2503 109.158 86.2503 107.5V52.5C86.2503 50.8424 86.9087 49.2527 88.0808 48.0806C89.2529 46.9085 90.8427 46.25 92.5003 46.25H107.5C109.158 46.25 110.748 46.9085 111.92 48.0806C113.092 49.2527 113.75 50.8424 113.75 52.5V107.5C113.75 109.158 113.092 110.747 111.92 111.919C110.748 113.092 109.158 113.75 107.5 113.75ZM93.7503 106.25H106.25V53.75H93.7503V106.25ZM67.5003 113.75H52.5002C50.8426 113.75 49.2529 113.092 48.0808 111.919C46.9087 110.747 46.2502 109.158 46.2502 107.5V72.5C46.2502 70.8424 46.9087 69.2527 48.0808 68.0806C49.2529 66.9085 50.8426 66.25 52.5002 66.25H67.5003C69.1579 66.25 70.7476 66.9085 71.9197 68.0806C73.0918 69.2527 73.7503 70.8424 73.7503 72.5V107.5C73.7503 109.158 73.0918 110.747 71.9197 111.919C70.7476 113.092 69.1579 113.75 67.5003 113.75ZM53.7502 106.25H66.2503V73.75H53.7502V106.25ZM27.5002 113.75H12.5002C10.8426 113.75 9.25293 113.092 8.08083 111.919C6.90872 110.747 6.25024 109.158 6.25024 107.5V82.5C6.25024 80.8424 6.90872 79.2527 8.08083 78.0806C9.25293 76.9085 10.8426 76.25 12.5002 76.25H27.5002C29.1579 76.25 30.7476 76.9085 31.9197 78.0806C33.0918 79.2527 33.7502 80.8424 33.7502 82.5V107.5C33.7502 109.158 33.0918 110.747 31.9197 111.919C30.7476 113.092 29.1579 113.75 27.5002 113.75ZM13.7502 106.25H26.2502V83.75H13.7502V106.25Z" fill="#312E81"/>
        </svg>
    );
}

const Illusion6d = () => {
    return (
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.9998 68.7502C14.2029 68.749 13.4265 68.4974 12.7805 68.0308C12.1345 67.5642 11.6515 66.9063 11.3998 66.1502C11.22 65.6618 11.1412 65.142 11.1679 64.6223C11.1947 64.1026 11.3266 63.5936 11.5556 63.1263C11.7846 62.6589 12.106 62.2429 12.5003 61.9032C12.8946 61.5636 13.3537 61.3074 13.8498 61.1502C29.297 56.2989 44.1424 49.7065 58.0998 41.5002C74.2877 32.0135 89.164 20.4486 102.35 7.10021C103.061 6.43781 104.001 6.07719 104.972 6.09433C105.944 6.11148 106.871 6.50504 107.558 7.1921C108.245 7.87917 108.639 8.80609 108.656 9.7776C108.673 10.7491 108.312 11.6893 107.65 12.4002C94.0088 26.2014 78.6296 38.1685 61.8998 48.0002C47.4569 56.4694 32.1114 63.2953 16.1498 68.3502L14.9998 68.7502Z" fill="#667085"/>
            <path d="M105 33.75C104.009 33.7371 103.063 33.3378 102.363 32.6373C101.662 31.9369 101.263 30.9905 101.25 30V13.75H85C84.0054 13.75 83.0516 13.3549 82.3483 12.6517C81.6451 11.9484 81.25 10.9946 81.25 10C81.25 9.00544 81.6451 8.05161 82.3483 7.34835C83.0516 6.64509 84.0054 6.25 85 6.25H105C105.991 6.26295 106.937 6.6622 107.637 7.36267C108.338 8.06314 108.737 9.00946 108.75 10V30C108.737 30.9905 108.338 31.9369 107.637 32.6373C106.937 33.3378 105.991 33.7371 105 33.75Z" fill="#667085"/>
            <path d="M107.5 113.75H92.5C90.8424 113.75 89.2527 113.092 88.0806 111.919C86.9085 110.747 86.25 109.158 86.25 107.5V52.5C86.25 50.8424 86.9085 49.2527 88.0806 48.0806C89.2527 46.9085 90.8424 46.25 92.5 46.25H107.5C109.158 46.25 110.747 46.9085 111.919 48.0806C113.092 49.2527 113.75 50.8424 113.75 52.5V107.5C113.75 109.158 113.092 110.747 111.919 111.919C110.747 113.092 109.158 113.75 107.5 113.75ZM93.75 106.25H106.25V53.75H93.75V106.25ZM67.5 113.75H52.5C50.8424 113.75 49.2527 113.092 48.0806 111.919C46.9085 110.747 46.25 109.158 46.25 107.5V72.5C46.25 70.8424 46.9085 69.2527 48.0806 68.0806C49.2527 66.9085 50.8424 66.25 52.5 66.25H67.5C69.1576 66.25 70.7473 66.9085 71.9194 68.0806C73.0915 69.2527 73.75 70.8424 73.75 72.5V107.5C73.75 109.158 73.0915 110.747 71.9194 111.919C70.7473 113.092 69.1576 113.75 67.5 113.75ZM53.75 106.25H66.25V73.75H53.75V106.25ZM27.5 113.75H12.5C10.8424 113.75 9.25269 113.092 8.08058 111.919C6.90848 110.747 6.25 109.158 6.25 107.5V82.5C6.25 80.8424 6.90848 79.2527 8.08058 78.0806C9.25269 76.9085 10.8424 76.25 12.5 76.25H27.5C29.1576 76.25 30.7473 76.9085 31.9194 78.0806C33.0915 79.2527 33.75 80.8424 33.75 82.5V107.5C33.75 109.158 33.0915 110.747 31.9194 111.919C30.7473 113.092 29.1576 113.75 27.5 113.75ZM13.75 106.25H26.25V83.75H13.75V106.25Z" fill="#312E81"/>
        </svg>
    );
}




export { IllusionBlank, Illusion1a, Illusion1b, Illusion1c, Illusion1d, Illusion2a, Illusion2b, Illusion2c, Illusion2d, Illusion3a, Illusion3b, Illusion3c, Illusion3d, Illusion4a, Illusion4b, Illusion4c, Illusion5a, Illusion5b, Illusion5c, Illusion5d, Illusion6a, Illusion6b, Illusion6c, Illusion6d};
