import React from 'react';

const TasksIcons = () => {
  return (
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.5774 8.32932C13.7203 8.47661 13.8002 8.67372 13.8002 8.87889C13.8002 9.08406 13.7203 9.28117 13.5774 9.42847L9.5238 13.5733C9.45402 13.6452 9.37051 13.7024 9.27823 13.7414C9.18595 13.7805 9.08677 13.8006 8.98657 13.8006C8.88637 13.8006 8.7872 13.7805 8.69491 13.7414C8.60263 13.7024 8.51913 13.6452 8.44935 13.5733L6.42205 11.5004C6.27922 11.3531 6.19934 11.156 6.19934 10.9508C6.19934 10.7457 6.27922 10.5486 6.42205 10.4013C6.49177 10.3291 6.57531 10.2717 6.66769 10.2325C6.76007 10.1933 6.8594 10.1731 6.95975 10.1731C7.0601 10.1731 7.15942 10.1933 7.2518 10.2325C7.34418 10.2717 7.42772 10.3291 7.49745 10.4013L8.9861 11.9251L12.502 8.32932C12.5718 8.25726 12.6554 8.19997 12.7478 8.16085C12.8401 8.12172 12.9394 8.10156 13.0397 8.10156C13.1401 8.10156 13.2394 8.12172 13.3317 8.16085C13.4241 8.19997 13.5077 8.25726 13.5774 8.32932Z" fill="#667085"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.11932 1.33928C7.26926 1.08976 7.48081 0.88296 7.73367 0.738729C7.98654 0.594497 8.27222 0.517672 8.56332 0.515625H11.4361C12.0394 0.515625 12.5752 0.842425 12.8801 1.33928L13.4178 2.21423C13.9498 2.25603 14.4533 2.29972 14.8932 2.34152C15.5099 2.3976 16.087 2.66984 16.5224 3.11017C16.9579 3.55049 17.2237 4.13055 17.2729 4.74787C17.4078 6.45597 17.5997 9.23378 17.5997 11.2934C17.5997 13.2361 17.4287 15.442 17.2957 16.8708C17.2383 17.4771 16.9705 18.0443 16.5388 18.4739C16.1071 18.9035 15.5387 19.1685 14.9321 19.223C13.567 19.3522 11.5387 19.5156 9.99972 19.5156C8.46072 19.5156 6.43247 19.3522 5.06732 19.223C4.46056 19.1687 3.89193 18.9038 3.46004 18.4742C3.02815 18.0445 2.76024 17.4773 2.70277 16.8708C2.57072 15.442 2.39972 13.2361 2.39972 11.2924C2.39972 9.23377 2.59067 6.45597 2.72652 4.74787C2.77573 4.13055 3.04155 3.55049 3.477 3.11017C3.91246 2.66984 4.48953 2.3976 5.10627 2.34152C5.54612 2.30067 6.04962 2.25603 6.58162 2.21423L7.11932 1.33928ZM6.22252 3.70952C5.87482 3.73802 5.54517 3.76842 5.24402 3.79692C4.97098 3.82152 4.7155 3.9421 4.52296 4.13725C4.33042 4.3324 4.21329 4.58948 4.19237 4.86283C4.05747 6.56998 3.87032 9.29742 3.87032 11.2924C3.87032 13.1677 4.03562 15.3204 4.16767 16.7359C4.19228 17.0025 4.30973 17.2519 4.49951 17.4406C4.6893 17.6294 4.93936 17.7454 5.20602 17.7686C6.56832 17.8968 8.53672 18.0536 9.99972 18.0536C11.4627 18.0536 13.4311 17.8968 14.7934 17.7686C15.0601 17.7454 15.3101 17.6294 15.4999 17.4406C15.6897 17.2519 15.8072 17.0025 15.8318 16.7359C15.9638 15.3204 16.1291 13.1668 16.1291 11.2924C16.1291 9.29742 15.942 6.56998 15.8061 4.86283C15.7852 4.58948 15.6681 4.3324 15.4755 4.13725C15.283 3.9421 15.0275 3.82152 14.7545 3.79692C14.4285 3.76608 14.1023 3.73695 13.776 3.70952C13.7383 3.91439 13.6667 4.11154 13.5641 4.29282C13.2867 4.78017 12.7633 5.16777 12.0897 5.16777H7.90972C7.23522 5.16777 6.71272 4.78018 6.43532 4.29378C6.33227 4.11227 6.26034 3.91479 6.22252 3.70952ZM8.56332 1.97673C8.51107 1.97673 8.43412 2.00522 8.37522 2.10022L7.72162 3.16423C7.68471 3.22576 7.66474 3.29596 7.66374 3.36771C7.66274 3.43945 7.68074 3.51019 7.71592 3.57272C7.77387 3.67722 7.84702 3.70572 7.90972 3.70572H12.0897C12.1524 3.70572 12.2256 3.67722 12.2835 3.57367C12.3187 3.51114 12.3367 3.4404 12.3357 3.36866C12.3347 3.29691 12.3147 3.22671 12.2778 3.16517L11.6242 2.10117C11.5653 2.00617 11.4884 1.97768 11.4361 1.97768L8.56332 1.97673Z" fill="#667085"/>
</svg>
  );
};

export default TasksIcons;