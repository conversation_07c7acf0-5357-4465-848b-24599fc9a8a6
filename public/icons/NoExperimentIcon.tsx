const NoExperimentIcon = () => {
  return (
    <svg
      width="486"
      height="215"
      viewBox="0 0 486 215"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g opacity="0.6" filter="url(#filter0_d_207_2046)">
        <rect
          x="91.332"
          y="22"
          width="301.875"
          height="54.4309"
          rx="7.19202"
          fill="white"
        />
      </g>
      <g filter="url(#filter1_d_207_2046)">
        <rect
          x="62.1797"
          y="55.6289"
          width="360.208"
          height="64.9484"
          rx="8.58174"
          fill="white"
          fillOpacity="0.8"
          shapeRendering="crispEdges"
        />
      </g>
      <path
        d="M86.312 94.5646C88.1532 94.5646 89.9418 93.9512 91.3943 92.8214L96.8744 98.2937C97.2838 98.6885 97.9361 98.6772 98.3314 98.2684C98.7171 97.8696 98.7171 97.2374 98.3314 96.8387L92.8514 91.3664C95.6588 87.7576 95.0051 82.5595 91.3911 79.756C87.7772 76.9526 82.5718 77.6054 79.7643 81.2142C76.9569 84.823 77.6107 90.0211 81.2246 92.8246C82.6795 93.9532 84.4697 94.5655 86.312 94.5646ZM81.9089 81.8943C84.3407 79.4659 88.2835 79.4659 90.7153 81.8942C93.1472 84.3226 93.1473 88.2598 90.7154 90.6883C88.2836 93.1167 84.3408 93.1167 81.9089 90.6883C79.4771 88.2777 79.4627 84.3547 81.8768 81.9263L81.9089 81.8943Z"
        fill="#E0E2E7"
        stroke="#E0E2E7"
        strokeWidth="1.71635"
      />
      <rect
        opacity="0.6"
        x="113.682"
        y="71.875"
        width="68.6641"
        height="10.7507"
        rx="5.37536"
        fill="#E0E2E7"
      />
      <rect
        opacity="0.6"
        x="113.457"
        y="92.6602"
        width="290.168"
        height="11.2411"
        rx="5.62053"
        fill="#E0E2E7"
      />
      <g filter="url(#filter2_d_207_2046)">
        <rect
          x="33"
          y="104.273"
          width="420"
          height="75.7293"
          rx="10.0062"
          fill="white"
        />
      </g>
      <path
        opacity="0.6"
        d="M61.1374 149.667C63.2842 149.667 65.3696 148.952 67.0633 147.635L73.453 154.015C73.9304 154.476 74.691 154.462 75.152 153.986C75.6017 153.521 75.6017 152.784 75.152 152.319L68.7623 145.938C72.0357 141.73 71.2734 135.669 67.0596 132.4C62.8458 129.132 56.7763 129.893 53.5029 134.101C50.2294 138.308 50.9917 144.369 55.2055 147.638C56.9019 148.954 58.9892 149.668 61.1374 149.667ZM56.0034 134.894C58.8388 132.062 63.4361 132.062 66.2716 134.894C69.1072 137.725 69.1072 142.316 66.2717 145.147C63.4363 147.979 58.839 147.979 56.0035 145.147C53.168 142.337 53.1511 137.762 55.966 134.931L56.0034 134.894Z"
        fill="#8382B3"
        stroke="#8382B3"
        strokeWidth="2.00125"
      />
      <rect
        opacity="0.3"
        x="92.6719"
        y="125.887"
        width="80.0618"
        height="11.1866"
        rx="5.59331"
        fill="#312E81"
      />
      <rect
        opacity="0.3"
        x="92.418"
        y="147.441"
        width="338.333"
        height="11.7584"
        rx="5.87918"
        fill="#312E81"
      />
      <defs>
        <filter
          id="filter0_d_207_2046"
          x="68.3176"
          y="0.423939"
          width="347.904"
          height="100.459"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1.4384" />
          <feGaussianBlur stdDeviation="11.5072" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_207_2046"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_207_2046"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_207_2046"
          x="34.7181"
          y="29.8837"
          width="415.131"
          height="119.872"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1.71635" />
          <feGaussianBlur stdDeviation="13.7308" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_207_2046"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_207_2046"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_207_2046"
          x="0.980026"
          y="74.2547"
          width="484.04"
          height="139.77"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2.00125" />
          <feGaussianBlur stdDeviation="16.01" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_207_2046"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_207_2046"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

const HelperCard1 = () => {
  return (
    <svg width="100%" height="100%" viewBox="0 0 336 182" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_d_266_5340)">
    <rect x="271.261" y="25.2617" width="75.74" height="75.7401" rx="8.23163" fill="white"/>
    <path opacity="0.6" d="M325.804 47.8787C322.684 46.4511 319.393 45.4331 316.011 44.8502C315.98 44.8445 315.948 44.8486 315.92 44.8619C315.892 44.8752 315.868 44.8971 315.853 44.9244C315.049 46.3508 313.504 47.2343 311.871 47.1118C310.041 46.9743 308.217 46.9731 306.406 47.1079C304.771 47.2296 303.204 46.3504 302.396 44.9244C302.381 44.8976 302.357 44.8763 302.329 44.863C302.301 44.8498 302.27 44.8453 302.239 44.8502C298.803 45.4402 295.515 46.4739 292.446 47.8787C292.419 47.89 292.397 47.9093 292.382 47.9339C286.145 57.225 284.436 66.2878 285.274 75.2382C285.278 75.282 285.302 75.3238 285.337 75.3505C289.452 78.3638 293.438 80.1931 297.35 81.4057C297.381 81.4148 297.413 81.4143 297.443 81.4045C297.473 81.3946 297.5 81.3758 297.519 81.3505C298.444 80.0904 299.269 78.7617 299.977 77.3645C300.018 77.2827 299.978 77.1855 299.893 77.1532C298.585 76.6583 297.339 76.0548 296.14 75.3697C296.045 75.3145 296.038 75.1793 296.125 75.1145C296.378 74.9261 296.626 74.7319 296.87 74.532C296.892 74.5145 296.918 74.5032 296.945 74.4995C296.973 74.4958 297.001 74.4998 297.026 74.511C304.9 78.0955 313.424 78.0955 321.205 74.511C321.23 74.499 321.258 74.4945 321.286 74.4978C321.314 74.5012 321.34 74.5124 321.362 74.5301C321.606 74.731 321.855 74.9258 322.109 75.1144C322.196 75.1792 322.191 75.3144 322.096 75.3696C320.897 76.0681 319.652 76.6582 318.341 77.1512C318.321 77.1589 318.303 77.1708 318.287 77.186C318.272 77.2013 318.26 77.2195 318.253 77.2397C318.245 77.2598 318.242 77.2813 318.243 77.3028C318.244 77.3243 318.25 77.3453 318.26 77.3644C318.982 78.7597 319.807 80.0884 320.715 81.3485C320.753 81.4018 320.822 81.4247 320.884 81.4056C324.815 80.1931 328.802 78.3637 332.917 75.3505C332.934 75.3376 332.949 75.321 332.96 75.302C332.971 75.283 332.977 75.2619 332.979 75.2401C333.983 64.8925 331.299 55.904 325.866 47.9358C325.853 47.9099 325.831 47.8897 325.804 47.8787ZM301.153 69.7883C298.782 69.7883 296.829 67.6184 296.829 64.9534C296.829 62.2884 298.744 60.1184 301.153 60.1184C303.58 60.1184 305.514 62.3074 305.476 64.9534C305.476 67.6184 303.561 69.7883 301.153 69.7883ZM317.139 69.7883C314.769 69.7883 312.815 67.6184 312.815 64.9534C312.815 62.2884 314.731 60.1184 317.139 60.1184C319.566 60.1184 321.501 62.3074 321.463 64.9534C321.463 67.6184 319.566 69.7883 317.139 69.7883Z" fill="#312E81"/>
    </g>
    <g filter="url(#filter1_d_266_5340)">
    <rect x="218.5" y="3.67969" width="33" height="33" rx="6.41667" fill="white"/>
    <path d="M241.353 14.366C240.165 13.822 238.91 13.434 237.622 13.2119C237.61 13.2097 237.598 13.2113 237.587 13.2164C237.576 13.2214 237.567 13.2298 237.562 13.2402C237.255 13.7837 236.667 14.1204 236.044 14.0737C235.347 14.0213 234.652 14.0209 233.961 14.0722C233.339 14.1186 232.741 13.7836 232.434 13.2402C232.428 13.23 232.419 13.2218 232.408 13.2168C232.397 13.2118 232.385 13.21 232.374 13.2119C231.064 13.4367 229.811 13.8306 228.642 14.366C228.632 14.3703 228.623 14.3776 228.617 14.387C226.241 17.9275 225.59 21.3809 225.909 24.7916C225.91 24.8083 225.92 24.8242 225.933 24.8344C227.501 25.9826 229.02 26.6797 230.511 27.1418C230.522 27.1452 230.535 27.1451 230.546 27.1413C230.558 27.1375 230.568 27.1304 230.575 27.1207C230.928 26.6406 231.242 26.1343 231.512 25.6018C231.527 25.5707 231.512 25.5336 231.48 25.5213C230.981 25.3327 230.506 25.1028 230.05 24.8417C230.014 24.8206 230.011 24.7691 230.044 24.7445C230.14 24.6727 230.235 24.5986 230.328 24.5225C230.336 24.5158 230.346 24.5115 230.356 24.5101C230.367 24.5087 230.378 24.5102 230.387 24.5145C233.388 25.8804 236.636 25.8804 239.601 24.5145C239.61 24.5099 239.621 24.5082 239.632 24.5094C239.642 24.5107 239.652 24.515 239.661 24.5217C239.754 24.5983 239.849 24.6725 239.945 24.7444C239.979 24.7691 239.977 24.8206 239.94 24.8416C239.484 25.1078 239.009 25.3327 238.51 25.5206C238.502 25.5235 238.495 25.528 238.489 25.5338C238.483 25.5396 238.479 25.5466 238.476 25.5543C238.473 25.5619 238.472 25.5701 238.472 25.5783C238.473 25.5865 238.475 25.5945 238.479 25.6018C238.754 26.1335 239.068 26.6398 239.414 27.12C239.429 27.1403 239.455 27.149 239.479 27.1417C240.977 26.6797 242.496 25.9826 244.064 24.8344C244.071 24.8294 244.076 24.8231 244.08 24.8159C244.084 24.8086 244.087 24.8006 244.088 24.7923C244.47 20.8492 243.447 17.4241 241.377 14.3877C241.372 14.3778 241.364 14.3701 241.353 14.366ZM231.96 22.7148C231.056 22.7148 230.312 21.888 230.312 20.8724C230.312 19.8569 231.042 19.03 231.96 19.03C232.885 19.03 233.622 19.8642 233.607 20.8724C233.607 21.888 232.877 22.7148 231.96 22.7148ZM238.051 22.7148C237.148 22.7148 236.404 21.888 236.404 20.8724C236.404 19.8569 237.134 19.03 238.051 19.03C238.976 19.03 239.714 19.8642 239.699 20.8724C239.699 21.888 238.976 22.7148 238.051 22.7148Z" fill="#E0E2E7"/>
    </g>
    <g filter="url(#filter2_d_266_5340)">
    <rect x="15" y="62.0156" width="219" height="44" rx="8.72318" fill="white"/>
    </g>
    <rect opacity="0.6" x="61.8582" y="74.793" width="78.7672" height="7.1306" rx="3.5653" fill="#312E81"/>
    <rect x="61.8582" y="87.4648" width="122.239" height="7.1306" rx="3.5653" fill="#E0E2E7"/>
    <path d="M44.5773 83.582C43.1736 84.8253 41.3633 85.5117 39.4881 85.5117C37.613 85.5117 35.8027 84.8253 34.399 83.582C32.11 84.9462 28.5573 88.8511 30.4679 91.6663C30.7599 92.0747 31.1448 92.4078 31.5908 92.6383C32.0368 92.8687 32.5312 92.9898 33.0332 92.9916H45.9439C46.4459 92.9898 46.9403 92.8687 47.3863 92.6383C47.8324 92.4078 48.2172 92.0747 48.5092 91.6663C50.4181 88.8536 46.8621 84.9377 44.5773 83.582Z" fill="#E0E2E7"/>
    <path d="M36.1627 82.8161C36.3536 82.962 36.5604 83.0855 36.7793 83.1844C36.727 83.1591 36.6822 83.1211 36.6315 83.0923C37.5405 83.5613 38.5506 83.8002 39.5733 83.7879C40.5961 83.7757 41.6002 83.5128 42.4977 83.0222C42.4345 83.0673 42.3678 83.1074 42.2984 83.1422C42.3685 83.1075 42.4335 83.0586 42.5028 83.0197C42.5535 82.991 42.6042 82.9597 42.654 82.9293C42.7317 82.8812 42.8119 82.8415 42.8896 82.7883C42.8106 82.8262 42.7351 82.8712 42.6641 82.9226C43.4838 82.4127 44.156 81.6977 44.6144 80.8482C45.0728 79.9987 45.3014 79.0442 45.2775 78.0792C45.0072 70.4173 33.9531 70.4257 33.6997 78.0792C33.6804 79.012 33.8958 79.9346 34.3262 80.7623C34.7566 81.59 35.3881 82.2962 36.1627 82.8161Z" fill="#E0E2E7"/>
    <g filter="url(#filter3_d_266_5340)">
    <rect x="81.2874" y="121.117" width="202.713" height="43.8022" rx="8.72318" fill="white"/>
    </g>
    <rect x="128.146" y="133.895" width="78.7672" height="7.1306" rx="3.5653" fill="#E0E2E7"/>
    <rect x="128.146" y="146.566" width="122.239" height="7.1306" rx="3.5653" fill="#E0E2E7"/>
    <path d="M110.865 142.547C109.461 143.79 107.651 144.477 105.776 144.477C103.901 144.477 102.09 143.79 100.687 142.547C98.3976 143.911 94.8449 147.816 96.7555 150.631C97.0475 151.04 97.4324 151.373 97.8784 151.603C98.3244 151.834 98.8188 151.955 99.3208 151.956H112.232C112.734 151.955 113.228 151.834 113.674 151.603C114.12 151.373 114.505 151.04 114.797 150.631C116.706 147.818 113.15 143.903 110.865 142.547Z" fill="#E0E2E7"/>
    <path d="M102.45 141.781C102.641 141.927 102.848 142.05 103.067 142.149C103.015 142.124 102.97 142.086 102.919 142.057C103.828 142.526 104.838 142.765 105.861 142.753C106.884 142.741 107.888 142.478 108.785 141.987C108.722 142.032 108.655 142.072 108.586 142.107C108.656 142.072 108.721 142.023 108.79 141.985C108.841 141.956 108.892 141.925 108.942 141.894C109.019 141.846 109.1 141.806 109.177 141.753C109.098 141.791 109.023 141.836 108.952 141.887C109.771 141.378 110.444 140.663 110.902 139.813C111.36 138.964 111.589 138.009 111.565 137.044C111.295 129.382 100.241 129.391 99.9873 137.044C99.968 137.977 100.183 138.899 100.614 139.727C101.044 140.555 101.676 141.261 102.45 141.781Z" fill="#E0E2E7"/>
    <defs>
    <filter id="filter0_d_266_5340" x="263.563" y="18.045" width="91.1356" height="91.1339" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="0.481113"/>
    <feGaussianBlur stdDeviation="3.84891"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5340"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5340" result="shape"/>
    </filter>
    <filter id="filter1_d_266_5340" x="215.567" y="0.929688" width="38.8667" height="38.8667" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="0.183333"/>
    <feGaussianBlur stdDeviation="1.46667"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5340"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5340" result="shape"/>
    </filter>
    <filter id="filter2_d_266_5340" x="0" y="48.2906" width="249" height="74" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.27495"/>
    <feGaussianBlur stdDeviation="7.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5340"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5340" result="shape"/>
    </filter>
    <filter id="filter3_d_266_5340" x="66.2874" y="107.392" width="232.713" height="73.8008" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.27495"/>
    <feGaussianBlur stdDeviation="7.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5340"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5340" result="shape"/>
    </filter>
    </defs>
    </svg>
  )
};

const HelperCard2 = () => {
  return (
    <svg width="100%" height="100%" viewBox="0 0 341 148" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g opacity="0.6" filter="url(#filter0_d_266_5588)">
    <rect x="58.1943" y="14" width="223.531" height="40.3602" rx="5.3263" fill="white"/>
    </g>
    <g filter="url(#filter1_d_266_5588)">
    <rect x="36.5972" y="38.9375" width="266.726" height="48.1588" rx="6.35551" fill="white" fillOpacity="0.8" shapeRendering="crispEdges"/>
    </g>
    <path d="M54.4676 67.81C55.8309 67.81 57.1553 67.3552 58.2309 66.5175L62.2887 70.5751C62.5919 70.8679 63.0749 70.8595 63.3677 70.5564C63.6532 70.2607 63.6532 69.7919 63.3677 69.4962L59.3098 65.4386C61.3886 62.7627 60.9045 58.9083 58.2285 56.8295C55.5525 54.7508 51.698 55.2349 49.6192 57.9108C47.5403 60.5867 48.0244 64.4411 50.7004 66.5198C51.7778 67.3567 53.1033 67.8107 54.4676 67.81ZM51.2071 58.4151C53.0078 56.6144 55.9274 56.6144 57.7281 58.415C59.5289 60.2157 59.5289 63.1351 57.7282 64.9357C55.9275 66.7364 53.0079 66.7364 51.2072 64.9358C49.4065 63.1483 49.3958 60.2395 51.1834 58.4388L51.2071 58.4151Z" fill="#E0E2E7" stroke="#E0E2E7" strokeWidth="1.2711"/>
    <rect opacity="0.6" x="74.7305" y="50.9844" width="50.8441" height="7.97159" rx="3.98579" fill="#E0E2E7"/>
    <rect opacity="0.6" x="74.5684" y="66.3867" width="214.862" height="8.33517" rx="4.16759" fill="#E0E2E7"/>
    <g filter="url(#filter2_d_266_5588)">
    <rect x="15" y="74.9648" width="311" height="56.1528" rx="7.41047" fill="white"/>
    </g>
    <path d="M35.8372 108.628C37.4268 108.628 38.971 108.098 40.2252 107.121L44.9566 111.852C45.31 112.194 45.8732 112.184 46.2146 111.83C46.5476 111.486 46.5476 110.939 46.2146 110.594L41.4832 105.863C43.9071 102.743 43.3426 98.2488 40.2224 95.825C37.1022 93.4012 32.6079 93.9656 30.184 97.0857C27.7601 100.206 28.3245 104.7 31.4447 107.124C32.7009 108.1 34.2465 108.629 35.8372 108.628ZM32.0355 97.6737C34.1351 95.5742 37.5393 95.5741 39.6389 97.6737C41.7386 99.7732 41.7386 103.177 39.639 105.277C37.5394 107.376 34.1352 107.376 32.0356 105.277C29.936 103.193 29.9235 99.8009 32.0078 97.7014L32.0355 97.6737Z" fill="#8382B3" stroke="#8382B3" strokeWidth="1.4821"/>
    <rect opacity="0.6" x="59.1885" y="91" width="59.2838" height="8.2948" rx="4.1474" fill="#312E81"/>
    <rect opacity="0.6" x="59" y="106.973" width="250.528" height="8.71875" rx="4.35937" fill="#312E81"/>
    <defs>
    <filter id="filter0_d_266_5588" x="43.1943" y="0.065261" width="253.531" height="70.3594" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.06526"/>
    <feGaussianBlur stdDeviation="7.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5588"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5588" result="shape"/>
    </filter>
    <filter id="filter1_d_266_5588" x="21.5972" y="25.2086" width="296.726" height="78.1602" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.2711"/>
    <feGaussianBlur stdDeviation="7.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5588"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5588" result="shape"/>
    </filter>
    <filter id="filter2_d_266_5588" x="0" y="61.4469" width="341" height="86.1523" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.4821"/>
    <feGaussianBlur stdDeviation="7.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5588"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5588" result="shape"/>
    </filter>
    </defs>
    </svg>

  );

};

const HelperCard3 = () => {
  return (
    <svg width="100%" height="100%" viewBox="0 0 331 190" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_d_266_5718)">
    <rect x="15.0001" y="71.8594" width="283.908" height="43.8557" rx="8.56341" fill="white"/>
    </g>
    <g opacity="0.8">
    <rect x="27.1182" y="82.8231" width="21.9279" height="21.9279" rx="4.03934" fill="#CCCEFA" fillOpacity="0.3"/>
    <rect x="27.1182" y="82.8231" width="21.9279" height="21.9279" rx="4.03934" stroke="#312E81" strokeWidth="1.1541"/>
    <path d="M43.8526 89.4609L35.9182 97.3954L32.3116 93.7888" stroke="#312E81" strokeWidth="2.40428" strokeLinecap="round" strokeLinejoin="round"/>
    </g>
    <rect x="58.8556" y="83.3984" width="77.3246" height="8.07869" rx="4.03934" fill="#8382B3"/>
    <rect x="58.8556" y="97.25" width="235.436" height="8.07869" rx="4.03934" fill="#E0E2E7"/>
    <g opacity="0.5">
    <g filter="url(#filter1_d_266_5718)">
    <rect x="90.7772" y="13" width="283.908" height="43.8557" rx="8.56341" fill="white"/>
    </g>
    <rect x="134.633" y="24.5391" width="77.3246" height="8.07869" rx="4.03934" fill="#E0E2E7"/>
    <rect x="134.633" y="38.3906" width="235.436" height="8.07869" rx="4.03934" fill="#E0E2E7"/>
    </g>
    <g filter="url(#filter2_d_266_5718)">
    <rect x="83.0919" y="129.566" width="283.908" height="43.8557" rx="8.56341" fill="white"/>
    </g>
    <rect x="132.718" y="141.105" width="77.3246" height="8.07869" rx="4.03934" fill="#E0E2E7"/>
    <rect x="132.718" y="154.957" width="152.341" height="8.07869" rx="4.03934" fill="#E0E2E7"/>
    <path d="M121.901 147.677C121.385 147.602 120.892 147.411 120.46 147.119C120.028 146.827 119.666 146.442 119.404 145.991C119.141 145.54 118.983 145.036 118.942 144.516C118.901 143.996 118.978 143.473 119.167 142.987C119.288 142.669 119.305 142.322 119.218 141.993C119.131 141.665 118.943 141.372 118.681 141.156C117.523 140.184 116.206 139.42 114.788 138.899C114.465 138.779 114.112 138.764 113.78 138.856C113.448 138.949 113.153 139.143 112.938 139.413C112.615 139.826 112.201 140.161 111.729 140.391C111.257 140.621 110.738 140.741 110.213 140.741C109.688 140.741 109.169 140.621 108.697 140.391C108.225 140.161 107.812 139.826 107.488 139.413C107.273 139.143 106.978 138.949 106.646 138.856C106.314 138.764 105.962 138.779 105.639 138.899C104.329 139.38 103.104 140.067 102.011 140.934C101.735 141.153 101.537 141.453 101.445 141.792C101.352 142.131 101.371 142.491 101.498 142.819C101.702 143.318 101.786 143.858 101.744 144.396C101.702 144.933 101.534 145.454 101.255 145.915C100.975 146.376 100.591 146.765 100.134 147.051C99.677 147.337 99.1592 147.512 98.6222 147.562C98.2736 147.599 97.9462 147.747 97.6881 147.984C97.4301 148.222 97.255 148.536 97.1888 148.88C97.0241 149.695 96.9411 150.525 96.9411 151.357C96.9398 152.054 96.9961 152.749 97.1092 153.437C97.1655 153.792 97.3372 154.119 97.5979 154.367C97.8586 154.615 98.1937 154.77 98.5514 154.808C99.1002 154.86 99.6286 155.042 100.092 155.34C100.556 155.637 100.942 156.042 101.218 156.519C101.493 156.996 101.65 157.533 101.676 158.083C101.702 158.634 101.595 159.183 101.365 159.683C101.215 160.009 101.177 160.376 101.259 160.726C101.341 161.075 101.537 161.387 101.816 161.612C102.967 162.567 104.271 163.318 105.674 163.833C105.854 163.895 106.042 163.928 106.232 163.931C106.492 163.93 106.748 163.867 106.979 163.747C107.21 163.627 107.409 163.453 107.559 163.24C107.874 162.781 108.297 162.405 108.79 162.146C109.284 161.887 109.833 161.753 110.39 161.754C110.93 161.755 111.462 161.881 111.945 162.124C112.427 162.367 112.846 162.719 113.168 163.152C113.383 163.44 113.687 163.649 114.032 163.747C114.378 163.845 114.746 163.825 115.08 163.692C116.362 163.175 117.556 162.462 118.619 161.577C118.886 161.356 119.076 161.057 119.162 160.721C119.247 160.386 119.225 160.032 119.097 159.71C118.889 159.217 118.798 158.683 118.832 158.149C118.865 157.616 119.023 157.097 119.291 156.634C119.56 156.172 119.932 155.778 120.379 155.484C120.826 155.19 121.335 155.004 121.866 154.941C122.21 154.893 122.531 154.737 122.78 154.495C123.03 154.254 123.197 153.939 123.255 153.596C123.397 152.858 123.474 152.109 123.485 151.357C123.485 150.565 123.411 149.774 123.264 148.995C123.204 148.66 123.041 148.352 122.796 148.116C122.551 147.879 122.238 147.725 121.901 147.677ZM114.637 151.357C114.637 152.232 114.378 153.088 113.892 153.815C113.406 154.543 112.715 155.11 111.906 155.445C111.098 155.78 110.208 155.867 109.35 155.696C108.492 155.526 107.704 155.104 107.085 154.486C106.466 153.867 106.045 153.079 105.874 152.221C105.703 151.362 105.791 150.473 106.126 149.664C106.461 148.856 107.028 148.165 107.755 147.679C108.483 147.193 109.338 146.933 110.213 146.933C111.387 146.933 112.512 147.399 113.341 148.229C114.171 149.059 114.637 150.184 114.637 151.357Z" fill="#E0E2E7"/>
    <path opacity="0.7" d="M112.946 22C115.38 22 117.325 22.6682 118.781 24.0045C120.26 25.3408 121 27.1663 121 29.481C121 31.8911 120.237 33.7047 118.709 34.9217C117.182 36.1387 115.154 36.7472 112.624 36.7472L112.481 39.5749H108.937L108.759 33.9553H109.94C112.254 33.9553 114.02 33.645 115.237 33.0246C116.478 32.4042 117.099 31.223 117.099 29.481C117.099 28.2163 116.729 27.226 115.989 26.5101C115.273 25.7942 114.271 25.4362 112.982 25.4362C111.694 25.4362 110.679 25.7823 109.94 26.4743C109.2 27.1663 108.83 28.1327 108.83 29.3736H105C105 27.9418 105.322 26.6652 105.967 25.5436C106.611 24.4221 107.53 23.5511 108.723 22.9306C109.94 22.3102 111.348 22 112.946 22ZM110.656 47.4855C109.916 47.4855 109.295 47.2349 108.794 46.7338C108.293 46.2327 108.043 45.6122 108.043 44.8725C108.043 44.1327 108.293 43.5123 108.794 43.0112C109.295 42.5101 109.916 42.2595 110.656 42.2595C111.371 42.2595 111.98 42.5101 112.481 43.0112C112.982 43.5123 113.233 44.1327 113.233 44.8725C113.233 45.6122 112.982 46.2327 112.481 46.7338C111.98 47.2349 111.371 47.4855 110.656 47.4855Z" fill="#E0E2E7"/>
    <defs>
    <filter id="filter0_d_266_5718" x="0.00012207" y="58.111" width="313.908" height="73.8555" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.2516"/>
    <feGaussianBlur stdDeviation="7.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5718"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5718" result="shape"/>
    </filter>
    <filter id="filter1_d_266_5718" x="70.7517" y="-5.77396" width="323.959" height="83.9066" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.2516"/>
    <feGaussianBlur stdDeviation="10.0128"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5718"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5718" result="shape"/>
    </filter>
    <filter id="filter2_d_266_5718" x="68.0919" y="115.818" width="313.908" height="73.8555" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="1.2516"/>
    <feGaussianBlur stdDeviation="7.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_266_5718"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_266_5718" result="shape"/>
    </filter>
    </defs>
    </svg>

  )
};


export { NoExperimentIcon, HelperCard1, HelperCard2, HelperCard3 };
