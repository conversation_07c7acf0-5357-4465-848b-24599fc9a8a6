const CalendarIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M6.66211 4.79297C6.32044 4.79297 6.03711 4.50964 6.03711 4.16797V1.66797C6.03711 1.3263 6.32044 1.04297 6.66211 1.04297C7.00378 1.04297 7.28711 1.3263 7.28711 1.66797V4.16797C7.28711 4.50964 7.00378 4.79297 6.66211 4.79297ZM13.3288 4.79297C12.9871 4.79297 12.7038 4.50964 12.7038 4.16797V1.66797C12.7038 1.3263 12.9871 1.04297 13.3288 1.04297C13.6704 1.04297 13.9538 1.3263 13.9538 1.66797V4.16797C13.9538 4.50964 13.6704 4.79297 13.3288 4.79297ZM7.07878 12.0846C6.97044 12.0846 6.86211 12.0596 6.76211 12.018C6.65378 11.9763 6.57044 11.918 6.48711 11.843C6.33711 11.6846 6.24544 11.4763 6.24544 11.2513C6.24544 11.143 6.27044 11.0346 6.31211 10.9346C6.35378 10.8346 6.41211 10.743 6.48711 10.6596C6.57044 10.5846 6.65378 10.5263 6.76211 10.4846C7.06211 10.3596 7.43711 10.4263 7.67044 10.6596C7.82044 10.818 7.91211 11.0346 7.91211 11.2513C7.91211 11.3013 7.90378 11.3596 7.89544 11.418C7.88711 11.468 7.87044 11.518 7.84544 11.568C7.82878 11.618 7.80378 11.668 7.77044 11.718C7.74544 11.7596 7.70378 11.8013 7.67044 11.843C7.51211 11.993 7.29544 12.0846 7.07878 12.0846ZM9.99544 12.0846C9.88711 12.0846 9.77878 12.0596 9.67878 12.018C9.57044 11.9763 9.48711 11.918 9.40378 11.843C9.25378 11.6846 9.16211 11.4763 9.16211 11.2513C9.16211 11.143 9.18711 11.0346 9.22878 10.9346C9.27044 10.8346 9.32878 10.743 9.40378 10.6596C9.48711 10.5846 9.57044 10.5263 9.67878 10.4846C9.97878 10.3513 10.3538 10.4263 10.5871 10.6596C10.7371 10.818 10.8288 11.0346 10.8288 11.2513C10.8288 11.3013 10.8204 11.3596 10.8121 11.418C10.8038 11.468 10.7871 11.518 10.7621 11.568C10.7454 11.618 10.7204 11.668 10.6871 11.718C10.6621 11.7596 10.6204 11.8013 10.5871 11.843C10.4288 11.993 10.2121 12.0846 9.99544 12.0846ZM12.9121 12.0846C12.8038 12.0846 12.6954 12.0596 12.5954 12.018C12.4871 11.9763 12.4038 11.918 12.3204 11.843L12.2204 11.718C12.1889 11.6715 12.1637 11.6211 12.1454 11.568C12.1214 11.5208 12.1045 11.4702 12.0954 11.418C12.0871 11.3596 12.0788 11.3013 12.0788 11.2513C12.0788 11.0346 12.1704 10.818 12.3204 10.6596C12.4038 10.5846 12.4871 10.5263 12.5954 10.4846C12.9038 10.3513 13.2704 10.4263 13.5038 10.6596C13.6538 10.818 13.7454 11.0346 13.7454 11.2513C13.7454 11.3013 13.7371 11.3596 13.7288 11.418C13.7204 11.468 13.7038 11.518 13.6788 11.568C13.6621 11.618 13.6371 11.668 13.6038 11.718C13.5788 11.7596 13.5371 11.8013 13.5038 11.843C13.3454 11.993 13.1288 12.0846 12.9121 12.0846ZM7.07878 15.0013C6.97044 15.0013 6.86211 14.9763 6.76211 14.9346C6.66211 14.893 6.57044 14.8346 6.48711 14.7596C6.33711 14.6013 6.24544 14.3846 6.24544 14.168C6.24544 14.0596 6.27044 13.9513 6.31211 13.8513C6.35378 13.743 6.41211 13.6513 6.48711 13.5763C6.79544 13.268 7.36211 13.268 7.67044 13.5763C7.82044 13.7346 7.91211 13.9513 7.91211 14.168C7.91211 14.3846 7.82044 14.6013 7.67044 14.7596C7.51211 14.9096 7.29544 15.0013 7.07878 15.0013ZM9.99544 15.0013C9.77878 15.0013 9.56211 14.9096 9.40378 14.7596C9.25378 14.6013 9.16211 14.3846 9.16211 14.168C9.16211 14.0596 9.18711 13.9513 9.22878 13.8513C9.27044 13.743 9.32878 13.6513 9.40378 13.5763C9.71211 13.268 10.2788 13.268 10.5871 13.5763C10.6621 13.6513 10.7204 13.743 10.7621 13.8513C10.8038 13.9513 10.8288 14.0596 10.8288 14.168C10.8288 14.3846 10.7371 14.6013 10.5871 14.7596C10.4288 14.9096 10.2121 15.0013 9.99544 15.0013ZM12.9121 15.0013C12.6954 15.0013 12.4788 14.9096 12.3204 14.7596C12.2433 14.6814 12.1837 14.5876 12.1454 14.4846C12.1038 14.3846 12.0788 14.2763 12.0788 14.168C12.0788 14.0596 12.1038 13.9513 12.1454 13.8513C12.1871 13.743 12.2454 13.6513 12.3204 13.5763C12.5121 13.3846 12.8038 13.293 13.0704 13.3513C13.1288 13.3596 13.1788 13.3763 13.2288 13.4013C13.2788 13.418 13.3288 13.443 13.3788 13.4763C13.4204 13.5013 13.4621 13.543 13.5038 13.5763C13.6538 13.7346 13.7454 13.9513 13.7454 14.168C13.7454 14.3846 13.6538 14.6013 13.5038 14.7596C13.3454 14.9096 13.1288 15.0013 12.9121 15.0013ZM17.0788 8.2013H2.91211C2.57044 8.2013 2.28711 7.91797 2.28711 7.5763C2.28711 7.23464 2.57044 6.9513 2.91211 6.9513H17.0788C17.4204 6.9513 17.7038 7.23464 17.7038 7.5763C17.7038 7.91797 17.4204 8.2013 17.0788 8.2013Z" fill="#667085"/>
            <path d="M13.3314 18.9596H6.66471C3.62305 18.9596 1.87305 17.2096 1.87305 14.168V7.08463C1.87305 4.04297 3.62305 2.29297 6.66471 2.29297H13.3314C16.373 2.29297 18.123 4.04297 18.123 7.08463V14.168C18.123 17.2096 16.373 18.9596 13.3314 18.9596ZM6.66471 3.54297C4.28138 3.54297 3.12305 4.7013 3.12305 7.08463V14.168C3.12305 16.5513 4.28138 17.7096 6.66471 17.7096H13.3314C15.7147 17.7096 16.873 16.5513 16.873 14.168V7.08463C16.873 4.7013 15.7147 3.54297 13.3314 3.54297H6.66471Z" fill="#667085"/>
        </svg>
    );
}

const SampleSizeIcon = () => {
        return (
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.5209 9.79532C17.3416 8.15847 17.4907 5.35554 15.854 3.53478C14.3528 1.86485 11.8401 1.58115 10.0045 2.87438C8.0034 1.46204 5.23638 1.93942 3.82413 3.9406C2.52828 5.77687 2.8108 8.29227 4.48171 9.79529C2.40557 10.8898 1.10979 13.0475 1.11919 15.3946V17.298C1.11919 17.6484 1.40324 17.9325 1.75364 17.9325H18.249C18.5995 17.9325 18.8835 17.6484 18.8835 17.298V15.3946C18.8929 13.0475 17.5971 10.8898 15.5209 9.79532ZM12.5391 3.33946C14.2885 3.33752 15.7084 4.75429 15.7103 6.50389C15.7117 7.75169 14.9806 8.88405 13.8428 9.39619C13.794 9.41839 13.7451 9.43869 13.6957 9.45965C13.5391 9.52281 13.3776 9.5731 13.2128 9.61003C13.1811 9.61702 13.1494 9.62082 13.117 9.62717C12.933 9.66245 12.7461 9.68135 12.5587 9.68364C12.4743 9.68364 12.3893 9.67729 12.3049 9.6697C12.2732 9.6697 12.2415 9.6697 12.2098 9.66335C11.8492 9.6209 11.4984 9.51749 11.1725 9.35753C11.1604 9.35181 11.1464 9.35244 11.1344 9.34738C11.0709 9.31693 11.0075 9.29028 10.9511 9.256C10.9561 9.24964 10.9593 9.24269 10.9644 9.23634C11.2553 8.8625 11.4847 8.44462 11.6438 7.99847L11.6635 7.94515C11.7359 7.73206 11.7921 7.51381 11.8316 7.29226C11.8374 7.25992 11.8418 7.2288 11.8469 7.19393C11.8836 6.96838 11.9029 6.74037 11.9046 6.51185C11.9028 6.28377 11.8835 6.05616 11.8469 5.83103C11.8418 5.79806 11.8374 5.76757 11.8316 5.7327C11.7921 5.51115 11.7359 5.2929 11.6635 5.07981L11.6438 5.02649C11.4846 4.58035 11.2553 4.16247 10.9644 3.78862C10.9593 3.78227 10.9561 3.77532 10.9511 3.76896C11.433 3.48773 11.981 3.33952 12.5391 3.33946ZM4.29137 6.51189C4.28688 4.76436 5.69976 3.34407 7.44716 3.33957C8.26627 3.33746 9.05427 3.65311 9.64542 4.22013C9.68222 4.25567 9.71839 4.29118 9.7539 4.32798C9.86283 4.44215 9.96334 4.56408 10.0546 4.6928C10.0826 4.73213 10.1079 4.77403 10.1339 4.81462C10.2226 4.95109 10.3004 5.09429 10.3667 5.2429C10.3826 5.27907 10.3947 5.31588 10.4086 5.35201C10.4755 5.51639 10.5281 5.6862 10.566 5.85958C10.5704 5.87862 10.5717 5.89765 10.5755 5.91732C10.6559 6.31129 10.6559 6.71743 10.5755 7.1114C10.5717 7.13106 10.5704 7.1501 10.566 7.16913C10.5282 7.34252 10.4755 7.51234 10.4086 7.6767C10.3947 7.71288 10.3826 7.74968 10.3667 7.78582C10.3004 7.93419 10.2225 8.07717 10.1339 8.21346C10.1079 8.25406 10.0826 8.29595 10.0546 8.33529C9.96336 8.46402 9.86285 8.58595 9.7539 8.7001C9.71836 8.7369 9.68219 8.77245 9.64542 8.80795C9.38856 9.05276 9.09238 9.25265 8.76926 9.39928C8.71824 9.42281 8.66617 9.44399 8.61321 9.46274C8.46029 9.52344 8.30291 9.57223 8.14247 9.60866C8.10251 9.61754 8.06124 9.6226 8.02065 9.62959C7.84837 9.66165 7.67371 9.67928 7.4985 9.68227H7.42873C7.25351 9.67929 7.07885 9.66167 6.90658 9.62959C6.86598 9.6226 6.82472 9.61754 6.78476 9.60866C6.62432 9.57223 6.46694 9.52345 6.31402 9.46274C6.26134 9.44181 6.20934 9.42025 6.15796 9.39928C5.02289 8.88676 4.29282 7.75734 4.29137 6.51189ZM12.5391 16.6636H2.38806V15.3946C2.37876 13.2446 3.73332 11.325 5.76198 10.6132C6.30119 10.8377 6.87949 10.9533 7.46356 10.9533C8.04763 10.9533 8.62592 10.8377 9.16513 10.6132C9.37693 10.6906 9.58337 10.7819 9.78308 10.8866C9.91504 10.9545 10.0369 11.0319 10.1637 11.11C10.2462 11.1613 10.3299 11.2115 10.4093 11.268C10.5317 11.3549 10.6472 11.4494 10.7608 11.5465C10.8337 11.61 10.906 11.6734 10.9745 11.7368C11.0792 11.8358 11.1776 11.9405 11.2721 12.0477C11.3401 12.1248 11.4057 12.2039 11.4688 12.285C11.5519 12.3911 11.6306 12.5005 11.7048 12.613C11.7683 12.7082 11.8247 12.8072 11.8805 12.9062C11.944 13.0153 12.0023 13.1245 12.0556 13.238C12.1089 13.3516 12.1559 13.476 12.2015 13.5972C12.2415 13.7025 12.284 13.8072 12.317 13.9144C12.3614 14.0616 12.3925 14.2138 12.4236 14.3661C12.4426 14.4562 12.4673 14.5444 12.4813 14.6358C12.5189 14.887 12.5382 15.1406 12.539 15.3946L12.5391 16.6636ZM17.6146 16.6636H13.808V15.3946C13.808 15.196 13.7965 14.9993 13.7788 14.8045C13.7737 14.7474 13.7648 14.691 13.7585 14.6339C13.7413 14.4936 13.7217 14.3547 13.695 14.217C13.6835 14.1579 13.6715 14.0989 13.6589 14.04C13.6284 13.8987 13.5931 13.7587 13.5529 13.6199C13.5396 13.5742 13.5275 13.5279 13.5136 13.4829C13.3359 12.915 13.0785 12.3752 12.7491 11.8796L12.7243 11.8434C12.6152 11.6814 12.4985 11.5245 12.3747 11.3733L12.3703 11.3676C12.2421 11.2088 12.1055 11.0569 11.9611 10.9126H11.9871C12.1662 10.9362 12.3465 10.9489 12.5271 10.9507H12.562C12.7291 10.9492 12.896 10.9384 13.0619 10.9184C13.1139 10.912 13.1653 10.9025 13.2173 10.8942C13.3518 10.8736 13.4851 10.8467 13.617 10.8137C13.6545 10.8041 13.6925 10.7953 13.7306 10.7845C13.9032 10.7375 14.0727 10.6799 14.2381 10.6119C16.2683 11.323 17.6241 13.2435 17.6146 15.3946V16.6636H17.6146Z" fill="#667085"/>
        </svg>
    );
}

const TargetIcon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <g clipPath="url(#clip0_1802_2449)">
                <path d="M18.75 9.38281H17.4681C17.3185 7.61803 16.5495 5.96304 15.2971 4.71067C14.0448 3.4583 12.3898 2.68929 10.625 2.53969V1.25781C10.625 1.09205 10.5592 0.933081 10.4419 0.815871C10.3247 0.698661 10.1658 0.632813 10 0.632812C9.83424 0.632813 9.67527 0.698661 9.55806 0.815871C9.44085 0.933081 9.375 1.09205 9.375 1.25781V2.53969C7.61021 2.68929 5.95522 3.4583 4.70286 4.71067C3.45049 5.96304 2.68147 7.61803 2.53188 9.38281H1.25C1.08424 9.38281 0.925268 9.44866 0.808058 9.56587C0.690848 9.68308 0.625 9.84205 0.625 10.0078C0.625 10.1736 0.690848 10.3325 0.808058 10.4498C0.925268 10.567 1.08424 10.6328 1.25 10.6328H2.53188C2.68147 12.3976 3.45049 14.0526 4.70286 15.305C5.95522 16.5573 7.61021 17.3263 9.375 17.4759V18.7578C9.375 18.9236 9.44085 19.0825 9.55806 19.1998C9.67527 19.317 9.83424 19.3828 10 19.3828C10.1658 19.3828 10.3247 19.317 10.4419 19.1998C10.5592 19.0825 10.625 18.9236 10.625 18.7578V17.4759C12.3898 17.3263 14.0448 16.5573 15.2971 15.305C16.5495 14.0526 17.3185 12.3976 17.4681 10.6328H18.75C18.9158 10.6328 19.0747 10.567 19.1919 10.4498C19.3092 10.3325 19.375 10.1736 19.375 10.0078C19.375 9.84205 19.3092 9.68308 19.1919 9.56587C19.0747 9.44866 18.9158 9.38281 18.75 9.38281ZM10.625 16.2259V15.6328C10.625 15.4671 10.5592 15.3081 10.4419 15.1909C10.3247 15.0737 10.1658 15.0078 10 15.0078C9.83424 15.0078 9.67527 15.0737 9.55806 15.1909C9.44085 15.3081 9.375 15.4671 9.375 15.6328V16.2259C7.94136 16.08 6.60199 15.4438 5.58301 14.4248C4.56403 13.4058 3.92785 12.0665 3.78188 10.6328H5C5.16576 10.6328 5.32473 10.567 5.44194 10.4498C5.55915 10.3325 5.625 10.1736 5.625 10.0078C5.625 9.84205 5.55915 9.68308 5.44194 9.56587C5.32473 9.44866 5.16576 9.38281 5 9.38281H3.78188C3.92785 7.94917 4.56403 6.6098 5.58301 5.59082C6.60199 4.57184 7.94136 3.93566 9.375 3.78969V4.38281C9.375 4.54857 9.44085 4.70754 9.55806 4.82475C9.67527 4.94196 9.83424 5.00781 10 5.00781C10.1658 5.00781 10.3247 4.94196 10.4419 4.82475C10.5592 4.70754 10.625 4.54857 10.625 4.38281V3.78969C12.0586 3.93566 13.398 4.57184 14.417 5.59082C15.436 6.6098 16.0722 7.94917 16.2181 9.38281H15.625C15.4592 9.38281 15.3003 9.44866 15.1831 9.56587C15.0658 9.68308 15 9.84205 15 10.0078C15 10.1736 15.0658 10.3325 15.1831 10.4498C15.3003 10.567 15.4592 10.6328 15.625 10.6328H16.2181C16.0722 12.0665 15.436 13.4058 14.417 14.4248C13.398 15.4438 12.0586 16.08 10.625 16.2259Z" fill="#667085"/>
                <path d="M10 6.88281C9.38194 6.88281 8.77775 7.06609 8.26384 7.40947C7.74994 7.75285 7.3494 8.24091 7.11288 8.81193C6.87635 9.38295 6.81447 10.0113 6.93505 10.6175C7.05563 11.2237 7.35325 11.7805 7.79029 12.2175C8.22733 12.6546 8.78415 12.9522 9.39034 13.0728C9.99654 13.1933 10.6249 13.1315 11.1959 12.8949C11.7669 12.6584 12.255 12.2579 12.5983 11.744C12.9417 11.2301 13.125 10.6259 13.125 10.0078C13.124 9.17932 12.7945 8.38504 12.2086 7.7992C11.6228 7.21336 10.8285 6.8838 10 6.88281ZM10 11.8828C9.62916 11.8828 9.26665 11.7728 8.95831 11.5668C8.64997 11.3608 8.40964 11.068 8.26773 10.7253C8.12581 10.3827 8.08868 10.0057 8.16103 9.64202C8.23338 9.2783 8.41195 8.94421 8.67418 8.68199C8.9364 8.41976 9.27049 8.24119 9.63421 8.16884C9.99792 8.09649 10.3749 8.13362 10.7175 8.27554C11.0601 8.41745 11.353 8.65778 11.559 8.96612C11.765 9.27446 11.875 9.63697 11.875 10.0078C11.875 10.5051 11.6775 10.982 11.3258 11.3336C10.9742 11.6853 10.4973 11.8828 10 11.8828Z" fill="#667085"/>
                <path d="M10 10.6328C10.3452 10.6328 10.625 10.353 10.625 10.0078C10.625 9.66263 10.3452 9.38281 10 9.38281C9.65482 9.38281 9.375 9.66263 9.375 10.0078C9.375 10.353 9.65482 10.6328 10 10.6328Z" fill="#667085"/>
            </g>
            <defs>
                <clipPath id="clip0_1802_2449">
                    <rect width="20" height="20" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    );
}

const RespondentsIcon = () => {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.08107 2.93883C8.73301 2.93883 8.38836 3.00739 8.06679 3.14058C7.74523 3.27378 7.45305 3.46901 7.20693 3.71513C6.96081 3.96124 6.76558 4.25342 6.63239 4.57499C6.49919 4.89655 6.43064 5.24121 6.43064 5.58927C6.43064 5.93733 6.49919 6.28198 6.63239 6.60354C6.76558 6.92511 6.96081 7.21729 7.20693 7.46341C7.45305 7.70952 7.74523 7.90475 8.06679 8.03795C8.38836 8.17114 8.73301 8.2397 9.08107 8.2397C9.78401 8.2397 10.4582 7.96046 10.9552 7.46341C11.4523 6.96635 11.7315 6.2922 11.7315 5.58927C11.7315 4.88633 11.4523 4.21218 10.9552 3.71513C10.4582 3.21807 9.78401 2.93883 9.08107 2.93883ZM4.66368 5.58927C4.66368 4.4177 5.12908 3.29412 5.9575 2.4657C6.78592 1.63728 7.9095 1.17188 9.08107 1.17188C10.2526 1.17188 11.3762 1.63728 12.2046 2.4657C13.0331 3.29412 13.4985 4.4177 13.4985 5.58927C13.4985 6.76083 13.0331 7.88441 12.2046 8.71283C11.3762 9.54125 10.2526 10.0067 9.08107 10.0067C7.9095 10.0067 6.78592 9.54125 5.9575 8.71283C5.12908 7.88441 4.66368 6.76083 4.66368 5.58927ZM15.5243 11.149C15.9242 10.749 16.4667 10.5243 17.0324 10.5243C17.598 10.5243 18.1405 10.749 18.5405 11.149C18.9404 11.549 19.1651 12.0914 19.1651 12.6571C19.1651 13.2227 18.9404 13.7652 18.5405 14.1652L14.8679 17.8387L14.844 17.8617C14.7565 17.95 14.6461 18.0604 14.5153 18.1514C14.4014 18.231 14.2786 18.2963 14.1496 18.3476C14.0012 18.4059 13.8483 18.4359 13.7255 18.4598L13.6937 18.4668L11.9047 18.8247C11.762 18.8532 11.6145 18.8462 11.4752 18.804C11.3359 18.7619 11.2092 18.686 11.1064 18.5831C11.0035 18.4802 10.9276 18.3535 10.8854 18.2143C10.8433 18.075 10.8362 17.9275 10.8648 17.7848L11.2226 15.9958L11.2297 15.9631C11.2535 15.8411 11.2836 15.6883 11.3419 15.539C11.3931 15.4109 11.4585 15.2881 11.538 15.1741C11.629 15.0434 11.7403 14.9329 11.8278 14.8455L11.8508 14.8216L15.5243 11.149ZM17.2912 12.3982C17.2573 12.3642 17.2169 12.3372 17.1725 12.3188C17.1281 12.3004 17.0805 12.2909 17.0324 12.2909C16.9843 12.2909 16.9367 12.3004 16.8923 12.3188C16.8478 12.3372 16.8075 12.3642 16.7735 12.3982L13.1009 16.0717C13.0632 16.1086 13.0261 16.146 12.9896 16.1839L12.9887 16.1857L12.9878 16.1875C12.9765 16.2388 12.9658 16.2904 12.956 16.3421L12.8579 16.8315L13.3474 16.7343C13.3991 16.7242 13.4506 16.7133 13.502 16.7017H13.5038L13.5055 16.6999C13.5276 16.6787 13.5585 16.6495 13.6177 16.5886L17.2912 12.9159C17.3253 12.882 17.3523 12.8416 17.3707 12.7972C17.3891 12.7528 17.3986 12.7052 17.3986 12.6571C17.3986 12.609 17.3891 12.5614 17.3707 12.517C17.3523 12.4726 17.3253 12.4322 17.2912 12.3982ZM3.85353 17.0745C3.97865 16.3341 4.2596 15.6287 4.67778 15.005C5.09596 14.3813 5.64184 13.8535 6.27928 13.4566C6.91673 13.0597 7.6312 12.8028 8.37541 12.7027C9.11962 12.6026 9.87659 12.6618 10.5962 12.8762C10.7077 12.9102 10.8248 12.9219 10.9408 12.9104C11.0568 12.899 11.1694 12.8647 11.2721 12.8096C11.3748 12.7544 11.4656 12.6795 11.5392 12.5892C11.6128 12.4988 11.6678 12.3947 11.701 12.283C11.7342 12.1713 11.745 12.0541 11.7327 11.9382C11.7205 11.8223 11.6854 11.7099 11.6295 11.6076C11.5736 11.5053 11.498 11.4151 11.4071 11.3422C11.3162 11.2692 11.2118 11.215 11.0998 11.1826C10.0448 10.8681 8.93084 10.8045 7.84684 10.9967C6.76284 11.189 5.73874 11.6318 4.85617 12.2899C3.9736 12.948 3.25696 13.8031 2.76338 14.7872C2.2698 15.7713 2.01293 16.857 2.01324 17.958C2.01324 18.1923 2.10633 18.417 2.27201 18.5827C2.43769 18.7484 2.66241 18.8414 2.89672 18.8414H8.19759C8.4319 18.8414 8.65662 18.7484 8.8223 18.5827C8.98799 18.417 9.08107 18.1923 9.08107 17.958C9.08107 17.7236 8.98799 17.4989 8.8223 17.3332C8.65662 17.1676 8.4319 17.0745 8.19759 17.0745H3.85353Z" fill="#667085" stroke="white" strokeWidth="0.318052"/>
        </svg>
    );
}


export { CalendarIcon, RespondentsIcon, TargetIcon, SampleSizeIcon };