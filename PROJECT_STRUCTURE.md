  

# Project Name: Holodeck

  

This README provides an overview of the project structure, folder organization, and functionality within a **Next.js** front-end application. Use this guide to understand the architecture of the codebase and locate specific features or components.

  

---

  

## **Table of Contents**

1. [Technologies Used](#technologies-used)
  

2. [Project Structure](#project-structure)

	- [Overview](#overview)

	- [Folder Details](#folder-details)

3. [Scripts](#scripts)


  

---
## **Technologies Used**

- [Next.js](https://nextjs.org/) - React Framework

- [React](https://reactjs.org/) - UI Library

- [Tailwind CSS](https://tailwindcss.com/) - Styling

- [Jest](https://jestjs.io/) - Testing Framework

- [PlayWright](https://playwright.dev/) - End-to-End Testing Framework

  
---

## **Project Structure**

  

### Overview

The folder structure is organized for scalability and readability:

  

```

project-root/

├── public/

├── src/app

├── tests/

├── next.config.js/

└── package.json

```

### Folder Details


#### **1. public/**

- The **public/** folder contains static assets that are accessible directly from the root URL (`/`).

- It includes:

	-   **Icons:** Commonly used icons for the project, such as app logos, favicons, or SVG 		assets.
	-   **Constant Data:** Static JSON files or other data resources that do not change dynamically.

  

Example:

```

public/
├── data/
│ ├── countries.json 
├── icons/
│ ├── HomeIcons.tsx 
│ ├── favicon.ico  
│ ├── clock.svg
│ ├── ../

```
#### **2. src/app**

- Contains pages, layouts used throughout the application.

- Structure by feature or UI elements.

  

Example:

```
src/app/
├── components/
├── experiments/
	├── page.tsx
├── ideation/
	├── page.tsx
├── insights/
	├── page.tsx
├── settings/
	├── page.tsx
├── showcase/
	├── page.tsx
└── page.tsx
└── layout.tsx

```

#### **3. tests**

- Contains end to end test scripts. Built using playwright library

  

Example:

```
tests/
├── experiments/
	├── create.e2e.ts
	├── replicate.e2e.ts
└── config.ts
└── saveAuthState.ts  #saves the user authentication

```

  

#### **5. src/app/components/**

- Contains reusable components used across the application at different pages.
- Components are structured according to UI pages as folders.

Example:

```

components/

├── _insights/
	├── Tooltip.tsx 				#reusable tooltip component.
	├── ExperimentOverviewCard.tsx 	#Experiment overview component used in experiments page.

├── _settings/
	├── ApiAccessComponent.tsx 		#Component that handles access token generation.
	
├── _insights/
	├── ShowcaseOverViewCard.tsx 	
	├── AmceChart.tsx	

├── _ui/
	├── Loading.tsx 	
	├── Button.tsx
	├── Paginator.tsx	
	├── NotificationCenter.tsx	

├── _graph/
	├── CausalGraph.tsx

├── _experiments/
	├── ConfidenceIndicator.tsx 	
	├── FeatureImportance.tsx

├── _ideation/
	├── _what 			#conatins components used in what UI feature.
		├── AddNewLevelCard.tsx
		├── AttributeCard.tsx
		├── AddNewAttributeCard.tsx
		├── LevelCard.tsx
	├── _whenWhere		#conatins components used in when/where UI feature.
		├── CountrySelector.tsx
		├── LLMModelSelector.tsx
		├── LocationMarkt.tsx
	├── _who			#conatins components used in who UI feature.
		├── PopulationTraitValue.tsx
		├── SpecialistCard.tsx
		├── TraitCard.tsx
	├── _why			#conatins components used in why UI feature.
		├── FocusCard.tsx
		├── CausalCarouselModel.tsx
		├── SampleQuestion.tsx
	├── WhatComponent.tsx		#parent component that utilizes other What components.
	├── WhenWhereComponent.tsx
	├── WhyComponent.tsx
	├── WhoComponent.tsx
	
	


```

  

## **Scripts**

  

The following commands in `package.json` for quick development and deployment:

```json

"scripts": {

"dev": "npm install && next dev",

"build": "next build",

"start": "next start",

"lint": "next lint",

"test": "jest",

"test:playwright": "npx ts-node tests/saveAuthState.ts && npx playwright test"

}

```

  

-  `npm run dev`: Starts the development server.

-  `npm run build`: Creates a production build.

-  `npm run start`: Runs the app in production mode.

-  `npm run lint`: Lints the codebase using ESLint.

-  `npm run test`: Runs the Unit & Integration test suite.

-  `npm run test:playwright` : Runs the End-to-End test cases.

 

---

  

---