# Subconscious AI Frontend (Holodeck)

Welcome to the Subconscious AI frontend repository. Our frontend is built using Next.js and is deployed in a Docker container on Google Cloud Platform (GCP) via GitHub Actions for continuous integration and deployment.

## Table of Contents

- [Subconscious AI Frontend (Holodeck)](#subconscious-ai-frontend-holodeck)
  - [Table of Contents](#table-of-contents)
  - [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Local Development](#local-development)
    - [Initial Setup](#initial-setup)
    - [Using Docker (Recommended)](#using-docker-recommended)
    - [Using npm (Alternative)](#using-npm-alternative)
  - [End to End Tests (Playwright)](#end-to-end-tests-playwright)
  - [Development Workflow](#development-workflow)
  - [Testing Production Build](#testing-production-build)
  - [Deploying to production](#deploying-to-production)
  - [How to Create a Release:](#how-to-create-a-release)

## Getting Started

This project is structured to support both Docker-based and traditional Node.js/npm setups. Follow these instructions to set up your development environment.

## Prerequisites

Before beginning, ensure you have the following installed on your machine:

- Docker
- Node.js and npm (for alternative development setups)

## Local Development

### Initial Setup

1. Clone the repository
```sh
<NAME_EMAIL>:Subconscious-ai/holodeck.git
```
2. Open the repo in your favorite code editor (Ideally VSCode):

3. Set up your environment variables:
3.1. Rename the `.env.local.example` file to `.env.local` in the root directory of the project.

```sh
mv .env.local.example .env.local
```

3.2. Edit the `.env.local` file to include your Doppler token and any other necessary environment variables specific to project.

### Using Docker (Recommended)

For a quick and easy setup, you can use Docker to build and run your application in a container. This method simulates the production environment closely and is recommended for consistent development.

1. To build and start your application, run:

```sh
docker-compose up
```

If the above command fails, you may try running the following command:

```sh
docker compose up
```

### Using npm (Alternative)

If you prefer not to use Docker or want a more traditional Node.js development environment, you can build and run your application using npm:

1. Install the dependencies:

```sh
npm install
```

2. Start the development server:

```sh
npm run dev
```

3. Start the development server with linting & formatting:

```sh
npm run dev:with-lint-format
```

# End to End Tests (Playwright)

This project includes end-to-end (E2E) tests built using Playwright, a powerful testing framework for web applications. The tests are designed to ensure that the application functions correctly from the user's perspective.     
1. Installation:
- Clone the repository and run `npm install` to install the libraries needed to run tests.
2.  Running the Tests:
- If you wanted to run the tests in headed mode(To open the browser and simulate the user flow).
	 - Change the following property in Playwright.config.ts:
		- `headless:true`
- Run the command ` npm run test:playwright ` to run the E2E Tests. 
3. Reporting:
- Yet to update this section


## Development Workflow

Follow these steps to manage your development process from ticket to deployment:

1. **Starting with Tickets**: Pick a ticket from the tasks list from GitHub and move it to "In Progress".
2. **Create a separate branch**: Use a separate branch for each feature.Use the ticket UI on GitHub to create a branch from `main` and it will give you a command to checkout to newly created branch:
   
   ```sh
   git fetch origin &&
   git checkout <ticket_number>-description
   ```

3. **Writing code**: development the feature based on task description.
4. **Formatting Code**: Ensure your code adheres to style guidelines:
   
   ```sh
   npm run format
   ```

5. **Linting Code**: Check for and resolve linting errors:

   ```sh
   npm run lint
   ```

6. **Writing Tests**: Write and run tests:

   ```sh
   npm test
   ```

7. **Committing Changes**: Stage and commit your changes:

   ```sh
   git add .
   git commit -m "Feature <ticket_number>: Add a concise description"
   ```

8. **Pushing Changes** and **Creating Pull Requests**: Do the following steps to push:
- Push your changes to remote branch.
- Open a pull request and write a comprehensive description for the PR.
- Assign yourself and add reviewer(s). 

9. **Review and Merge**: After the reviewer reviewed your code, address review comments and merge your changes into `main`.

10. **Deploying**: Merged changes will trigger an automatic deployment via CI/CD.

11. **Closing Tickets and Deleting Branch**: Mark the ticket as "Done" after verifying the live deployment.
If the branch still exists, delete the branch.

## Testing Production Build

Test the Docker production build locally:

```sh
docker build -t app:latest .
docker run --rm -p 3000:3000 app:latest
```

## Deploying to production

Subconscious AI frontend (holodeck) uses GitHub Actions and Google Cloud Platform (GCP) Cloud Build pipeline for continuous integration and deployment. Releases tagged in the format `vX.X.X` will automatically be deployed to production.

## How to Create a Release:

- Navigate to the main page of the [repository](https://github.com/Subconscious-ai/holodeck).
- To the right of the list of files, click Releases.
- At the top of the page, click Draft a new release.
- select the Choose a tag dropdown menu and create a new tag (e.g. v1.2.2).
- If you're ready to publicize your release, click Publish release. To work on the release later, click Save draft. You can then view your published or draft releases in the releases feed for your repository. For more information, see [Viewing your repository's releases and tags](https://docs.github.com/en/repositories/releasing-projects-on-github/viewing-your-repositorys-releases-and-tags).
