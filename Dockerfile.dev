# Use an official Node runtime as a parent image - match production version
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /app

# Install system dependencies needed for npm packages
RUN apk add --no-cache libc6-compat python3 make g++

# Copy package.json and package-lock.json for better Docker layer caching
COPY package*.json ./

# Accept Doppler token for development (consistent with production naming)
ARG DOPPLER_TOKEN_ARG
ENV DOPPLER_TOKEN=$DOPPLER_TOKEN_ARG

# Install all dependencies (including dev dependencies)
RUN npm ci --ignore-scripts

# Copy the current directory contents into the container at /app
COPY . .

# Run the code formatting linting script
RUN npm run format
RUN npm run lint

RUN npm run test

# Expose the port that the app runs on
EXPOSE 3000

# Start the Next.js app in development mode
CMD ["npm", "run", "dev"]