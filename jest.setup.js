import "@testing-library/jest-dom";
import "resize-observer-polyfill";

// Polyfill for structuredClone
if (typeof structuredClone !== "function") {
  global.structuredClone = (obj) => JSON.parse(JSON.stringify(obj));
}

// Mock fetch for Auth0 token
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({ token: "mock-auth0-token" }),
  })
);

// Mock js-cookie
jest.mock("js-cookie", () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}));

// Mock react-vega and vega
jest.mock("react-vega", () => ({
  Vega: () => "Mocked Vega Component",
}));

jest.mock("vega", () => ({
  View: jest.fn(),
}));

jest.mock("vega-lite", () => ({}));

// Mock Auth0
jest.mock("@auth0/nextjs-auth0", () => ({
  useAuth0: () => ({
    isAuthenticated: true,
    user: { sub: "mock-auth0-user-id" },
    getAccessTokenSilently: jest.fn().mockResolvedValue("mock-token"),
  }),
}));
