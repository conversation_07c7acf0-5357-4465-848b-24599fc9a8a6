{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "jest.setup.js", "src/app/_components/_util/AmceChart.jsx", "src/app/_components/_ui/AmceChartDRAFT.jsx", "tests/saveAuthState.ts", "src/app/_components/_ideation/_who/PopulationTraits.tsx"], "exclude": ["node_modules"]}