{"extends": ["next/core-web-vitals", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es2021": true, "node": true, "jest": true}, "rules": {"react-hooks/exhaustive-deps": "warn", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "warn"}}