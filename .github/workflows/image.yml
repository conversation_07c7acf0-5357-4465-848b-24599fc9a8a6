name: Publish Image
on:
  workflow_dispatch:
    inputs:
      environment:
        description: The environment to deploy to
        type: choice
        options:
          - dev01
          - test
          - prod
        default: dev01
        required: true
  workflow_call:
    outputs:
      image:
        description: The URI of the image
        value: ${{ jobs.build_image.outputs.image }}
    secrets:
      DUPLO_TOKEN:
        description: The token to use for Duplo API calls
        required: true

env:
  DUPLO_HOST: ${{ vars.DUPLO_HOST }}
  DUPLO_TENANT: ${{ vars.DUPLO_TENANT }}
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN }}

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build_image:
    environment: 
      name: ${{ inputs.environment }}
    name: Build and Push Image
    runs-on: ubuntu-latest
    outputs:
      image: ${{ steps.build_image.outputs.uri }}
    steps:

    - name: Checkout code
      uses: actions/checkout@v3

    - name: Duplo Setup
      uses: duplocloud/actions@main

    - name: Build and Push Docker Image
      id: build_image
      uses: duplocloud/actions/build-image@main
      with:
        build-args: >
          --build-arg DOPPLER_TOKEN_ARG=${{ secrets.DOPPLER_TOKEN }}
        push: true
