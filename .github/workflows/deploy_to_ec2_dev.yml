name: Deploy to EC2 Dev

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-24.04

    steps:
    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Deploy via SSH
      uses: appleboy/ssh-action@v1
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ubuntu
        key: ${{ secrets.EC2_KEY }}
        port: 22
        script: |
          cd /home/<USER>/holodeck
          git checkout develop
          git pull origin develop
          sudo systemctl restart holodeck