FROM node:20-alpine AS base

# Set environment variables
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat curl gnupg python3 make g++
WORKDIR /app

# Install the Doppler CLI in the container
# Install Doppler CLI
RUN wget -q -t3 'https://packages.doppler.com/public/cli/rsa.8004D9FF50437357.key' -O /etc/apk/keys/<EMAIL> && \
    echo 'https://packages.doppler.com/public/cli/alpine/any-version/main' | tee -a /etc/apk/repositories && \
    apk add doppler

# Copy package files
COPY package*.json ./

# Define build argument for Doppler token (CloudBuild uses DOPPLER_TOKEN_ARG)
ARG DOPPLER_TOKEN_ARG
ENV DOPPLER_TOKEN=$DOPPLER_TOKEN_ARG

# Install production dependencies only
RUN doppler run -- npm ci --only=production --ignore-scripts

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

COPY --from=deps /usr/bin/doppler /usr/bin/doppler

# Copy package files and install all dependencies (including dev deps for build)
COPY package*.json ./

# Set Doppler token for builder stage (CloudBuild uses DOPPLER_TOKEN_ARG)
ARG DOPPLER_TOKEN_ARG
ENV DOPPLER_TOKEN=$DOPPLER_TOKEN_ARG

# Install all dependencies (including dev dependencies for build)
RUN npm ci --ignore-scripts

# Copy source code
COPY . .

# Run the code formatting linting script
RUN npm run format
RUN npm run lint
RUN npm run test

# Build the application using the environment variables fetched from Doppler
RUN doppler run -- npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

# Create non-root user (optimized - combine commands and use faster method)
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -u 1001 -G nodejs nextjs

# Install only Doppler in runner stage
COPY --from=deps /usr/bin/doppler /usr/bin/doppler

# Set Doppler token for runtime (CloudBuild uses DOPPLER_TOKEN_ARG)
ARG DOPPLER_TOKEN_ARG
ENV DOPPLER_TOKEN=$DOPPLER_TOKEN_ARG

# Copy necessary files
COPY package*.json ./

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next

# Copy only production node_modules (smaller size)
COPY --from=deps /app/node_modules ./node_modules

USER nextjs

EXPOSE 3000

CMD ["doppler", "run", "--", "npm", "run", "start"]