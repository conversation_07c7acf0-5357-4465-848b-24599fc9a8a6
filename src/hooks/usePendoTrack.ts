import { useCallback } from "react";

type FeatureName = "Collab" | "Chat Agent";
type ActionType =
  | "clicked"
  | "started"
  | "ended"
  | "opened"
  | "closed"
  | "message_sent"
  | string;
type AdditionalProps = Record<string, string | number | boolean | null>;

export function usePendoTrack() {
  const trackFeatureUsage = useCallback(
    (
      featureName: FeatureName,
      action: ActionType,
      additionalProps: AdditionalProps = {}
    ) => {
      if (window.pendo) {
        window.pendo.track(`${featureName.replace(/\s+/g, "_")}_${action}`, {
          feature: featureName,
          action: action,
          timestamp: new Date().toISOString(),
          ...additionalProps,
        });
      } else {
        console.warn("Pendo is not initialized");
      }
    },
    []
  );

  return { trackFeatureUsage };
}
