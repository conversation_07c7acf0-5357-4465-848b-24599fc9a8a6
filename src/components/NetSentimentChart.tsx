import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Too<PERSON>ip<PERSON>rovider,
} from "@/components/ui/tooltip";
import { Info } from "lucide-react";

interface SurveyResult {
  statement: string;
  responses: Record<string, number>;
  net_sentiment: number;
  avg_score: number;
}

interface ConceptStatement {
  labels: string[];
  statement: string;
}

interface NetSentimentChartProps {
  data: SurveyResult[];
  conceptStatements: ConceptStatement[];
}

export const NetSentimentChart: React.FC<NetSentimentChartProps> = ({
  data,
  conceptStatements,
}) => {
  // Group statements by their label sets
  const groupStatementsByLabels = () => {
    const groups: Array<{
      labels: string[];
      statements: Array<{
        conceptStatement: ConceptStatement;
        surveyResult: SurveyResult;
      }>;
    }> = [];

    conceptStatements.forEach((conceptStatement) => {
      const matchingSurveyResult = data.find(
        (result) => result.statement === conceptStatement.statement
      );

      if (matchingSurveyResult) {
        const labelsKey = conceptStatement.labels.join("|");
        let existingGroup = groups.find(
          (group) => group.labels.join("|") === labelsKey
        );

        if (!existingGroup) {
          existingGroup = {
            labels: conceptStatement.labels,
            statements: [],
          };
          groups.push(existingGroup);
        }

        existingGroup.statements.push({
          conceptStatement,
          surveyResult: matchingSurveyResult,
        });
      }
    });

    return groups;
  };

  const statementGroups = groupStatementsByLabels();

  // Dynamic sentiment color calculation based on scale context
  const getSentimentColor = (sentiment: number, scaleSize: number) => {
    // Adjust thresholds based on scale size
    const highThreshold = scaleSize <= 3 ? 30 : scaleSize <= 5 ? 40 : 50;
    const moderateThreshold = scaleSize <= 3 ? 10 : scaleSize <= 5 ? 15 : 20;

    if (sentiment >= highThreshold) return "text-green-600"; // High positive
    if (sentiment >= moderateThreshold) return "text-green-500"; // Moderate positive
    if (sentiment >= 0) return "text-green-400"; // Low positive
    return "text-red-500"; // Negative
  };

  const formatSentiment = (sentiment: number) => {
    return sentiment > 0
      ? `+${sentiment.toFixed(0)}%`
      : `${sentiment.toFixed(0)}%`;
  };

  // Dynamic grid layout based on number of statements
  const getGridLayout = (numStatements: number) => {
    if (numStatements <= 2) return "grid-cols-1 md:grid-cols-2";
    if (numStatements <= 3) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
    if (numStatements <= 4) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";
    return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"; // For 5+ statements, use 3 columns max
  };

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 lg:p-8">
        <div className="flex items-center justify-center mb-6 gap-2">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 text-center">
            NET SENTIMENT
          </h2>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="ml-1 cursor-pointer align-middle">
                  <Info
                    className="w-5 h-5 text-gray-400 hover:text-gray-600"
                    aria-label="Info"
                  />
                </span>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="bg-white text-gray-800 shadow-lg border border-gray-200 rounded-lg p-4 min-w-[320px] max-w-md"
              >
                <div className="flex flex-col gap-2">
                  <div className="font-bold text-base mb-1">
                    Net Statement Score
                  </div>
                  <div className="text-sm mb-2 text-gray-700">
                    = <span className="font-semibold">% agreeing (top 2)</span>{" "}
                    −{" "}
                    <span className="font-semibold">
                      % disagreeing (bottom 2)
                    </span>
                  </div>
                  <div className="flex items-center gap-2 mb-1">
                    <span className="inline-block w-3 h-3 rounded-full bg-green-500"></span>
                    <span className="text-sm font-medium text-green-700">
                      Positive Score:
                    </span>
                    <span className="text-sm">
                      More people agree than disagree
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="inline-block w-3 h-3 rounded-full bg-red-500"></span>
                    <span className="text-sm font-medium text-red-700">
                      Negative Score:
                    </span>
                    <span className="text-sm">
                      More people disagree than agree
                    </span>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {statementGroups.map((group, groupIndex) => {
          const allStatementsInGroup = group.statements.map(
            (s) => s.surveyResult
          );

          return (
            <div key={groupIndex}>
              {groupIndex > 0 && (
                <div className="border-t border-gray-200 my-6"></div>
              )}

              <div
                className={`grid ${getGridLayout(allStatementsInGroup.length)} gap-4 md:gap-6 mb-6`}
              >
                {group.statements.map((statementData, index) => {
                  const item = statementData.surveyResult;

                  return (
                    <div
                      key={index}
                      className="bg-gray-50 rounded-lg border border-gray-200 p-4 md:p-6"
                    >
                      <div className="text-center">
                        <h3 className="text-sm md:text-base font-semibold text-gray-700 mb-3 md:mb-4 leading-tight">
                          {item.statement}
                        </h3>

                        <div
                          className={`text-3xl md:text-4xl lg:text-5xl font-bold mb-2 ${getSentimentColor(
                            item.net_sentiment,
                            group.labels.length
                          )}`}
                        >
                          {formatSentiment(item.net_sentiment)}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
