import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Info } from "lucide-react";

interface SurveyResult {
  statement: string;
  responses: Record<string, number>;
  net_sentiment: number;
  avg_score: number;
}

interface ConceptStatement {
  labels: string[];
  statement: string;
}

interface SurveyResultsChartProps {
  data: SurveyResult[];
  conceptStatements: ConceptStatement[];
}

export const SurveyResultsChart: React.FC<SurveyResultsChartProps> = ({
  data,
  conceptStatements,
}) => {
  const getPercentage = (value: number, total: number) => {
    return (value / total) * 100;
  };

  const generateColors = (numCategories: number): string[] => {
    const baseColors = [
      "#EF4444", // [1] Strongly Disagree - red-500
      "#FB923C", // [2] Disagree - orange-400
      "#D1D5DB", // [3] Neutral - gray-300
      "#2CA58D", // [4] Agree - teal green (better contrast than emerald-500)
      "#91E6A9", // [5] Strongly Agree - pastel mint green (high contrast)
    ];

    if (numCategories <= baseColors.length) {
      return baseColors.slice(0, numCategories);
    }

    // If more categories than base colors, generate additional ones dynamically
    const additionalColors = [];
    for (let i = baseColors.length; i < numCategories; i++) {
      additionalColors.push(`hsl(${(i * 360) / numCategories}, 70%, 50%)`);
    }
    return [...baseColors, ...additionalColors];
  };

  // Group statements by their label sets
  const groupStatementsByLabels = () => {
    const groups: Array<{
      labels: string[];
      statements: Array<{
        conceptStatement: ConceptStatement;
        surveyResult: SurveyResult;
      }>;
    }> = [];

    conceptStatements.forEach((conceptStatement) => {
      const matchingSurveyResult = data.find(
        (result) => result.statement === conceptStatement.statement
      );

      if (matchingSurveyResult) {
        const labelsKey = conceptStatement.labels.join("|");
        let existingGroup = groups.find(
          (group) => group.labels.join("|") === labelsKey
        );

        if (!existingGroup) {
          existingGroup = {
            labels: conceptStatement.labels,
            statements: [],
          };
          groups.push(existingGroup);
        }

        existingGroup.statements.push({
          conceptStatement,
          surveyResult: matchingSurveyResult,
        });
      }
    });

    return groups;
  };

  const statementGroups = groupStatementsByLabels();

  // Create color maps for each unique label set
  const createColorMaps = () => {
    const colorMaps: Record<string, Record<string, string>> = {};

    statementGroups.forEach((group) => {
      const labelsKey = group.labels.join("|");
      const colors = generateColors(group.labels.length);
      colorMaps[labelsKey] = group.labels.reduce(
        (acc, label, index) => {
          acc[label] = colors[index];
          return acc;
        },
        {} as Record<string, string>
      );
    });

    return colorMaps;
  };

  const colorMaps = createColorMaps();

  return (
    <div className="w-full">
      <div className="bg-white border-blue-600 rounded-lg shadow-lg p-4 md:p-6 lg:p-8">
        <div className="flex items-center justify-center mb-6 gap-2">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 text-center">
            STATEMENT RESPONSES
          </h2>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="ml-1 cursor-pointer align-middle">
                  <Info className="w-5 h-5 text-gray-400 hover:text-gray-600" />
                </span>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="bg-white text-gray-800 shadow-lg border border-gray-200 rounded-lg p-4 min-w-[320px] max-w-md"
              >
                <div className="flex flex-col gap-2">
                  <div className="text-sm mb-2 text-gray-700">
                    Shows the percentage of respondents who selected each option
                    on the Likert scale
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="space-y-6">
          <div className="hidden md:block">
            <div className="flex items-center gap-4">
              <div className="flex-1 flex justify-between text-sm text-gray-600">
                <span>0%</span>
                <span>20%</span>
                <span>40%</span>
                <span>60%</span>
                <span>80%</span>
                <span>100%</span>
              </div>
              <div className="flex-shrink-0 w-20"></div>
            </div>
          </div>

          {statementGroups.map((group, groupIndex) => {
            const labelsKey = group.labels.join("|");
            const colorMap = colorMaps[labelsKey];

            // Create legend for this group
            const groupLegend = group.labels.map((label, index) => ({
              label: `[${index + 1}] ${label}`,
              color: colorMap[label],
            }));

            return (
              <div key={groupIndex}>
                {/* Statements for this group */}
                {group.statements.map((statementData, statementIndex) => {
                  const item = statementData.surveyResult;
                  const total = Object.values(item.responses).reduce(
                    (sum, val) => sum + val,
                    0
                  );

                  return (
                    <div
                      key={`${groupIndex}-${statementIndex}`}
                      className="space-y-3 mb-8"
                    >
                      {/* Statement Header */}
                      <div className="text-sm md:text-base text-gray-700 font-medium leading-tight">
                        {item.statement}
                      </div>

                      {/* Chart Container with proper alignment */}
                      <div className="flex items-center gap-4">
                        {/* Stacked bar container */}
                        <div className="flex-1 relative">
                          {/* Small percentage labels container - positioned closer to the bar */}
                          <div className="absolute -top-4 left-0 right-0 flex h-4">
                            {group.labels.map((category) => {
                              const value = item.responses[category] || 0;
                              const percentage = getPercentage(value, total);

                              return (
                                <div
                                  key={`label-${category}`}
                                  className="flex items-center justify-center"
                                  style={{
                                    width: `${percentage}%`,
                                    minWidth: percentage > 0 ? "8px" : "0px",
                                  }}
                                >
                                  {percentage > 0 && percentage <= 3 ? (
                                    <span
                                      className="text-gray-700 text-xs font-medium whitespace-nowrap"
                                      style={{ fontSize: "9px" }}
                                    >
                                      {Math.round(percentage)}%
                                    </span>
                                  ) : null}
                                </div>
                              );
                            })}
                          </div>

                          {/* Main stacked bar */}
                          <div className="flex h-10 md:h-12 bg-gray-200 rounded overflow-hidden">
                            {group.labels.map((category) => {
                              const value = item.responses[category] || 0;
                              const percentage = getPercentage(value, total);

                              return (
                                <div
                                  key={category}
                                  className="flex items-center justify-center text-white text-xs font-medium transition-all duration-200 hover:opacity-90"
                                  style={{
                                    width: `${percentage}%`,
                                    backgroundColor: colorMap[category],
                                    minWidth: percentage > 0 ? "8px" : "0px",
                                  }}
                                >
                                  {percentage > 3
                                    ? `${Math.round(percentage)}%`
                                    : ""}
                                </div>
                              );
                            })}
                          </div>
                        </div>

                        <div className="flex-shrink-0">
                          <span
                            className="text-white text-xs px-2 py-2 rounded font-medium "
                            style={{ backgroundColor: "#1E3A8A" }} // dark navy blue
                          >
                            Avg: {item.avg_score.toFixed(2)}
                          </span>
                        </div>
                      </div>

                      {/* Mobile percentage labels */}
                      <div className="flex md:hidden justify-between text-xs text-gray-500 ml-0">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  );
                })}

                {/* Legend for this group - placed directly after its statements */}
                <div className="mt-4 mb-8">
                  <div className="flex flex-wrap justify-center gap-3 md:gap-6">
                    {groupLegend.map((item) => (
                      <div
                        key={item.label}
                        className="flex items-center gap-2 min-w-0"
                      >
                        <div
                          className="w-3 h-3 md:w-4 md:h-4 rounded flex-shrink-0"
                          style={{ backgroundColor: item.color }}
                        ></div>
                        <span className="text-xs md:text-sm text-gray-700 truncate">
                          {item.label}
                        </span>
                      </div>
                    ))}
                  </div>
                  {/* Add line after legend (except for the last group) */}
                  {groupIndex < statementGroups.length - 1 && (
                    <div className="border-t border-gray-200 mt-6"></div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
