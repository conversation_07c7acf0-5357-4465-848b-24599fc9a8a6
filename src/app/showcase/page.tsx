"use client";
import React, { useState, useEffect, useMemo } from "react";
import { withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import Fuse from "fuse.js";
import responseData from "../_components/showcaseHB_Data.json";
import ShowcaseOverviewCard from "../_components/_showcase/ShowcaseOverviewCard";
import Loading from "../_components/_ui/Loading";
import { Header } from "../_components/_ui/NotificationCenter";
import { Sparkle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

const ITEMS_PER_PAGE = 10;

// Fuse.js options for fuzzy search
const fuseOptions = {
  keys: [
    "title",
    "authors",
    "hb_folder",
    "journal",
    "DOI",
    "year",
    "regression_model",
    "type_of_task",
    "type_of_experiment_design",
    "research_domain",
    "research_sub_domain",
  ],
  threshold: 0.3, // Lower threshold means more strict matching
  includeScore: true,
};

export default withPageAuthRequired(
  function ShowcasePage() {
    const router = useRouter();
    const searchParams = useSearchParams();

    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [hbData, setHbData] = useState<any[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [filteredData, setFilteredData] = useState<any[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [isHovered, setIsHovered] = useState(false);

    // Initialize Fuse instance with memoization
    const fuse = useMemo(() => {
      return new Fuse(hbData, fuseOptions);
    }, [hbData]);

    // Function to create URL with search parameters
    const createQueryString = (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);
      return params.toString();
    };

    const ReplayTutorials = () => {
      if (window.pendo && window.pendo.isReady) {
        window.pendo.showGuideById("VaZdy_vVtS6bNBvfRdP704XdDWc");
      }
    };

    // Initialize from URL parameters
    useEffect(() => {
      const search = searchParams.get("search") || "";
      setSearchQuery(search);

      if (search && hbData.length > 0) {
        performSearch(search);
      }
    }, [searchParams, hbData]);

    useEffect(() => {
      if (responseData.length > 0) {
        setHbData(responseData);
        setFilteredData(responseData);
      }
    }, [responseData]);

    useEffect(() => {
      if (hbData.length > 0) {
        setIsLoading(false);

        // Perform initial search if URL has search parameter
        const initialSearch = searchParams.get("search");
        if (initialSearch) {
          performSearch(initialSearch);
        }
      }
    }, [hbData]);

    // Function to perform search (reused in multiple places)
    const performSearch = (query: string) => {
      if (query) {
        // Combine exact and fuzzy search
        const queryLower = query.toLowerCase();

        // Exact match search
        const exactMatches = hbData.filter(
          (experiment) =>
            experiment.title?.toLowerCase().includes(queryLower) ||
            experiment.authors?.toLowerCase().includes(queryLower) ||
            experiment.hb_folder?.toLowerCase().includes(queryLower) ||
            experiment.journal?.toLowerCase().includes(queryLower) ||
            experiment.DOI?.toLowerCase().includes(queryLower) ||
            experiment.year?.toString().includes(queryLower) ||
            experiment.regression_model?.toLowerCase().includes(queryLower) ||
            experiment.type_of_task?.toLowerCase().includes(queryLower) ||
            experiment.type_of_experiment_design
              ?.toLowerCase()
              .includes(queryLower) ||
            experiment.research_domain?.toLowerCase().includes(queryLower) ||
            experiment.research_sub_domain?.toLowerCase().includes(queryLower)
        );

        // Fuzzy search
        const fuzzyResults = fuse.search(query).map((result) => result.item);

        // Combine results and remove duplicates
        const combinedResults = Array.from(
          new Set([...exactMatches, ...fuzzyResults])
        );

        setFilteredData(combinedResults);
        setCurrentPage(1);
      } else {
        setFilteredData(hbData);
      }
    };

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
      const query = e.target.value;
      setSearchQuery(query);

      // Update URL with search parameter
      if (query) {
        router.push(`/showcase?${createQueryString("search", query)}`, {
          scroll: false,
        });
      } else {
        router.push("/showcase", { scroll: false });
      }

      performSearch(query);
    };

    // Debounced search to avoid too frequent URL updates
    useEffect(() => {
      const delayDebounceFn = setTimeout(() => {
        if (searchQuery) {
          router.push(`/showcase?${createQueryString("search", searchQuery)}`, {
            scroll: false,
          });
        } else if (searchParams.has("search")) {
          router.push("/showcase", { scroll: false });
        }
      }, 500);

      return () => clearTimeout(delayDebounceFn);
    }, [searchQuery]);

    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

    const handlePageChange = (page: number) => {
      setCurrentPage(page);
    };

    const paginatedData = filteredData.slice(
      (currentPage - 1) * ITEMS_PER_PAGE,
      currentPage * ITEMS_PER_PAGE
    );

    const renderPageNumbers = () => {
      const pageNumbers = [];
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(
          <button
            key={i}
            onMouseDown={() => handlePageChange(i)}
            className={`px-3 py-1 border rounded ${
              i === currentPage
                ? "bg-primary-dark text-white"
                : "bg-white text-primary-dark hover:bg-primary hover:text-white"
            }`}
          >
            {i}
          </button>
        );
      }
      return pageNumbers;
    };

    return (
      <div className="z-10 py-8 px-10 w-full flex flex-col font-inter">
        <div className="pb-6">
          <div className="flex justify-between">
            <h1 className="text-text-dark font-medium text-3xl pb-3">
              Showcase
            </h1>
            <div className="flex gap-2">
              <a
                href="https://form.typeform.com/to/ViMQd1a2"
                target="_blank"
                className="relative"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                {isHovered && (
                  <div
                    className="absolute flex flex-col items-start bg-primary-dark text-white text-xs py-2 px-4 rounded-lg shadow-lg"
                    style={{
                      right: "320px",
                      top: "50%",
                      transform: "translateY(-50%)",
                      width: "250px",
                    }}
                  >
                    <div>Want us to replicate your paper?</div>
                    <div>Contact us!</div>
                  </div>
                )}
                <button
                  className={`flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border ${
                    isHovered
                      ? "text-md"
                      : "bg-white hover:bg-secondary-grey shadow-sm"
                  }`}
                  style={{ width: "300px" }}
                >
                  <Sparkle />
                  <div>Request paper replication</div>
                </button>
              </a>
              <button
                onClick={ReplayTutorials}
                id="replay-tutorial-showcase"
                className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
              >
                Show Tutorial
              </button>
              <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
                <Header />
              </div>
            </div>
          </div>
          <p className="font-roboto text-subtitle pb-3 text-xl">
            Human Baseline Experiments we have created to show that our Causal
            Experiments have Bioequivalence, meaning that we can explain as much
            of the variance of any human decision as the best-designed studies.
          </p>
        </div>
        <div className="pb-4">
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearch}
            placeholder="Search by title, authors, journal, DOI, year, name, research domain, experiment type, regression model..."
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>
        <div className="flex flex-col w-full gap-4 h-fit">
          {isLoading ? (
            <Loading />
          ) : (
            paginatedData.map((experiment, index) => {
              const {
                title,
                authors,
                hb_folder,
                DOI,
                replication_id,
                journal,
                run_id,
              } = experiment;
              return (
                <ShowcaseOverviewCard
                  key={index}
                  title={title}
                  authors={authors}
                  hb_folder={hb_folder}
                  DOI={DOI}
                  repID={replication_id}
                  journal={journal}
                  runID={run_id}
                />
              );
            })
          )}
        </div>
        <div className="flex justify-center items-center mt-4 gap-2">
          <button
            onMouseDown={() => handlePageChange(1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white"
          >
            First
          </button>
          <button
            onMouseDown={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white"
          >
            Previous
          </button>
          {renderPageNumbers()}
          <button
            onMouseDown={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white"
          >
            Next
          </button>
          <button
            onMouseDown={() => handlePageChange(totalPages)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white"
          >
            Last
          </button>
        </div>
      </div>
    );
  },
  {
    returnTo: "/showcase",
  }
);
