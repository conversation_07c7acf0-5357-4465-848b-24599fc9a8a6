// /app/showcase/layout.tsx
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "Research Showcase | Subconscious",
  description:
    "Explore our collection of Human Baseline Experiments demonstrating Causal Experiments with Bioequivalence, capable of explaining human decision variance comparable to the best-designed studies.",

  openGraph: {
    title: "Research Showcase | Human Baseline Experiments",
    description:
      "Explore our collection of Human Baseline Experiments demonstrating Causal Experiments with Bioequivalence, capable of explaining human decision variance comparable to the best-designed studies.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Research Showcase | Human Baseline Experiments",
    description:
      "Explore our collection of Human Baseline Experiments demonstrating Causal Experiments with Bioequivalence.",
  },
};

export default function ShowcaseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
