/**
 * Subscription status response from the API
 */
export interface SubscriptionStatusResponse {
  subscription_status: {
    status: string;
    current_period_end?: number;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * Formatted subscription data for client use
 */
export interface SubscriptionData {
  stripeCustomerId: string | null;
  subscriptionStatus: "Active" | "Not Subscribed" | null;
  renewalDate: string | null;
  roles: string[];
  isSubscriber: boolean;
}

/**
 * Error response from the API
 */
export interface ApiErrorResponse {
  error: string;
  message?: string;
  status?: number;
}
