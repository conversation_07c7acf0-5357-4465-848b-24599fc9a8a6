"use server";

import { getManagementToken } from "../auth";
import * as Sen<PERSON> from "@sentry/nextjs";
import { SubscriptionStatusResponse } from "./types";

/**
 * Checks subscription status for a customer
 * @param {string} customerId - The Stripe customer ID
 * @returns {Promise<SubscriptionStatusResponse>} Subscription data
 */
export async function checkSubscription(
  customerId: string
): Promise<SubscriptionStatusResponse> {
  if (!customerId) {
    throw new Error("Customer ID is required");
  }

  try {
    const token = await getManagementToken();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/check-subscription/?customer_id=${customerId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Failed to check subscription: ${response.status} ${errorText}`
      );
    }

    return (await response.json()) as SubscriptionStatusResponse;
  } catch (error) {
    console.error("Error checking subscription:", error);

    // Log error to Sentry with context
    Sentry.captureException(error, {
      tags: {
        action: "checkSubscription",
        source: "server_action",
      },
      extra: {
        customerId,
        endpoint: `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/check-subscription`,
        errorMessage: error instanceof Error ? error.message : String(error),
      },
    });

    throw new Error(
      `Failed to check subscription status: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
