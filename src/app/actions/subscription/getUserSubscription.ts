"use server";

import { getUserData } from "../user/getUserData";
import { checkSubscription } from "./checkSubscription";
import * as Sentry from "@sentry/nextjs";
import { SubscriptionData } from "./types";

/**
 * Gets formatted user subscription data
 * @param {string} userId - The Auth0 user ID
 * @returns {Promise<SubscriptionData>} Formatted subscription data
 */
export async function getUserSubscription(
  userId: string
): Promise<SubscriptionData> {
  if (!userId) {
    throw new Error("User ID is required");
  }

  try {
    // First get the user data to get the stripe customer ID
    const userData = await getUserData(userId);

    if (!userData) {
      return {
        stripeCustomerId: null,
        subscriptionStatus: "Not Subscribed",
        renewalDate: null,
        roles: [],
        isSubscriber: false,
      };
    }

    // Default values if no metadata exists
    const stripeCustomerId = userData.app_metadata?.stripe_customer_id || null;
    const roles = userData.app_metadata?.roles || [];

    // If no customer ID, return default subscription data
    if (!stripeCustomerId) {
      return {
        stripeCustomerId: null,
        subscriptionStatus: "Not Subscribed",
        renewalDate: null,
        roles,
        isSubscriber: false,
      };
    }

    // Then check the subscription status
    const subscriptionData = await checkSubscription(stripeCustomerId);

    // Handle case where subscription_status might be undefined
    if (!subscriptionData.subscription_status) {
      return {
        stripeCustomerId,
        subscriptionStatus: "Not Subscribed",
        renewalDate: null,
        roles,
        isSubscriber: false,
      };
    }

    const status =
      subscriptionData.subscription_status.status === "active" ||
      subscriptionData.subscription_status.status === "trialing"
        ? ("Active" as const)
        : ("Not Subscribed" as const);

    const renewalDateEpoch =
      subscriptionData.subscription_status.current_period_end;
    const renewalDate = renewalDateEpoch
      ? new Date(renewalDateEpoch * 1000).toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        })
      : null;

    return {
      stripeCustomerId,
      subscriptionStatus: status,
      renewalDate,
      roles,
      isSubscriber: status === "Active",
    };
  } catch (error) {
    console.error("Error getting user subscription:", error);

    // Log error to Sentry with context
    Sentry.captureException(error, {
      tags: {
        action: "getUserSubscription",
        source: "server_action",
      },
      extra: {
        userId,
        errorMessage: error instanceof Error ? error.message : String(error),
      },
    });

    throw new Error(
      `Failed to get user subscription data: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
