"use server";

import { getManagementToken } from "../auth";
import * as Sen<PERSON> from "@sentry/nextjs";
import { CustomerPortalResponse } from "./types";

/**
 * Creates a Stripe customer portal session
 * @param {string} customerId - The Stripe customer ID
 * @returns {Promise<CustomerPortalResponse>} Portal session data with URL
 */
export async function createCustomerPortalSession(
  customerId: string
): Promise<CustomerPortalResponse> {
  if (!customerId) {
    throw new Error("Customer ID is required");
  }

  try {
    const token = await getManagementToken();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/create-customer-portal-session/?customer_id=${customerId}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Failed to create customer portal session: ${response.status} ${errorText}`
      );
    }

    const data = (await response.json()) as CustomerPortalResponse;

    // Validate that url exists in the response
    if (!data.url) {
      throw new Error("Invalid response: url is missing");
    }

    return data;
  } catch (error) {
    console.error("Error creating customer portal session:", error);

    // Log error to Sentry with context
    Sentry.captureException(error, {
      tags: {
        action: "createCustomerPortalSession",
        source: "server_action",
      },
      extra: {
        customerId,
        endpoint: `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/create-customer-portal-session`,
        errorMessage: error instanceof Error ? error.message : String(error),
      },
    });

    throw new Error(
      `Failed to create customer portal session: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
