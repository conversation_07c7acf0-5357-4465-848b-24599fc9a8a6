/**
 * Response from the checkout session creation API
 */
export interface CheckoutSessionResponse {
  session_id: string;
  [key: string]: any;
}

/**
 * Response from the customer portal session creation API
 */
export interface CustomerPortalResponse {
  url: string;
  [key: string]: any;
}

/**
 * Error response from the API
 */
export interface ApiErrorResponse {
  error: string;
  message?: string;
  status?: number;
}
