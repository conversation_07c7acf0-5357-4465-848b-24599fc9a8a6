"use server";

import { getManagementToken } from "../auth";
import * as Sen<PERSON> from "@sentry/nextjs";
import { CheckoutSessionResponse } from "./types";

/**
 * Creates a Stripe checkout session
 * @param {string} customerId - The Stripe customer ID
 * @returns {Promise<CheckoutSessionResponse>} Checkout session data with session_id
 */
export async function createCheckoutSession(
  customerId: string
): Promise<CheckoutSessionResponse> {
  if (!customerId) {
    throw new Error("Customer ID is required");
  }

  try {
    const token = await getManagementToken();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/create-checkout-session/?customer_id=${customerId}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          token: token,
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Failed to create checkout session: ${response.status} ${errorText}`
      );
    }

    const data = (await response.json()) as CheckoutSessionResponse;

    // Validate that session_id exists in the response
    if (!data.session_id) {
      throw new Error("Invalid response: session_id is missing");
    }

    return data;
  } catch (error) {
    console.error("Error creating checkout session:", error);

    // Log error to Sentry with context
    Sentry.captureException(error, {
      tags: {
        action: "createCheckoutSession",
        source: "server_action",
      },
      extra: {
        customerId,
        endpoint: `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/payments/create-checkout-session`,
        errorMessage: error instanceof Error ? error.message : String(error),
      },
    });

    throw new Error(
      `Failed to create checkout session: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
