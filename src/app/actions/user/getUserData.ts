"use server";

import { getManagementToken } from "../auth";
import * as Sen<PERSON> from "@sentry/nextjs";
import { Auth0UserData } from "./types";

/**
 * Fetches user data from Auth0 Management API
 * @param {string} userId - The Auth0 user ID
 * @returns {Promise<Auth0UserData | null>} User data including app_metadata
 */
export async function getUserData(
  userId: string
): Promise<Auth0UserData | null> {
  if (!userId) {
    throw new Error("User ID is required");
  }

  try {
    const token = await getManagementToken();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_AUTH0_AUDIENCE}users?q=user_id:"${userId}"&search_engine=v3`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Failed to fetch user data: ${response.status} ${errorText}`
      );
    }

    const data = (await response.json()) as Auth0UserData[];
    return data[0] || null;
  } catch (error) {
    console.error("Error fetching user data:", error);

    // Log error to Sentry with context
    Sentry.captureException(error, {
      tags: {
        action: "getUserData",
        source: "server_action",
      },
      extra: {
        userId,
        endpoint: `${process.env.NEXT_PUBLIC_AUTH0_AUDIENCE}users`,
        errorMessage: error instanceof Error ? error.message : String(error),
      },
    });

    throw new Error(
      `Failed to fetch user data from Auth0: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
