/**
 * Auth0 user data response
 */
export interface Auth0UserData {
  user_id: string;
  email: string;
  name?: string;
  nickname?: string;
  picture?: string;
  app_metadata?: {
    stripe_customer_id?: string;
    roles?: string[];
    [key: string]: any;
  };
  user_metadata?: {
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * Error response from the API
 */
export interface ApiErrorResponse {
  error: string;
  message?: string;
  status?: number;
}
