// /app/collaborative-workspace/layout.tsx
import { Metada<PERSON> } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "Collaboratory | Subconscious",
  description:
    "Collaborate with peers on research experiments and reports in real-time. Share insights, analyze data collectively, and accelerate discovery through teamwork.",
  openGraph: {
    title: "Collaborative Workspace | Research Together",
    description:
      "Collaborate with peers on research experiments and reports in real-time. Share insights, analyze data collectively, and accelerate discovery through teamwork.",
    type: "website",
    siteName: "Your Research Platform Name",
  },
  twitter: {
    card: "summary_large_image",
    title: "Collaborative Workspace | Research Together",
    description:
      "Collaborate with peers on research experiments and reports in real-time. Accelerate discovery through teamwork.",
  },
  keywords:
    "research collaboration, team research, collaborative experiments, shared reports, real-time collaboration",
};

export default function CollaborativeWorkspaceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
