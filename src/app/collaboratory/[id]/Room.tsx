"use client";

import { ReactNode, useEffect, useState } from "react";
import {
  LiveblocksProvider,
  RoomProvider,
  ClientSideSuspense,
} from "@liveblocks/react/suspense";
import { useParams } from "next/navigation";
import { useUser } from "@auth0/nextjs-auth0/client";
import Loading from "@/app/_components/_ui/Loading";

export function Room({ children }: { children: ReactNode }) {
  const params = useParams();
  const roomId = params.id as string;

  const { user, isLoading } = useUser();

  if (!roomId) {
    throw new Error("Room ID is missing or undefined.");
  }

  return (
    <LiveblocksProvider
      authEndpoint="/api/liveblocks-auth" // Pointing to the backend route
    >
      <RoomProvider
        id={roomId}
        initialPresence={{
          name: user?.name || "Anonymous User",
          email: user?.email || "No Email",
          sub: user?.sub || "No Sub",
        }}
      >
        <ClientSideSuspense fallback={<Loading />}>
          {children}
        </ClientSideSuspense>
      </RoomProvider>
    </LiveblocksProvider>
  );
}
