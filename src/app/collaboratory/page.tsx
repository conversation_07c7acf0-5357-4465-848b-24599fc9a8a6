"use client";

import { useEffect, useState } from "react";
import { collection, getDocs } from "firebase/firestore";
import { db } from "../lib/firebase";
import { useUser } from "@auth0/nextjs-auth0/client";
import { useRouter } from "next/navigation";
import CollaboratoryOverviewCard from "../_components/_collaboratory/CollaboratoryOverviewCard";
import { NoExperimentIcon } from "../../../public/icons/NoExperimentIcon";
import Loading from "../_components/_ui/Loading";

interface Experiment {
  id: string;
  name: string;
  collaborators: {
    email: string;
    nickname: string;
    role: string;
  }[];
}

export default function CollaboratoryPage() {
  const [experiments, setExperiments] = useState<Experiment[]>([]);
  const { user, isLoading } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!user || isLoading) return;

    const fetchExperiments = async () => {
      try {
        const querySnapshot = await getDocs(collection(db, "collaboratories"));
        const experimentsData = querySnapshot.docs
          .map((doc) => {
            const data = doc.data() as Omit<Experiment, "id">;
            return {
              id: doc.id,
              ...data,
            };
          })
          .filter((experiment) =>
            experiment.collaborators.some(
              (collaborator) => collaborator.email === user.email
            )
          );

        setExperiments(experimentsData);
      } catch (error) {
        console.error("Error fetching experiments:", error);
      }
    };

    fetchExperiments();
  }, [user, isLoading]);

  const handleDelete = (id: string) => {
    setExperiments((prev) => prev.filter((experiment) => experiment.id !== id));
  };

  if (isLoading)
    return (
      <div>
        <Loading />
      </div>
    );

  if (!user) return <div>Please log in to see your collaboratories.</div>;

  return (
    <div className="z-10 py-8 px-10 w-full flex flex-col font-inter">
      <div className="flex justify-between">
        <h1 className="text-text-dark font-medium text-3xl pb-6">
          Collaboratory
        </h1>
      </div>

      {experiments.length > 0 ? (
        <div className="grid gap-4">
          {experiments.map((experiment) => (
            <CollaboratoryOverviewCard
              key={experiment.id}
              experiment={experiment}
              onDelete={handleDelete}
            />
          ))}
        </div>
      ) : (
        <div className="flex items-center justify-center mt-4">
          <div className="flex flex-col gap-4 w-full py-24 items-center text-center border border-card-border rounded-xl bg-white">
            <NoExperimentIcon />
            <p className="text-text-dark font-medium text-2xl">
              You have not created any workspaces yet.
            </p>
            <button
              className="flex bg-[#504D9A] text-white font-inter font-medium text-lg px-4 py-2 rounded-xl items-center justify-center"
              onMouseDown={() => {
                router.push(`/experiments`);
              }}
            >
              <div className="flex w-fit gap-2 items-center px-2">
                <p className="whitespace-nowrap">Create your first Workspace</p>
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
