import { Suspense } from "react";
import { getSession } from "@auth0/nextjs-auth0/edge";
import { redirect } from "next/navigation";
import Loading from "../../_components/_ui/Loading";
import ConceptTestingResultsClient from "./ConceptTestingResultsClient";

async function ConceptTestingResultsPage() {
  const session = await getSession();
  if (!session?.user) {
    redirect("/api/auth/login?returnTo=/concept-testing/results");
  }

  return (
    <Suspense
      fallback={
        <div className="flex-1 flex items-center justify-center min-h-[calc(100vh-250px)]">
          <Loading />
        </div>
      }
    >
      <ConceptTestingResultsClient />
    </Suspense>
  );
}

export default ConceptTestingResultsPage;
