"use client";

import { useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@auth0/nextjs-auth0/client";
import {
  AlarmClock,
  ChevronUp,
  RefreshCw,
  Search,
  FileQuestion,
} from "lucide-react";
import Confetti from "react-confetti";
import * as Sentry from "@sentry/react";
import axios from "axios";

import Loading from "../../_components/_ui/Loading";
import ConceptTestingOverviewCard from "../../_components/_concept-testing/ConceptTestingOverviewCard";
import Paginator from "../../_components/_ui/Paginator";
import { Header } from "../../_components/_ui/NotificationCenter";
import CustomEventSource from "../../utils/CustomEventSource";
import filterRuns from "../../api/util/filter_runs";
import { Run } from "../../_components/_experiments/types";
import {
  useConceptTestingResultsStore,
  useConceptTestingRuns,
  useConceptTestingLocalRuns,
  useConceptTestingUI,
  useConceptTestingActions,
} from "../../stores/conceptTestingResultsStore";

const ITEMS_PER_PAGE = 10;

const fetcher = async (uri: string, userID: string | null) => {
  try {
    const response = await fetch(uri, {
      method: "POST",
      body: JSON.stringify({ userID: userID }),
    });
    if (!response.ok) {
      const error = new Error("Failed to fetch data");
      Sentry.captureException(error, {
        extra: { uri, userID, status: response.status },
      });
      throw error;
    }
    const data = await response.json();
    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: { uri, userID },
    });
    throw error;
  }
};

interface ConceptTestingResultsClientProps {
  initialRuns?: Run[];
}

export default function ConceptTestingResultsClient({
  initialRuns = [],
}: ConceptTestingResultsClientProps) {
  const { user } = useUser();
  const router = useRouter();

  // Zustand store hooks
  const {
    runs,
    localRuns,
    accessToken,
    connectionActive,
    setRuns,
    setLocalRuns,
    setQueueCount,
    setIsLoading,
    setAccessToken,
    setConnectionActive,
    setDebouncedSearchQuery,
    setShowConfetti,
    setConfettiExperimentId,
  } = useConceptTestingResultsStore();

  const filteredRuns = useConceptTestingRuns();
  const filteredLocalRuns = useConceptTestingLocalRuns();
  const {
    isLoading,
    collapsed,
    activeTab,
    selectedDropdownItem,
    currentPage,
    searchQuery,
    queueCount,
    showConfetti,
  } = useConceptTestingUI();

  const {
    setCollapsed,
    setActiveTab,
    setSelectedDropdownItem,
    setCurrentPage,
    setSearchQuery,
    updatePrivacy,
  } = useConceptTestingActions();

  // Initialize with server data
  useEffect(() => {
    if (initialRuns.length > 0) {
      const conceptTestingRuns = filterRuns(initialRuns, "concept_testing");
      setRuns(conceptTestingRuns);
      setLocalRuns(conceptTestingRuns);
      setIsLoading(false);
    }
  }, [initialRuns, setRuns, setLocalRuns, setIsLoading]);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery, setDebouncedSearchQuery]);

  // Fetch access token
  useEffect(() => {
    const fetchToken = async () => {
      try {
        const response = await fetch("/api/token");
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setAccessToken(data.accessToken);
      } catch (error) {
        console.error("Failed to fetch access token:", error);
        Sentry.captureException(error);
      }
    };
    fetchToken();
  }, [setAccessToken]);

  const fetchAndUpdateData = useCallback(async () => {
    setIsLoading(true);

    if (user?.sub) {
      try {
        const fetchRuns = await fetcher("/api/runs", user?.sub);
        const filteredRuns = filterRuns(fetchRuns, "concept_testing");
        setLocalRuns(filteredRuns);
        setRuns(filteredRuns);

        // Handle confetti logic for concept tests
        let pendingExpRunId =
          localStorage.getItem("pendingConceptTestRunId") || "";
        let recentExpId = filteredRuns.find(
          (run: Run) => run.state == "finished"
        )?.id;

        if (pendingExpRunId && recentExpId && pendingExpRunId === recentExpId) {
          localStorage.setItem(
            "runningConceptTests",
            Math.max(
              0,
              JSON.parse(localStorage.getItem("runningConceptTests") || "0") - 1
            ).toString()
          );
        }

        const newRunningConceptTest = filteredRuns.find(
          (run: Run) => run.state === "running"
        );
        const previousPendingId = pendingExpRunId;
        pendingExpRunId = newRunningConceptTest?.id || "";
        localStorage.setItem("pendingConceptTestRunId", pendingExpRunId);

        if (pendingExpRunId && pendingExpRunId !== previousPendingId) {
          const shownConfettiConceptTests = JSON.parse(
            localStorage.getItem("shownConfettiConceptTests") || "[]"
          );

          if (!shownConfettiConceptTests.includes(pendingExpRunId)) {
            setConfettiExperimentId(pendingExpRunId);
            setShowConfetti(true);
            shownConfettiConceptTests.push(pendingExpRunId);
            localStorage.setItem(
              "shownConfettiConceptTests",
              JSON.stringify(shownConfettiConceptTests)
            );
          }
        }

        const queueResponse = await axios.get("/api/tasks");
        const queueData = queueResponse.data;
        setQueueCount(queueData.length);
      } catch (error) {
        console.error("Error fetching concept testing runs:", error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [
    user?.sub,
    setIsLoading,
    setLocalRuns,
    setRuns,
    setQueueCount,
    setConfettiExperimentId,
    setShowConfetti,
  ]);

  // Setup EventSource for real-time updates
  useEffect(() => {
    let eventSource: any = null;

    const setupEventSource = () => {
      if (user && accessToken) {
        eventSource = new CustomEventSource(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/notifications/events`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        const eventHandler = (event: any) => {
          try {
            JSON.parse(event.data);
          } catch (error) {
            console.error("Failed to parse event data:", error);
            Sentry.captureException(error, {
              extra: {
                message: "Malformed event data",
                data: event.data,
              },
            });
          }
          fetchAndUpdateData();
        };

        eventSource.addEventListener("started", eventHandler);
        eventSource.addEventListener("finished", eventHandler);
        eventSource.addEventListener("crashed", eventHandler);

        eventSource.addEventListener("connection-status", (event: any) => {
          const status = JSON.parse(event.data);
          setConnectionActive(!status.reconnecting);
        });

        eventSource.onerror = (error: any) => {
          Sentry.captureException(error, {
            extra: { message: "EventSource failed. Reconnecting..." },
          });
          setTimeout(() => {
            setupEventSource();
          }, 3000);
        };
      }
    };

    if (user && accessToken) {
      fetchAndUpdateData();
      setupEventSource();
    }

    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [user, accessToken, fetchAndUpdateData, setConnectionActive]);

  // Confetti timeout effect
  useEffect(() => {
    if (showConfetti) {
      const timer = setTimeout(() => {
        setShowConfetti(false);
        setConfettiExperimentId(null);
      }, 2500);
      return () => clearTimeout(timer);
    }
  }, [showConfetti, setShowConfetti, setConfettiExperimentId]);

  // Event handlers
  const handleTabClick = useCallback(
    (tab: string) => {
      setActiveTab(tab);
    },
    [setActiveTab]
  );

  const handleShowAllConceptTests = useCallback(() => {
    setCollapsed(!collapsed);
  }, [collapsed, setCollapsed]);

  const handleDropdownItemClick = useCallback(
    (item: string) => {
      setSelectedDropdownItem(item);
    },
    [setSelectedDropdownItem]
  );

  const handleRefreshClick = useCallback(() => {
    fetchAndUpdateData();
  }, [fetchAndUpdateData]);

  const quickRefresh = useCallback(() => {
    return fetchAndUpdateData();
  }, [fetchAndUpdateData]);

  const handlePrivacyToggle = useCallback(
    (id: string, isPrivate: boolean) => {
      updatePrivacy(id, isPrivate);
    },
    [updatePrivacy]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage]
  );

  const ReplayTutorials = () => {
    if (window.pendo && window.pendo.isReady) {
      window.pendo.showGuideById("kpKqPpqjYOEUf_o5hXy9GDtUeRM");
    }
  };

  return (
    <div className="z-10 py-8 px-10 w-full flex flex-col font-inter">
      {showConfetti && <Confetti gravity={0.3} />}

      <div className="flex justify-between">
        <h1 className="text-text-dark font-medium text-3xl pb-6">
          Concept Testing Results
        </h1>
        <div className="flex items-center gap-2">
          <div
            className="flex cursor-pointer h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
            onClick={handleRefreshClick}
          >
            <RefreshCw className="w-7 h-6 text-[#868e90]" />
          </div>
          <button
            onClick={ReplayTutorials}
            className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
          >
            Show Tutorial
          </button>
          <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
            <Header />
          </div>
        </div>
      </div>

      <div className="flex w-full justify-between items-center">
        <div className="flex border-b relative border-[#D0D5DD] h-8 text-sm font-semibold">
          <div
            className={`cursor-pointer ${
              activeTab === "myConceptTests"
                ? "border-b text-[#312E81] border-[#312E81]"
                : "text-[#667085]"
            }`}
            onClick={() => handleTabClick("myConceptTests")}
          >
            Your Concept Tests
          </div>
        </div>
        {activeTab === "myConceptTests" && (
          <div className="min-w-fit flex flex-col relative cursor-pointer">
            <div
              className="flex items-center justify-between gap-3 bg-white border-[#D0D5DD] w-full border px-3 py-2 boxshadow-allexps rounded-lg"
              onClick={handleShowAllConceptTests}
            >
              {selectedDropdownItem}
              <ChevronUp
                className={`h-6 w-6 text-chevron transition-all duration-300 ${
                  collapsed ? "rotate-180" : "rotate-0"
                }`}
              />
            </div>
            {!collapsed && (
              <div className="flex w-full flex-col px-[6px] py-1 absolute boxshadow-dropdown top-12 rounded-lg z-10 bg-white">
                <div
                  className={`flex justify-start rounded-md w-full px-2 py-3 cursor-pointer ${
                    selectedDropdownItem === "All Concept Tests"
                      ? "bg-[#EFF0F5]"
                      : ""
                  }`}
                  onClick={() => handleDropdownItemClick("All Concept Tests")}
                >
                  All Concept Tests
                </div>
                <div
                  className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                    selectedDropdownItem === "Your Public Concept Tests"
                      ? "bg-[#EFF0F5]"
                      : ""
                  }`}
                  onClick={() =>
                    handleDropdownItemClick("Your Public Concept Tests")
                  }
                >
                  Your Public Concept Tests
                </div>
                <div
                  className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                    selectedDropdownItem === "Private Concept Tests"
                      ? "bg-[#EFF0F5]"
                      : ""
                  }`}
                  onClick={() =>
                    handleDropdownItemClick("Private Concept Tests")
                  }
                >
                  Private Concept Tests
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {isLoading ? (
        <div className="flex-1 flex items-center justify-center min-h-[calc(100vh-250px)]">
          <Loading />
        </div>
      ) : (
        <>
          {localRuns !== undefined &&
            localRuns.length === 0 &&
            runs.length === 0 &&
            queueCount === 0 && (
              <div className="flex items-center justify-center mt-4">
                <div className="flex flex-col gap-4 w-full py-24 items-center text-center border border-card-border rounded-xl bg-white">
                  <FileQuestion className="w-16 h-16 text-gray-400" />
                  <p className="text-text-dark font-medium text-2xl">
                    You have not created any concept tests yet.
                  </p>
                  <button
                    className="flex bg-[#504D9A] text-white font-inter font-medium text-lg px-4 py-2 rounded-xl items-center justify-center"
                    onMouseDown={() => {
                      router.push(`/concept-testing`);
                    }}
                  >
                    <div className="flex w-fit gap-2 items-center px-2">
                      <p className="whitespace-nowrap">
                        Create your first Concept Test
                      </p>
                    </div>
                  </button>
                </div>
              </div>
            )}

          {!isLoading &&
            runs.length === 0 &&
            localRuns !== undefined &&
            localRuns.length !== 0 && (
              <div className="flex flex-col gap-4 w-full justify-center items-center text-center">
                <p className="font-roboto text-text-dark text-xl font-normal">
                  Please wait while your concept test finishes executing.
                </p>
              </div>
            )}

          {localRuns && localRuns.length >= 5 && (
            <div className="relative flex items-center w-full mt-5 mb-4">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by concept test name, description, ID (partial ok), Date, Respondents, state (finished/failed)..."
                className="w-full px-4 py-2 pl-10 border rounded-lg
              focus:outline-none focus:ring-2 focus:ring-primary-dark
              bg-white transition-all duration-200"
                spellCheck="false"
                autoComplete="off"
              />
              <Search className="absolute left-3 w-4 h-4 text-gray-400" />
            </div>
          )}

          {queueCount > 0 && (
            <div className="border mt-4 border-blue-500 text-blue-700 rounded-md px-3 py-2 bg-blue-100 font-semibold mb-2 flex flex-row items-center gap-3">
              <div>
                <AlarmClock />
              </div>
              <div className="flex flex-col">
                <div className="py-1">
                  Concept Tests in Queue ({queueCount}). It will start
                  shortly...
                </div>
              </div>
            </div>
          )}

          <div className="mb-4 mt-4">
            {filteredLocalRuns && filteredLocalRuns.length !== 0 && (
              <div className="flex flex-col w-full gap-4">
                {filteredLocalRuns
                  .filter(
                    (run: Run) => run.state === "running" && !run.question
                  )
                  .map((run: Run) => (
                    <div key={run.id}>
                      <ConceptTestingOverviewCard
                        run={run}
                        runCount={filteredLocalRuns.length}
                        onPrivacyToggle={handlePrivacyToggle}
                      />
                    </div>
                  ))}
              </div>
            )}
          </div>

          {filteredRuns.length !== 0 && (
            <div className="flex flex-col w-full gap-4">
              {filteredRuns
                .filter((run: Run) => run.state !== "running")
                .slice(
                  (currentPage - 1) * ITEMS_PER_PAGE,
                  currentPage * ITEMS_PER_PAGE
                )
                .map((run: Run) => {
                  return (
                    <div key={run.id}>
                      <ConceptTestingOverviewCard
                        run={run}
                        runCount={filteredRuns.length}
                        onPrivacyToggle={handlePrivacyToggle}
                      />
                    </div>
                  );
                })}
            </div>
          )}

          {filteredRuns.length > ITEMS_PER_PAGE && (
            <div className="flex justify-center items-center mt-4">
              <Paginator
                page={currentPage}
                setPage={handlePageChange}
                totalPage={Math.ceil(filteredRuns.length / ITEMS_PER_PAGE)}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
