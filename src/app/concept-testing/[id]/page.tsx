import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, Download, ArrowUpRight, Users } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import HeaderClient from "./HeaderClient";
import { SurveyResultsChart } from "@/components/SurveyResultsChart";
import { NetSentimentChart } from "@/components/NetSentimentChart";
import { getAccessToken } from "@auth0/nextjs-auth0";
import ConceptImage from "./ConceptImage";
import { ShareButton } from "@/app/_components/_concept-testing/ShareButton";

type ParamsPromise = Promise<{ id: string }>;

export default async function ConceptTestPage(props: {
  params: ParamsPromise;
}) {
  const { id } = await props.params;

  const { accessToken } = await getAccessToken();

  if (!accessToken) {
    throw new Error("No access token found");
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${id}`,
    {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    }
  );
  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }
  const result = await res.json();
  const runDetails = result.run_details;

  const title = runDetails.run_name;
  const createdAt = runDetails.start_time;
  const description =
    runDetails.summary["Experiment Mappings"].concept_description;
  const imageNameFromApi = runDetails.summary["Experiment Mappings"].image_name;
  const image = imageNameFromApi
    ? (async () => {
        const presignedRes = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/aws_s3/presigned-download-url?filename=${encodeURIComponent("00917014-484f-47a2-808b-39813acb4f39.png")}`,
          {
            method: "POST",
            headers: {
              Accept: "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
        if (presignedRes.ok) {
          const { url } = await presignedRes.json();
          return url;
        }
        return "/brain_logo.png";
      })()
    : "/brain_logo.png";

  const when = runDetails.configs.experiment_design.year;
  const whereName = runDetails.configs.experiment_design.country;
  const whereFlag =
    whereName === "United States of America (USA)"
      ? "http://purecatamphetamine.github.io/country-flag-icons/3x2/US.svg"
      : "";

  const surveyResultsData =
    runDetails.summary["Survey Analytics"].survey_results;

  const conceptStatements =
    runDetails.summary["Experiment Mappings"].concept_statements;

  const audienceSize =
    surveyResultsData && surveyResultsData.length > 0
      ? Object.values(surveyResultsData[0].responses).reduce(
          (sum: number, val: any) => sum + val,
          0
        )
      : 0;

  const demographics = [];
  const targetPopulation =
    runDetails.configs.experiment_design.target_population;

  if (targetPopulation.age) {
    demographics.push({
      name: "Age",
      value: `${targetPopulation.age[0]}-${targetPopulation.age[1]}`,
    });
  }
  if (targetPopulation.gender) {
    demographics.push({
      name: "Gender",
      value: targetPopulation.gender.join(", "),
    });
  }
  if (targetPopulation.education_level) {
    demographics.push({
      name: "Education",
      value: targetPopulation.education_level
        .map((ed: string) => ed.replace(/_/g, " "))
        .join(", "),
    });
  }
  if (targetPopulation.household_income) {
    demographics.push({
      name: "Income",
      value: `$${targetPopulation.household_income[0].toLocaleString()}-$${targetPopulation.household_income[1].toLocaleString()}`,
    });
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Await the image URL if it's a Promise
  const imageUrl = image instanceof Promise ? await image : image;

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Top navigation bar */}
      <div className="sticky top-0 z-10 bg-background border-b border-border">
        <div className="container mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Link
              href="/concept-testing/results"
              className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              <span>Back to Tests</span>
            </Link>
          </div>
          <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
            <HeaderClient />
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 container mx-auto px-4 sm:px-6 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div className="flex items-center gap-3">
            {/* <Button variant="outline" className="h-10 gap-2">
                <Download className="h-4 w-4" />
                Export
              </Button> */}
            <ShareButton
              shareTitle={`Concept Test Results for ${title || "Untitled Concept"}`}
              shareText={
                description
                  ? `Check out these concept test results: ${description.substring(0, 100)}...`
                  : "Check out these concept test results!"
              }
            />
          </div>
        </div>

        {/* Concept Details Section */}
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8 p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Image and Description - Grouped */}
            <div className="flex flex-col gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">Image</h3>
                <div className="relative aspect-video w-full overflow-hidden rounded-lg border border-input">
                  <ConceptImage
                    src={imageUrl}
                    alt="Concept"
                    className="object-contain w-full h-full p-5"
                  />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-3">Description</h3>
                <div className="p-4 bg-muted/20 border border-input rounded-lg min-h-[120px]">
                  <p className="text-text-default leading-relaxed">
                    {description}
                  </p>
                </div>
              </div>
            </div>

            {/* Context and Sample Characteristics - Grouped */}
            <div className="flex flex-col gap-6">
              {/* When and Where */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Context</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">
                      When
                    </h4>
                    <div className="p-3 bg-muted/20 rounded-md border border-input text-base">
                      {when}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">
                      Where
                    </h4>
                    <div className="p-3 bg-muted/20 rounded-md border border-input text-base flex items-center gap-2">
                      {whereFlag && (
                        <Image
                          src={whereFlag}
                          alt={`${whereName} flag`}
                          width={24}
                          height={16}
                          className="inline-block"
                        />
                      )}
                      {whereName}
                    </div>
                  </div>
                </div>
              </div>

              {/* Sample Characteristics */}
              <div>
                <h3 className="text-lg font-semibold mb-3">
                  Sample Characteristics
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {demographics.map((demo, idx) => (
                    <div
                      key={idx}
                      className="flex flex-col p-3 bg-muted/20 rounded-md border border-input"
                    >
                      <span className="text-xs text-muted-foreground mb-1">
                        {demo.name}
                      </span>
                      <span className="text-sm font-medium">{demo.value}</span>
                    </div>
                  ))}
                </div>
                <div className="bg-muted/20 rounded-lg p-3 mt-4 flex items-center gap-2 border border-input">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-text-default">
                    {audienceSize} respondents
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Survey Results Charts */}
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8">
          <div className="p-6">
            <div className="space-y-8">
              <SurveyResultsChart
                data={surveyResultsData}
                conceptStatements={conceptStatements}
              />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8">
          <div className="p-6">
            <NetSentimentChart
              data={surveyResultsData}
              conceptStatements={conceptStatements}
            />
          </div>
        </div>

        {/* Call to action */}
        <div className="bg-primary/5 rounded-lg border border-primary/20 p-6 flex flex-col md:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="text-lg font-semibold text-text-dark mb-1">
              Want to test another concept?
            </h3>
            <p className="text-muted-foreground">
              Create a new concept test and gather insights from your target
              audience.
            </p>
          </div>
          <Button className="whitespace-nowrap gap-2">
            Create New Test
            <ArrowUpRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
