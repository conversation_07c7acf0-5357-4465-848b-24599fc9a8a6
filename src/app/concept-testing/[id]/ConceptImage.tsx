"use client";

import React, { useState } from "react";
import Image from "next/image";

interface ConceptImageProps {
  src: string;
  alt: string;
  className?: string;
}

export default function ConceptImage({
  src,
  alt,
  className,
}: ConceptImageProps) {
  const [imageSrc, setImageSrc] = useState(src);

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Image
        src={imageSrc}
        alt={alt}
        width={0}
        height={0}
        sizes="100vw"
        style={{
          width: "100%",
          height: "100%",
          objectFit: "contain",
          objectPosition: "center",
        }}
        className={className ? `${className} object-contain` : "object-contain"}
        onError={() => {
          setImageSrc("/brain_logo.png");
        }}
      />
    </div>
  );
}
