"use client";
import React from "react";
import { DetailedRun } from "@/app/_components/_insights/types";
import { withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import { useEffect, useState } from "react";
import * as Sentry from "@sentry/react";
import { ErrorMessage } from "@/app/utils/errorMessage";

interface InsightsPageProps {
  params: { slug: string };
}

const runFetcher = async (uri: string, runID: string) => {
  const body = JSON.stringify({ runID: runID });
  const response = await fetch(uri, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: body,
    cache: "no-cache",
  });

  if (!response.ok) {
    const error = new Error(`Failed to fetch run data: ${response.statusText}`);
    Sentry.captureException(error, {
      extra: { uri, runID, status: response.status },
    });
    throw error;
  }

  try {
    const data = await response.json();
    return data;
  } catch (error) {
    Sentry.captureException(error, { extra: { uri, runID } });
    throw error;
  }
};

const InsightsPage = ({ params }: InsightsPageProps) => {
  const [run, setRun] = useState<DetailedRun | undefined>(undefined);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [runName, setRunName] = useState("");
  const [runID, setRunID] = useState("");
  const [analyticsURL, setAnalyticsURL] = useState(
    process.env.NEXT_PUBLIC_ANALYTICS_URL
  );

  const iframeId = "insights-iframe";

  useEffect(() => {
    const iframe = document.getElementById(iframeId) as HTMLIFrameElement;

    const handleIframeLoad = () => {
      setIframeLoaded(true);
      getRunData();
    };

    iframe.addEventListener("load", handleIframeLoad);

    return () => {
      iframe.removeEventListener("load", handleIframeLoad);
    };
  }, []);

  const getRunData = async () => {
    try {
      const fetchRun = await runFetcher("/api/run", params.slug);
      setRun(fetchRun);
      if (fetchRun) {
        setRunID(fetchRun.run_details.run_id);
        setRunName(fetchRun.run_details.run_name);
      } else {
        throw new Error("Run data is undefined");
      }
    } catch (error) {
      Sentry.captureException(new Error(ErrorMessage.runData), {
        extra: { slug: params.slug },
      });
    }
  };

  const sendRunNameToShiny = async () => {
    try {
      const response = await fetch("/api/token");
      const data = await response?.json();
      const accessToken = data?.accessToken;
      const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
      const apiURL = process.env.NEXT_PUBLIC_BACKEND_ENDPOINT;

      const message = {
        runName,
        accessToken,
        apiURL,

        runID,
      };
      iframe.contentWindow!.postMessage(message, analyticsURL!);
    } catch (error) {
      Sentry.captureException(new Error("Failed to send run name to Shiny"), {
        extra: { runName, runID },
      });
    }
  };

  useEffect(() => {
    if (iframeLoaded && runName) {
      const checkShinyReady = setInterval(() => {
        const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
        iframe.contentWindow!.postMessage({ type: "isShinyReady" }, "*");
      }, 1000);

      const handleMessage = (event: MessageEvent) => {
        if (event.data && event.data.type === "shinyReady") {
          sendRunNameToShiny();
          clearInterval(checkShinyReady);
        }
      };

      window.addEventListener("message", handleMessage);

      return () => {
        window.removeEventListener("message", handleMessage);
        clearInterval(checkShinyReady);
      };
    }
  }, [iframeLoaded, runName]);

  return (
    <div className="h-full w-full">
      <iframe
        id={iframeId}
        src={analyticsURL}
        title="Insights"
        className="w-full h-full"
      />
    </div>
  );
};

export default withPageAuthRequired(
  Sentry.withProfiler(InsightsPage as React.ComponentType<unknown>)
) as React.ComponentType;
