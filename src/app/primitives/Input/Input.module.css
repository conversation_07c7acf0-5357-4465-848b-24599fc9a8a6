.input {
  height: var(--space-13);
  padding: 0 var(--space-5);
  background: var(--color-surface);
  border-radius: var(--radius-sm);
  font-size: var(--size-sm);
  color: var(--color-text-light);
  transition: var(--transition);
  transition-property: background, color, opacity;
  outline: none;
}

.input:hover,
.input:focus-visible {
  background: var(--color-surface-hover);
  color: var(--color-text);
}

.input:disabled {
  cursor: default;
  opacity: var(--opacity-disabled);
}

.input::placeholder {
  color: var(--color-text-lightest);
}
