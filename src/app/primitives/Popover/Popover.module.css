@keyframes up {
  0% {
    opacity: 0;
    transform: translateY(var(--space-3));
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes down {
  0% {
    opacity: 0;
    transform: translateY(calc(-1 * var(--space-3)));
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.popover {
  z-index: var(--z-above);
  border: 1px solid var(--color-border);
  background: var(--color-surface-elevated);
  border-radius: var(--radius);
  box-shadow: var(--shadow-xl);
  animation: 0.15s ease-in-out;
  overflow: hidden;
}

.popover[data-side="top"] {
  animation-name: up;
}
.popover[data-side="bottom"] {
  animation-name: down;
}
