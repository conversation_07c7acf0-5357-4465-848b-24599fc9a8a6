import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import {
  ModerationRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    ModerationRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: {} // Required for Next.js 15+ compatibility
    ): Promise<NextResponse> {
      try {
        // Get access token with proper response handling
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        const { why_prompt } = validatedData;

        // Make API call with improved error handling
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/copilot/check-moderation`,
          null, // Null body since we're using query params
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              Accept: "application/json",
            },
            params: {
              why_prompt,
            },
            timeout: 60_000, // 60 seconds (using numeric separator)
            validateStatus: () => true,
          }
        );

        return createSuccessResponse(data);
      } catch (error: unknown) {
        console.error(
          "Moderation Error:",
          error instanceof Error ? error.message : error
        );

        // Handle different error types
        if (axios.isAxiosError(error)) {
          return createErrorResponse(
            ErrorMessage.moderationData,
            error.response?.status || 500,
            error.response?.data || error.message
          );
        }

        return createErrorResponse(
          ErrorMessage.moderationData,
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
) as any; // Temporary Auth0 compatibility fix
