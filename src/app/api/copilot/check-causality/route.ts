import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import {
  CausalityRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    CausalityRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: {} // Required for Next.js 15+ compatibility
    ): Promise<NextResponse> {
      try {
        // Get access token with proper response handling
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        const { why_prompt } = validatedData;

        // Make API call with improved error handling
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v2/copilot/causality`,
          { why_prompt },
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            timeout: 60_000, // 60 seconds (using numeric separator)
            validateStatus: () => true,
          }
        );

        return createSuccessResponse(data);
      } catch (error: unknown) {
        console.error(
          "Causality Error:",
          error instanceof Error ? error.message : error
        );

        // Handle different error types
        if (axios.isAxiosError(error)) {
          return createErrorResponse(
            ErrorMessage.causalityData,
            error.response?.status || 500,
            error.response?.data || error.message
          );
        }

        return createErrorResponse(
          ErrorMessage.causalityData,
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
) as any; // Temporary Auth0 compatibility fix
