import { liveblocks } from "@/app/lib/liveblocks";
import { getSession } from "@auth0/nextjs-auth0";
import { NextResponse } from "next/server";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/app/lib/firebase";

export async function POST(request: Request) {
  try {
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { room } = await request.json();
    if (!room) {
      return NextResponse.json(
        { error: "Room ID is required" },
        { status: 400 }
      );
    }

    const { sub, name, email } = session.user;

    // Fetch the experiment details from Firebase
    const experimentRef = doc(db, "collaboratories", room);
    const experimentSnap = await getDoc(experimentRef);

    if (!experimentSnap.exists()) {
      return NextResponse.json({ error: "Room not found" }, { status: 404 });
    }

    const experiment = experimentSnap.data();
    const collaborator = experiment.collaborators.find(
      (collab: any) => collab.email === email
    );

    if (!collaborator) {
      return NextResponse.json(
        { error: "Not a collaborator" },
        { status: 403 }
      );
    }

    // Determine Liveblocks permission based on role
    const permission =
      collaborator.role === "Owner" || collaborator.role === "Can edit"
        ? "full"
        : collaborator.role === "Can view"
          ? "read"
          : "none";

    if (permission === "none") {
      return NextResponse.json({ error: "Access Denied" }, { status: 403 });
    }

    const userInfo = {
      id: sub,
      name: name || "Anonymous User",
      email: email || "<EMAIL>",
      sub: sub,
    };

    const lbSession = await liveblocks.prepareSession(userInfo.id, {
      userInfo,
    });

    lbSession.allow(
      room,
      permission === "full" ? lbSession.FULL_ACCESS : lbSession.READ_ACCESS
    );
    const authorizationResponse = await lbSession.authorize();

    return new Response(authorizationResponse.body, {
      status: authorizationResponse.status,
    });
  } catch (error) {
    console.error("Error in Liveblocks authentication:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
