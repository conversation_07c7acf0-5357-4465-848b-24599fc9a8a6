import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import {
  CreateLevelsRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    CreateLevelsRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: {} // Required for Next.js 15+ compatibility
    ): Promise<NextResponse> {
      try {
        // Get access token with proper response handling
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        // validatedData contains the validated request body

        // Make API call with improved error handling
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/levels`,
          validatedData,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            timeout: 10_000, // 10 seconds (using numeric separator)
            validateStatus: () => true,
          }
        );

        return createSuccessResponse(data);
      } catch (error: unknown) {
        console.error(
          "Levels Error:",
          error instanceof Error ? error.message : error
        );

        // Handle different error types with standardized responses
        if (axios.isAxiosError(error)) {
          return createErrorResponse(
            ErrorMessage.levelData,
            error.response?.status || 500,
            error.response?.data || error.message
          );
        }

        return createErrorResponse(
          ErrorMessage.levelData,
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
) as any; // Temporary Auth0 compatibility fix
