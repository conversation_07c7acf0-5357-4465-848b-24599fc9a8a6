import { NextRequest, NextResponse } from "next/server";
import { withApiAuthRequired } from "@auth0/nextjs-auth0";
import { ErrorMessage } from "@/app/utils/errorMessage";
import { createCheckoutSession } from "@/app/actions/stripe";

/**
 * API route for creating a Stripe checkout session
 * This is a wrapper around the server action to maintain compatibility
 * with existing code that expects this endpoint
 */
export const POST = withApiAuthRequired(async function handler(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Required context type
) {
  try {
    const { stripeCustomerId } = await req.json();

    if (!stripeCustomerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    // Use the server action to create the checkout session
    const data = await createCheckoutSession(stripeCustomerId);
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error creating checkout session:", error);
    return NextResponse.json(
      { error: ErrorMessage.checkoutError },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
