import { NextRequest, NextResponse } from "next/server";
import { jwtDecode } from "jwt-decode";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Required context
) {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());

    if (!accessToken) {
      return NextResponse.json({ roles: [] });
    }

    const decoded = jwtDecode<{ [key: string]: string[] }>(accessToken);
    const roles = decoded["https://www.api.subconscious.ai/roles"] || [];

    return NextResponse.json({ roles });
  } catch (error: unknown) {
    console.error(
      "Roles Check Error:",
      error instanceof Error ? error.message : error
    );

    return NextResponse.json({ roles: [] });
  }
}) as any; // Temporary Auth0 fix
