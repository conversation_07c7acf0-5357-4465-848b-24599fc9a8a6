import { getAccessToken } from "@auth0/nextjs-auth0";
import axios from "axios";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Input validation schema
const RealWorldConjointStatementRequestSchema = z.object({
  why_prompt: z.string(),
  suggestion_count: z.number().int().min(1).max(5),
  attribute_count: z.number().int().min(2).max(10),
  statement_history: z.array(z.string()),
});

export type RealWorldConjointStatementRequest = z.infer<
  typeof RealWorldConjointStatementRequestSchema
>;

// Response schema
const RealWorldConjointStatementResponseSchema = z.object({
  is_causal: z.boolean(),
  suggestions: z.array(
    z.object({
      attributes: z.array(z.string()),
      statement: z.string(),
    })
  ),
});

export type RealWorldConjointStatementResponse = z.infer<
  typeof RealWorldConjointStatementResponseSchema
>;

export async function POST(request: NextRequest) {
  try {
    let res = new NextResponse();
    const { accessToken } = await getAccessToken(request, res);

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = RealWorldConjointStatementRequestSchema.parse(body);

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/experiments/conjoint-statements`,
      validatedData,
      {
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    const data: RealWorldConjointStatementResponse = response.data;

    return NextResponse.json(data, { status: 200 });
  } catch (error: unknown) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    console.error("Error processing conjoint statement request:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
