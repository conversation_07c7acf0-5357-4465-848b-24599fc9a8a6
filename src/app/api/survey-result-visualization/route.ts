import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Required context type
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const run_name = searchParams.get("run_name");

    // Validate required parameter
    if (!run_name) {
      return NextResponse.json(
        { error: "run_name query parameter is required" },
        { status: 400 }
      );
    }

    // Make API call with improved error handling
    const { data } = await axios.get(
      `https://pygwalker-app-820181924042.us-central1.run.app/visualize/${run_name}`,
      {
        params: {
          environment: process.env.ENVIRONMENT,
        },
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "text/html", // Keeping original accept header
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    // Return HTML response as JSON string if needed
    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Visualization Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          error: ErrorMessage.traitsFetch,
          details: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json(
      { error: ErrorMessage.traitsFetch },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
