import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import {
  LikertLabelRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    LikertLabelRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData
    ): Promise<NextResponse> {
      const res = new NextResponse(); // Create response object internally
      try {
        // Pass req and res to getAccessToken
        const { accessToken } = await getAccessToken(req, res);
        if (!accessToken) {
          return createErrorResponse("Unauthorized", 401);
        }

        // Data is already validated by withValidation wrapper
        // validatedData contains: description, image_name, scale, statements

        const backendUrl = `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/experiments/likert-label`;

        const backendResponse = await axios.post(backendUrl, validatedData, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        });

        // Return backend data using standardized success response
        return createSuccessResponse(backendResponse.data);
      } catch (error: unknown) {
        console.error("Likert Label API Error:", error);

        if (axios.isAxiosError(error)) {
          return createErrorResponse(
            "Failed to generate Likert labels",
            error.response?.status || 500,
            error.response?.data?.detail || error.message
          );
        }

        return createErrorResponse(
          "Failed to generate Likert labels",
          500,
          error instanceof Error ? error.message : "Internal Server Error"
        );
      }
    }
  )
) as any; // Temporary Auth0 compatibility fix
