import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import FormData from "form-data";
import { getAccessToken } from "@auth0/nextjs-auth0";

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const { accessToken } = await getAccessToken(req, new NextResponse());

    const file = formData.get("file");

    if (!file) {
      return NextResponse.json(
        { error: "No file found in the request" },
        { status: 400 }
      );
    }

    // Create a new FormData object to forward the file
    const outgoingFormData = new FormData();
    // The API expects the field name to be "file"
    outgoingFormData.append("file", file);

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/personas/extract-from-pdf`,
      outgoingFormData,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        // Include withCredentials if required by the target API
        // withCredentials: true,
      }
    );

    // Axios automatically parses JSON responses
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error processing PDF extraction request:", error);

    // Handle Axios errors
    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const errorData = error.response?.data || error.message;

      return NextResponse.json(
        { error: "API request failed", details: errorData },
        { status }
      );
    }

    // Handle other errors
    let errorMessage = "Internal Server Error";
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { error: "Failed to process the request.", details: errorMessage },
      { status: 500 }
    );
  }
}
