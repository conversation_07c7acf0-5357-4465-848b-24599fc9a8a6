import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import {
  ProductLevelsRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    ProductLevelsRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: { params?: Record<string, string | string[]> } // Allow params to be optional
    ): Promise<NextResponse> {
      try {
        // Get access token with proper response handling
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        // validatedData contains: why_prompt, country, and other validated fields

        // Make API call with improved error handling
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/product-attributes-levels`,
          validatedData,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            timeout: 120_000, // 2 minutes (using numeric separator)
            validateStatus: () => true,
          }
        );

        return createSuccessResponse(data);
      } catch (error: unknown) {
        console.error(
          "Product Levels Error:",
          error instanceof Error ? error.message : error
        );

        // Handle different error types
        if (axios.isAxiosError(error)) {
          if (error.code === "ECONNABORTED") {
            return createErrorResponse(
              "Request timed out. Please try again.",
              408
            );
          }
          return createErrorResponse(
            "Failed to generate product levels",
            error.response?.status || 500,
            error.response?.data || error.message
          );
        }

        return createErrorResponse(
          "An unknown error occurred",
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
) as any; // Temporary Auth0 compatibility fix
