import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Required context
) {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());
    const body = await req.json();

    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/traits/suggest/levels`,
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 100_000, // 100 seconds (formatted)
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Trait Levels Error:",
      error instanceof Error ? error.message : error
    );

    return NextResponse.json(
      {
        error: ErrorMessage.attributesAndLevelsData,
        details: axios.isAxiosError(error)
          ? error.response?.data || error.message
          : "Unknown error",
      },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 fix
