import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import { createSuccessResponse, createErrorResponse } from "@/lib/schemas";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Required context
): Promise<NextResponse> {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());

    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/traits`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (formatted)
        validateStatus: () => true,
      }
    );

    return createSuccessResponse(data);
  } catch (error: unknown) {
    console.error(
      "Traits Fetch Error:",
      error instanceof Error ? error.message : error
    );

    if (axios.isAxiosError(error)) {
      return createErrorResponse(
        ErrorMessage.traitsFetch,
        error.response?.status || 500,
        error.response?.data || error.message
      );
    }

    return createErrorResponse(
      ErrorMessage.traitsFetch,
      500,
      error instanceof Error ? error.message : "Unknown error"
    );
  }
}) as any; // Temporary Auth0 fix
