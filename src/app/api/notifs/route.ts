import { NextRequest, NextResponse } from "next/server";

interface RequestBody {
  name: string;
  to: {
    subscriberId: string;
    firstName?: string;
    email?: string;
    lastName?: string;
    avatar?: string;
  };
  payload: any;
}

export async function POST(req: NextRequest) {
  try {
    const { name, to, payload }: RequestBody = await req.json();

    const novuApiKey = process.env.NOVU_API_KEY;
    const novuApiUrl = process.env.NOVU_API_URL;

    if (!novuApiKey) {
      return NextResponse.json(
        { message: "Novu API key is not configured" },
        { status: 500 }
      );
    }

    // Construct the recipient object for Novu
    const recipient = {
      subscriberId: to.subscriberId,
      firstName: to.firstName,
      email: to.email,
      lastName: to.lastName,
      avatar: to.avatar,
    };

    // Make the API call to Novu
    const response = await fetch(`${novuApiUrl}/v1/events/trigger`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `ApiKey ${novuApiKey}`,
      },
      body: JSON.stringify({
        name,
        to: [recipient], // Pass the recipient with multiple properties
        payload,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { message: errorData.message },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error("Error triggering notification:", error);
    return NextResponse.json(
      { message: "Failed to send notification" },
      { status: 500 }
    );
  }
}
