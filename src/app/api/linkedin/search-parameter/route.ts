import { unipileFetch } from "@/lib/unipileClient";
import { NextRequest } from "next/server";

import { NextResponse } from "next/server";

export interface SearchResponse {
  object: string;
  items: Item[];
  paging: Paging;
}

export interface Item {
  object: string;
  title: string;
  id: string;
}

export interface Paging {
  page_count: number;
}

type Type =
  | "LOCATION"
  | "INDUSTRY"
  | "COMPANY"
  | "SCHOOL"
  | "TITLE"
  | "PEOPLE"
  | "SERVICE"
  | "JOB_FUNCTION"
  | "JOB_TITLE"
  | "EMPLOYMENT_TYPE"
  | "SKILL";

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const keywords = searchParams.get("keywords");
  const type = searchParams.get("type") as Type;

  const data = await unipileFetch("/api/v1/linkedin/search/parameters", {
    keywords: keywords || "",
    type: type,
  });

  return NextResponse.json(data.items);
}
