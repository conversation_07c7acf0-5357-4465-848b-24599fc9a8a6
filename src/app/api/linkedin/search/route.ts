import { unipileFetch } from "@/lib/unipileClient";
import { NextRequest, NextResponse } from "next/server";
export interface LinkedInSearchParams {
  industry?: { include: string[] };
  company?: { include: string[] };
  past_company?: { include: string[] };
  school?: { include: string[] };
  company_location?: { include: string[] };
  role?: { include: string[] };
  seniority?: { include: string[] };
  past_role?: { include: string[] };
  keywords?: string;
  first_name?: string;
  last_name?: string;
  profile_language?: string[];
  company_headcount?: { min?: number; max?: number }[];
  company_type?: string[];
  tenure_at_company?: { min?: number; max?: number }[];
  tenure_at_role?: { min?: number; max?: number }[];
}

export async function POST(req: NextRequest) {
  try {
    // Parse the request body from NextRequest
    const requestBody: LinkedInSearchParams = await req.json();

    const filteredProfiles = await searchProfiles(requestBody);
    const profiles = [];
    for (const item of filteredProfiles) {
      const profile = await fetchProfile(item.public_identifier);
      const profilePosts = await fetchProfilePosts(profile.provider_id);
      profiles.push({ ...profile, ...item, posts: profilePosts });
    }

    return NextResponse.json(profiles);
  } catch (error) {
    console.error("Error processing LinkedIn API request:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}

const searchProfiles = async (requestBody: LinkedInSearchParams) => {
  const data = await unipileFetch(
    "/api/v1/linkedin/search",
    {
      limit: "5",
    },
    {
      method: "POST",
      body: JSON.stringify({
        api: "sales_navigator",
        category: "people",
        ...requestBody,
        posted_on_linkedin: true,
      }),
    }
  );
  return data.items;
};

const fetchProfilePosts = async (identifier: string) => {
  const data = await unipileFetch(`/api/v1/users/${identifier}/posts`);
  return data.items;
};

const fetchProfile = async (identifier: string) => {
  const data = await unipileFetch(`/api/v1/users/${identifier}`, {
    linkedin_sections: "*",
  });
  return data;
};
