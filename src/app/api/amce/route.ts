import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export async function POST(req: NextRequest) {
  // Check authentication
  const session = await getSession();
  if (!session?.accessToken) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  const accessToken = session.accessToken;
  const body = await req.json();

  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/artifact/${body.filename}`,
      {
        headers: {
          accept: "application/json",
          "Content-Type": "image/png",
          Authorization: `Bearer ${accessToken}`,
          responseType: "arraybuffer",
        },
        timeout: 10000,
      }
    );

    const data = Buffer.from(response.data, "binary");
    return new NextResponse(data, {
      headers: {
        "Content-Type": "image/png",
      },
    });
  } catch (error) {
    console.error("Error in POST request:", error);
    return NextResponse.json(
      { error: ErrorMessage.artifactData },
      { status: 500 }
    );
  }
}
