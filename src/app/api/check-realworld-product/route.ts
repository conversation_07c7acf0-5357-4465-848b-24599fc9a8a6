import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import {
  ProductCheckRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    ProductCheckRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: {} // Required for Next.js 15+ compatibility
    ): Promise<NextResponse> {
      try {
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        const { why_prompt } = validatedData;

        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/market-simulator/check-realworld-product`,
          validatedData,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            timeout: 10_000, // 10 seconds timeout
            validateStatus: () => true,
          }
        );

        return createSuccessResponse(data);
      } catch (error: unknown) {
        console.error("Verification Error:", error);

        if (axios.isAxiosError(error)) {
          if (error.code === "ECONNABORTED") {
            return createErrorResponse(
              "Request timed out. Please try again.",
              408
            );
          }
          if (error.response) {
            return createErrorResponse(
              error.response.data?.error || "Verification failed",
              error.response.status
            );
          }
        }

        return createErrorResponse(
          "Unable to verify product existence in the real world",
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
) as any; // Temporary Auth0 compatibility fix
