import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import {
  RunRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    RunRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: { params?: Record<string, string | string[]> } // Required context type
    ): Promise<NextResponse> {
      try {
        // Get access token with proper response handling
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        const { runID } = validatedData;

        // Make API call with improved error handling
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${runID}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              Accept: "application/json",
            },
            timeout: 10_000, // 10 seconds (using numeric separator)
            validateStatus: () => true,
          }
        );

        return createSuccessResponse(data);
      } catch (error: unknown) {
        console.error(
          "Run Data Error:",
          error instanceof Error ? error.message : error
        );

        // Handle different error types
        if (axios.isAxiosError(error)) {
          return createErrorResponse(
            ErrorMessage.runData,
            error.response?.status || 500,
            error.response?.data || error.message
          );
        }

        return createErrorResponse(
          ErrorMessage.runData,
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
) as any; // Temporary Auth0 compatibility fix
