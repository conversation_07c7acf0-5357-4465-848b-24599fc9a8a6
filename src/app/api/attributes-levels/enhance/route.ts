import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withA<PERSON><PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: {} // Required for Next.js 15+ type compatibility
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Parse request body with error handling
    const body = await req.json().catch(() => {
      throw new Error("Invalid JSON payload");
    });

    // Make API call with improved timeout handling
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v2/attributes_levels/enhance`,
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 120_000, // 2 minutes timeout (using numeric separator)
        validateStatus: () => true, // Handle all status codes as valid
      }
    );

    // Return successful response
    return NextResponse.json(data);
  } catch (error: unknown) {
    // Enhanced error logging
    console.error(
      "Enhancement Error:",
      error instanceof Error ? error.message : error
    );

    // Type-safe error response
    return NextResponse.json(
      {
        error: ErrorMessage.attributesAndLevelsData,
        details: axios.isAxiosError(error)
          ? error.response?.data || error.message
          : error instanceof Error
            ? error.message
            : "Unknown error",
      },
      { status: 500 }
    );
  }
}) as any; // Temporary type assertion for Auth0 compatibility
