import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import {
  AttributesLevelsRequestSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const POST = withApiAuthRequired(
  withValidation(
    AttributesLevelsRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: {} // Required for Next.js 15+ type compatibility
    ): Promise<NextResponse> {
      try {
        // Get access token with proper response handling
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        // validatedData contains the validated request body

        // Make API call with improved timeout handling
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v2/attributes_levels/consolidated`,
          validatedData,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            timeout: 120_000, // 2 minutes timeout (using numeric separator)
            validateStatus: () => true, // Handle all status codes
          }
        );

        return createSuccessResponse(data);
      } catch (error: unknown) {
        // Enhanced error logging
        console.error(
          "Consolidation Error:",
          error instanceof Error ? error.message : error
        );

        // Type-safe error response using standardized error handling
        if (axios.isAxiosError(error)) {
          return createErrorResponse(
            ErrorMessage.attributesAndLevelsData,
            error.response?.status || 500,
            error.response?.data || error.message
          );
        }

        return createErrorResponse(
          ErrorMessage.attributesAndLevelsData,
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
) as any; // Temporary type assertion for Auth0 compatibility
