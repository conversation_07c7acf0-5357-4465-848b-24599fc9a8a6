import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import {
  UuidSchema,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  context: { params?: Record<string, string | string[]> } // Adjusted context typing for compatibility
): Promise<NextResponse> {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Get ID from route parameters and validate with Zod
    const runID = context.params?.id;

    // Validate the runID parameter using Zod schema
    const validationResult = UuidSchema.safeParse(runID);
    if (!validationResult.success) {
      return createErrorResponse(
        "Invalid run ID format",
        400,
        validationResult.error.errors.map((err) => err.message).join(", ")
      );
    }

    const validatedRunID = validationResult.data;

    // Make API call with improved error handling
    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${validatedRunID}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    return createSuccessResponse(data);
  } catch (error: unknown) {
    console.error(
      "Run Details Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      return createErrorResponse(
        ErrorMessage.runData,
        error.response?.status || 500,
        error.response?.data || error.message
      );
    }

    return createErrorResponse(
      ErrorMessage.runData,
      500,
      error instanceof Error ? error.message : "Unknown error"
    );
  }
}) as any; // Temporary Auth0 compatibility fix
