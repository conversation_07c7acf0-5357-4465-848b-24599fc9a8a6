import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";
import {
  UpdateConfigRequestSchema,
  UuidSchema,
  createSuccessResponse,
  createErrorResponse,
} from "@/lib/schemas";

export const PUT = withApiAuthRequired(async function PUT(
  req: NextRequest,
  context: { params?: Record<string, string | string[]> } // Adjusted context typing for compatibility
): Promise<NextResponse> {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Get ID from route parameters and validate
    const runID = context.params?.id;
    const runIdValidation = UuidSchema.safeParse(runID);

    if (!runIdValidation.success) {
      return createErrorResponse(
        "Invalid run ID format",
        400,
        runIdValidation.error.errors.map((err) => err.message).join(", ")
      );
    }

    // Parse and validate request body
    const requestBody = await req.json();
    const bodyValidation = UpdateConfigRequestSchema.safeParse(requestBody);

    if (!bodyValidation.success) {
      return createErrorResponse(
        "Invalid request body",
        400,
        bodyValidation.error.errors
          .map((err) => `${err.path.join(".")}: ${err.message}`)
          .join(", ")
      );
    }

    const validatedRunID = runIdValidation.data;
    const { config_update } = bodyValidation.data;

    // Make API call with improved error handling
    const { data } = await axios.put(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${validatedRunID}/update_config`,
      { config_update },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    return createSuccessResponse(data);
  } catch (error: unknown) {
    console.error(
      "Config Update Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      return createErrorResponse(
        ErrorMessage.privacyStatus,
        error.response?.status || 500,
        error.response?.data || error.message
      );
    }

    return createErrorResponse(
      ErrorMessage.privacyStatus,
      500,
      error instanceof Error ? error.message : "Unknown error"
    );
  }
}) as any; // Temporary Auth0 compatibility fix
