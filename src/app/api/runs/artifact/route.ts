import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withA<PERSON><PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Proper context typing
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Parse request body
    const body = await req.json();

    if (!body.artifactName) {
      return NextResponse.json(
        { error: "artifactName is required" },
        { status: 400 }
      );
    }

    // Make API call with improved error handling
    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/artifact/${body.artifactName}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    // Construct response with analytics URL
    return NextResponse.json({
      data,
      analyticsURL: process.env.NEXT_PUBLIC_ANALYTICS_URL,
    });
  } catch (error: unknown) {
    console.error(
      "Artifact Fetch Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          error: ErrorMessage.artifactData,
          details: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json(
      { error: ErrorMessage.artifactData },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
