import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withA<PERSON><PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  context: { params?: Record<string, string | string[]> } // Adjusted context typing for compatibility
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Get filename from route parameters
    const file_name = context.params?.filename;

    if (!file_name) {
      return NextResponse.json(
        { error: "File name is required" },
        { status: 400 }
      );
    }

    // Make API call with improved error handling
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/artifact/${file_name}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        responseType: "arraybuffer",
        timeout: 30_000, // 30 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    // Handle JSON responses
    const contentType = response.headers["content-type"];
    if (contentType?.includes("application/json")) {
      const jsonResponse = JSON.parse(Buffer.from(response.data).toString());
      return NextResponse.json(jsonResponse, { status: response.status });
    }

    // Handle binary responses
    return new NextResponse(response.data, {
      status: response.status,
      headers: {
        "Content-Type": contentType || "application/octet-stream",
        "Content-Disposition": `inline; filename="${file_name}"`,
        "Cache-Control": "no-cache",
      },
    });
  } catch (error: unknown) {
    console.error("Artifact Fetch Error:", error);

    // Handle Axios errors
    if (axios.isAxiosError(error)) {
      // Handle 404 responses
      if (error.response?.status === 404) {
        try {
          const errorData =
            error.response.data instanceof Buffer
              ? JSON.parse(error.response.data.toString())
              : error.response.data;
          return NextResponse.json(
            { error: errorData?.error || "Research report not found" },
            { status: 404 }
          );
        } catch {
          return NextResponse.json(
            { error: "Research report not found" },
            { status: 404 }
          );
        }
      }

      // Handle timeout errors
      if (error.code === "ECONNABORTED" || error.response?.status === 504) {
        return NextResponse.json(
          { error: "Request timed out while fetching research report" },
          { status: 504 }
        );
      }

      // Handle unauthorized errors
      if (error.response?.status === 401) {
        return NextResponse.json(
          { error: "Unauthorized access" },
          { status: 401 }
        );
      }
    }

    // Handle generic errors
    return NextResponse.json(
      { error: ErrorMessage.artifactData },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
