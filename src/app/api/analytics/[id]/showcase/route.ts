import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  context: { params?: { id?: string } } // Updated type definition
) {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Safely extract ID with type assertion
    const hbID = context.params?.id as string;

    if (!hbID) {
      return NextResponse.json(
        { error: "Missing analytics ID" },
        { status: 400 }
      );
    }

    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/analytics/${hbID}/showcase`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
        timeout: 10_000,
      }
    );

    return NextResponse.json(data);
  } catch (error) {
    console.error("Analytics error:", error);
    return NextResponse.json(
      { error: ErrorMessage.analyticsData },
      { status: 500 }
    );
  }
}) as any; // Temporary type assertion to resolve Auth0 conflict
