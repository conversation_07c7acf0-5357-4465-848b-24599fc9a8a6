import { NextRequest, NextResponse } from "next/server";
import { S3Client, DeleteObjectCommand } from "@aws-sdk/client-s3";
import { getSession, getAccessToken } from "@auth0/nextjs-auth0";
import { v4 as uuidv4 } from "uuid";

// S3 client setup
const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY!,
  },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME!;

export async function POST(request: NextRequest) {
  const res = new NextResponse();
  try {
    // Get user session
    const session = await getSession(request, res);
    if (!session || !session.user || !session.user.sub) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get access token for external API
    const { accessToken } = await getAccessToken(request, res);
    if (!accessToken) {
      return NextResponse.json({ error: "No access token" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("image") as File;

    if (!file) {
      return NextResponse.json({ error: "No image provided" }, { status: 400 });
    }

    const fileExtension = file.name.split(".").pop();
    const uniqueFilename = `${uuidv4()}.${fileExtension}`;

    // Step 1: Get presigned upload URL
    const presignRes = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/aws_s3/presigned-upload-url?filename=${uniqueFilename}`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );
    if (!presignRes.ok) {
      return NextResponse.json(
        { error: "Failed to get presigned URL" },
        { status: 500 }
      );
    }
    const { url: presignedUrl } = await presignRes.json();

    // Step 2: Upload image to S3 using presigned URL
    const arrayBuffer = await file.arrayBuffer();

    const uploadRes = await fetch(presignedUrl, {
      method: "PUT",
      body: Buffer.from(arrayBuffer),
    });
    if (!uploadRes.ok) {
      const errorText = await uploadRes.text();
      console.error("S3 upload error:", uploadRes.status, errorText);
      return NextResponse.json(
        { error: "Failed to upload image to S3", details: errorText },
        { status: 500 }
      );
    }

    // Remove query params from presignedUrl to get the S3 object URL
    const s3Url = presignedUrl.split("?")[0];

    return NextResponse.json(
      {
        success: true,
        message: "Image uploaded successfully",
        imageUrl: s3Url,
        key: uniqueFilename,
      },
      res
    );
  } catch (error) {
    console.error("Error uploading to S3:", error);
    return NextResponse.json(
      { error: "Failed to upload image" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const res = new NextResponse();
  try {
    // Optional: Add session check for deletion as well for security
    const session = await getSession(request, res);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // We might want to verify if the user owns the key they are trying to delete
    // For now, we proceed if the user is authenticated.

    const { searchParams } = new URL(request.url);
    const key = searchParams.get("key");

    if (!key) {
      return NextResponse.json({ error: "No key provided" }, { status: 400 });
    }

    const params = {
      Bucket: BUCKET_NAME,
      Key: key,
    };

    await s3Client.send(new DeleteObjectCommand(params));

    return NextResponse.json(
      {
        success: true,
        message: "Image deleted successfully",
      },
      res
    );
  } catch (error) {
    console.error("Error deleting from S3:", error);
    return NextResponse.json(
      { error: "Failed to delete image" },
      { status: 500 }
    );
  }
}
