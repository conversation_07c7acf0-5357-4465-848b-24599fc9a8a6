"use client";
import {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  memo,
} from "react";
import ExperimentOverviewCard from "../_components/_experiments/ExperimentOverviewCard";
import { Run } from "../_components/_experiments/types";
import { useUser, withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import SessionContext from "../_components/_util/SessionContext";
import filterRuns from "../api/util/filter_runs";
import {
  AlarmClock,
  ChevronUp,
  RefreshCw,
  Search,
  FileQuestion,
} from "lucide-react";
import { Header } from "../_components/_ui/NotificationCenter";
import * as Sentry from "@sentry/react";
import Confetti from "react-confetti";
import Paginator from "../_components/_ui/Paginator";
import axios from "axios";
import { useSubscription } from "../hooks/useSubscription";
import { ExperimentResultsSkeleton } from "../_components/_ui/ExperimentResultsSkeleton";
import Link from "next/link";

const EXPERIMENT_STATUS = {
  IDLE: "idle",
  QUEUED: "queued",
  RUNNING: "running",
  FINISHED: "finished",
  FAILED: "failed",
};

const ITEMS_PER_PAGE = 10;
const STATUS_CHECK_INTERVAL = 10000; // 10 seconds

const fetcher = async (uri: string, userID: string | null) => {
  try {
    const response = await fetch(uri, {
      method: "POST",
      body: JSON.stringify({ userID: userID }),
    });
    if (!response.ok) {
      const error = new Error("Failed to fetch data");
      Sentry.captureException(error, {
        extra: { uri, userID, status: response.status },
      });
      throw error;
    }
    const data = await response.json();
    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: { uri, userID },
    });
    throw error;
  }
};

export default withPageAuthRequired(
  memo(function ExperimentsPage() {
    const { user } = useUser();
    const [collapsed, setCollapsed] = useState(true);
    const { runs, setRuns } = useContext(SessionContext);
    const statusCheckRef = useRef<NodeJS.Timeout | null>(null);
    const { subscriptionStatus, roles } = useSubscription();
    const [localRuns, setLocalRuns] = useState<Run[] | undefined>(undefined);
    const [activeTab, setActiveTab] = useState("myExperiments");
    const [filteredData, setFilteredData] = useState<any[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);

    const [selectedDropdownItem, setSelectedDropdownItem] =
      useState("All Experiments");

    const [isLoading, setIsLoading] = useState(true);
    const [accessToken, setAccessToken] = useState<string | null>(null);

    const [searchQuery, setSearchQuery] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

    const [showConfetti, setShowConfetti] = useState(false);
    const [confettiExperimentId, setConfettiExperimentId] = useState<
      string | null
    >(null);

    const [queueCount, setQueueCount] = useState(0);
    const [experimentStatus, setExperimentStatus] = useState(
      EXPERIMENT_STATUS.IDLE
    );

    useEffect(() => {
      const fetchToken = async () => {
        try {
          const response = await fetch("/api/token");
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          const data = await response.json();
          setAccessToken(data.accessToken);
        } catch (error) {
          console.error("Failed to fetch access token:", error);
          Sentry.captureException(error);
        }
      };

      fetchToken();
    }, []);

    const pendingExpRunId = localStorage.getItem("pendingExpRunId");

    // Initialize from localStorage
    useEffect(() => {
      const storedStatus =
        localStorage.getItem("experimentStatus") || EXPERIMENT_STATUS.IDLE;
      setExperimentStatus(storedStatus);
    }, []);

    const updateExperimentStatus = useCallback((status: string) => {
      setExperimentStatus(status);
      localStorage.setItem("experimentStatus", status);
    }, []);

    const fetchRunsData = useCallback(async () => {
      setIsLoading(true);
      if (!user?.sub) return;

      try {
        const fetchRuns = await fetcher("/api/runs", user.sub);
        const filteredRuns = filterRuns(fetchRuns, "conjoint");

        setLocalRuns(filteredRuns);
        setRuns(filteredRuns);

        const shownConfettiExperiments = JSON.parse(
          localStorage.getItem("shownConfettiExperiments") || "[]"
        );
        const pendingExpRunId = localStorage.getItem("pendingExpRunId") || "";
        const newRunningExperiment = filteredRuns.find(
          (run) => run.state === "running"
        );
        const newRunId = newRunningExperiment?.id;

        if (newRunId && newRunId !== pendingExpRunId) {
          if (!shownConfettiExperiments.includes(newRunId)) {
            setConfettiExperimentId(newRunId);
            setShowConfetti(true);
            shownConfettiExperiments.push(newRunId);
            localStorage.setItem(
              "shownConfettiExperiments",
              JSON.stringify(shownConfettiExperiments)
            );
          }
          localStorage.setItem("pendingExpRunId", newRunId);
        }

        const runningCount = filteredRuns.filter(
          (run) => run.state === "running"
        ).length;
        localStorage.setItem("runningExperiments", runningCount.toString());
      } catch (error) {
        console.error("Error fetching runs:", error);
      } finally {
        setIsLoading(false);
      }
    }, [user, setRuns]);

    // This function ONLY checks the queue
    const checkQueue = useCallback(async () => {
      if (!user?.sub) return;
      try {
        const queueRes = await axios.get("/api/tasks");
        const queueData = queueRes.data;
        setQueueCount(queueData.length);

        // If the queue is now empty, it means we've moved to the running state
        if (queueData.length === 0) {
          updateExperimentStatus(EXPERIMENT_STATUS.RUNNING);
        }
      } catch (error) {
        console.error("Queue check failed:", error);
        updateExperimentStatus(EXPERIMENT_STATUS.IDLE); // Exit on error
      }
    }, [user, updateExperimentStatus]);

    // This function ONLY checks the status of a running experiment
    const checkRunStatus = useCallback(async () => {
      if (!accessToken || !pendingExpRunId) return;
      try {
        const statusRes = await axios.get(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${pendingExpRunId}/status`,
          {
            headers: { Authorization: `Bearer ${accessToken}` },
          }
        );

        // If the status is no longer "running", the experiment is done.
        if (statusRes.data.status !== "running") {
          updateExperimentStatus(EXPERIMENT_STATUS.IDLE);
          fetchRunsData(); // Fetch final results
        }
      } catch (error) {
        console.error("Status check failed:", error);
        updateExperimentStatus(EXPERIMENT_STATUS.IDLE); // Exit on error
        fetchRunsData();
      }
    }, [accessToken, pendingExpRunId, fetchRunsData, updateExperimentStatus]);

    // Set up periodic, stateful status checks
    useEffect(() => {
      // Clear any existing interval when this effect re-runs or unmounts
      if (statusCheckRef.current) {
        clearInterval(statusCheckRef.current);
        statusCheckRef.current = null;
      }

      // If the status is QUEUED, poll the queue.
      if (experimentStatus === EXPERIMENT_STATUS.QUEUED) {
        checkQueue(); // Check immediately
        statusCheckRef.current = setInterval(checkQueue, STATUS_CHECK_INTERVAL);
      }
      // If the status is RUNNING, poll the run status.
      else if (experimentStatus === EXPERIMENT_STATUS.RUNNING) {
        checkRunStatus(); // Check immediately
        statusCheckRef.current = setInterval(
          checkRunStatus,
          STATUS_CHECK_INTERVAL
        );
      }

      return () => {
        if (statusCheckRef.current) {
          clearInterval(statusCheckRef.current);
        }
      };
    }, [experimentStatus, checkQueue, checkRunStatus]);

    // Main data loading effect
    useEffect(() => {
      if (!accessToken || !user) return;

      // Always fetch the initial list of runs on load.
      fetchRunsData();

      // The polling useEffect will handle the active experiment in parallel.
    }, [accessToken, user, fetchRunsData]);

    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedSearchQuery(searchQuery);
      }, 300);

      return () => clearTimeout(timer);
    }, [searchQuery]);

    const searchExperiments = (query: string, experiments: Run[]): Run[] => {
      if (!experiments?.length) return experiments;
      if (!query.trim()) return experiments;

      const searchTerms = query.toLowerCase().trim().split(/\s+/);

      return experiments.filter((experiment) => {
        const searchableText = [
          experiment.name,
          experiment.id,
          experiment.state,
          experiment.created_at,
          experiment.confidence,
          experiment.question,
          experiment.survey_prompt,
          experiment.r_squared?.toString(),
          experiment.sample_size?.toString(),
          experiment.total_number_of_tasks?.toString(),
          experiment.task_count?.toString(),
          experiment.is_private ? "private" : "public",
        ]
          .filter(Boolean)
          .join(" ")
          .toLowerCase();

        return searchTerms.every((term) => searchableText.includes(term));
      });
    };

    const filterExperiments = useCallback(
      (runs: Run[]) => {
        if (!runs) return [];

        let filtered = searchExperiments(debouncedSearchQuery, runs);

        if (activeTab === "communityExperiments") {
          filtered = filtered.filter((run) => !run.is_private);
        } else {
          if (selectedDropdownItem === "Your Public Experiments") {
            filtered = filtered.filter((run) => !run.is_private);
          } else if (selectedDropdownItem === "Private Experiments") {
            filtered = filtered.filter((run) => run.is_private);
          }
        }

        return filtered;
      },
      [activeTab, selectedDropdownItem, debouncedSearchQuery, searchExperiments]
    );

    const handleTabClick = useCallback((tab: string) => {
      setActiveTab(tab);
      setCurrentPage(1);
    }, []);

    const handleShowAllExperiments = useCallback(() => {
      setCollapsed(!collapsed);
    }, [collapsed]);

    const handleDropdownItemClick = useCallback((item: string) => {
      setSelectedDropdownItem(item);
      setCollapsed(true);
      setCurrentPage(1);
    }, []);

    const filteredLocalRuns = useMemo(
      () => (localRuns ? filterExperiments(localRuns) : []),
      [localRuns, filterExperiments]
    );

    const filteredRuns = useMemo(
      () => filterExperiments(runs),
      [runs, filterExperiments]
    );

    const handleRefreshClick = useCallback(() => {
      // Always refresh the main list of experiments.
      fetchRunsData();

      // Also, immediately trigger a status check if an experiment is active.
      if (experimentStatus === EXPERIMENT_STATUS.QUEUED) {
        checkQueue();
      } else if (experimentStatus === EXPERIMENT_STATUS.RUNNING) {
        checkRunStatus();
      }
    }, [experimentStatus, fetchRunsData, checkQueue, checkRunStatus]);

    const handlePrivacyToggle = (id: string, isPrivate: boolean) => {
      const updatedRuns = runs.map((run) =>
        run.id === id ? { ...run, is_private: isPrivate } : run
      );
      setRuns(updatedRuns);
    };

    const paginatedData = filteredData.slice(
      (currentPage - 1) * ITEMS_PER_PAGE,
      currentPage * ITEMS_PER_PAGE
    );

    useEffect(() => {
      if (runs.length > 0) {
        setFilteredData(runs);
      }
    }, [runs, paginatedData]);

    const handlePageChange = (page: number) => {
      setCurrentPage(page);
    };

    const ReplayTutorials = () => {
      if (window.pendo && window.pendo.isReady) {
        window.pendo.showGuideById("kpKqPpqjYOEUf_o5hXy9GDtUeRM");
      }
    };

    useEffect(() => {
      if (showConfetti) {
        const timer = setTimeout(() => {
          setShowConfetti(false);
          setConfettiExperimentId(null);
        }, 2500);
        return () => clearTimeout(timer);
      }
    }, [showConfetti]);

    return (
      <div className="z-10 py-8 px-10 w-full flex flex-col font-inter">
        {showConfetti && <Confetti gravity={0.3} />}

        <div className="flex justify-between">
          <h1 className="text-text-dark font-medium text-3xl pb-6">
            Experiment Results
          </h1>
          <div className="flex items-center gap-2">
            <div
              className="flex cursor-pointer h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
              onClick={handleRefreshClick}
            >
              <RefreshCw className="w-7 h-6 text-[#868e90]" />
            </div>
            <button
              onClick={ReplayTutorials}
              className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
            >
              Show Tutorial
            </button>
            <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
              <Header />
            </div>
          </div>
        </div>

        <div className="flex w-full justify-between items-center">
          <div className="flex border-b relative border-[#D0D5DD] h-8 text-sm font-semibold">
            <div
              className={`cursor-pointer ${
                activeTab === "myExperiments"
                  ? "border-b text-[#312E81] border-[#312E81]"
                  : "text-[#667085]"
              }`}
              onClick={() => handleTabClick("myExperiments")}
            >
              Your Experiments
            </div>
          </div>
          {activeTab === "myExperiments" && (
            <div className="min-w-fit flex flex-col relative cursor-pointer">
              <div
                className="flex items-center justify-between gap-3 bg-white border-[#D0D5DD] w-full border px-3 py-2 boxshadow-allexps rounded-lg"
                onClick={handleShowAllExperiments}
              >
                {selectedDropdownItem}
                <ChevronUp
                  className={`h-6 w-6 text-chevron transition-all duration-300 ${
                    collapsed ? "rotate-180" : "rotate-0"
                  }`}
                />
              </div>
              {!collapsed && (
                <div className="flex w-full flex-col px-[6px] py-1 absolute boxshadow-dropdown top-12 rounded-lg z-10 bg-white">
                  <div
                    className={`flex justify-start rounded-md w-full px-2 py-3 cursor-pointer ${
                      selectedDropdownItem === "All Experiments"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() => handleDropdownItemClick("All Experiments")}
                  >
                    All Experiments
                  </div>
                  <div
                    className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                      selectedDropdownItem === "Your Public Experiments"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() =>
                      handleDropdownItemClick("Your Public Experiments")
                    }
                  >
                    Your Public Experiments
                  </div>
                  <div
                    className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                      selectedDropdownItem === "Private Experiments"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() =>
                      handleDropdownItemClick("Private Experiments")
                    }
                  >
                    Private Experiments
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {isLoading ? (
          <div className="flex-1 flex items-center justify-center min-h-[calc(100vh-250px)]">
            <ExperimentResultsSkeleton />
          </div>
        ) : (
          <>
            {localRuns !== undefined &&
              localRuns.length === 0 &&
              runs.length === 0 &&
              queueCount === 0 && (
                <div className="flex items-center justify-center mt-4">
                  <div className="flex flex-col gap-4 w-full py-24 items-center text-center border border-card-border rounded-xl bg-white">
                    <FileQuestion className="w-16 h-16 text-gray-400" />
                    <p className="text-text-dark font-medium text-2xl">
                      You have not created any experiments yet.
                    </p>
                    <Link href="/ideation" passHref>
                      <button
                        className="flex bg-[#504D9A] text-white font-inter font-medium text-lg px-4 py-2 rounded-xl items-center justify-center"
                        type="button"
                      >
                        <div className="flex w-fit gap-2 items-center px-2">
                          <p className="whitespace-nowrap">
                            Run your first Experiment
                          </p>
                        </div>
                      </button>
                    </Link>
                  </div>
                </div>
              )}

            {!isLoading &&
              runs.length === 0 &&
              localRuns !== undefined &&
              localRuns.length !== 0 && (
                <div className="flex flex-col gap-4 w-full justify-center items-center text-center">
                  <p className="font-roboto text-text-dark text-xl font-normal">
                    Please wait while your experiment finishes executing.
                  </p>
                </div>
              )}

            {localRuns && localRuns.length >= 5 && (
              <div className="relative flex items-center w-full mt-5 mb-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by experiment name, question, ID (partial ok), Date, Respondents(250), tasks (5), sample size (1250), Variance explained (71), confidence (High/Reasonable), private/public, model (GPT4), state (finished/failed)..."
                  className="w-full px-4 py-2 pl-10 border rounded-lg
                focus:outline-none focus:ring-2 focus:ring-primary-dark
                bg-white transition-all duration-200"
                  spellCheck="false"
                  autoComplete="off"
                />
                <Search className="absolute left-3 w-4 h-4 text-gray-400" />
              </div>
            )}

            {experimentStatus === EXPERIMENT_STATUS.QUEUED && (
              <div className="border mt-4 border-blue-500 text-blue-700 rounded-md px-3 py-2 bg-blue-100 font-semibold mb-2 flex flex-row items-center gap-3">
                <div>
                  <AlarmClock />
                </div>
                <div className="flex flex-col">
                  <div className="py-1">
                    Experiments in Queue ({queueCount}). It will start
                    shortly...
                  </div>
                </div>
              </div>
            )}

            <div className="mb-4 mt-4">
              {filteredLocalRuns && filteredLocalRuns.length !== 0 && (
                <div className="flex flex-col w-full gap-4">
                  {filteredLocalRuns
                    .filter((run: Run) => run.state === "running")
                    .map((run: Run) => (
                      <div key={run.id}>
                        <ExperimentOverviewCard
                          run={run}
                          runCount={runs.length}
                          onPrivacyToggle={handlePrivacyToggle}
                          subscriptionStatus={subscriptionStatus}
                          roles={roles}
                        />
                      </div>
                    ))}
                </div>
              )}
            </div>

            {filteredRuns.length !== 0 && (
              <div className="flex flex-col w-full gap-4">
                {filteredRuns
                  .filter((run: Run) => run.state !== "running")
                  .slice(
                    (currentPage - 1) * ITEMS_PER_PAGE,
                    currentPage * ITEMS_PER_PAGE
                  )
                  .map((run: Run) => {
                    return (
                      <div key={run.id}>
                        <ExperimentOverviewCard
                          run={run}
                          runCount={runs.length}
                          onPrivacyToggle={handlePrivacyToggle}
                          subscriptionStatus={subscriptionStatus}
                          roles={roles}
                        />
                      </div>
                    );
                  })}
              </div>
            )}

            {filteredRuns.length > ITEMS_PER_PAGE && (
              <div className="flex justify-center items-center mt-4">
                <Paginator
                  page={currentPage}
                  setPage={handlePageChange}
                  totalPage={Math.ceil(filteredRuns.length / ITEMS_PER_PAGE)}
                />
              </div>
            )}
          </>
        )}
      </div>
    );
  }),
  {
    returnTo: "/experiments",
  }
);
