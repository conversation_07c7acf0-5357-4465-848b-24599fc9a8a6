// /app/experiments/layout.tsx
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "Experiments | Subconscious",
  description:
    "View and manage all your experiments in one place. Monitor results, analyze data, and gain insights from your completed and ongoing research experiments.",
  openGraph: {
    title: "Your Experiments | Track Your Research Progress",
    description:
      "View and manage all your experiments in one place. Monitor results, analyze data, and gain insights from your completed and ongoing research experiments.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Your Experiments | Track Your Research Progress",
    description:
      "View and manage all your experiments in one place. Monitor results, analyze data, and gain insights.",
  },
};

export default function ExperimentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
