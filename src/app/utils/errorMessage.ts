const supportMessage = `<a href="https://discord.com/invite/3bgj4ZhABz" target="_blank" rel="noopener noreferrer" style="text-decoration: underline;">contact our support team for assistance</a>.`;

export const ErrorMessage = {
  causalityCheck:
    "This prompt is not suitable for analysis using conjoint methods, which are designed to understand individual preferences and trade-offs. View tutorial or use one of the suggestions below:",
  causalityRephrase: `The prompt does not contain a causal relationship. Please edit the prompt and try again. If the issue persists, ${supportMessage}`,
  moderationCheck: `There was a problem checking the prompt for moderation. Please try again in a moment. If the issue persists, ${supportMessage}`,
  moderationFlag: (categories: any) =>
    `The prompt does not comply with our content policy because of ${categories}, please edit the prompt and try again. If the issue persists, ${supportMessage}`,
  traitsFetch: `There was a problem fetching the traits. Please try again in a moment. If the issue persists, ${supportMessage}`,
  attributesFetch: `There was a problem fetching the attributes. Please try again in a moment. If the issue persists, ${supportMessage}`,
  orthogonalCreation: `There was a problem generating orthogonal attributes and levels. Please try again in a moment. If the issue persists, ${supportMessage}`,
  artifactData: `There was a problem fetching the artifact data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  analyticsData: `There was a problem fetching the analytics data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  orthogonalData: `There was a problem fetching the orthogonal data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  attributesAndLevelsData: `There was a problem fetching the attributes and levels data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  causalityData: `There was a problem fetching the causality data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  moderationData: `There was a problem fetching the moderation data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  experimentData: `There was a problem fetching the experiment data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  levelData: `There was a problem fetching the levels data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  runData: `There was a problem fetching the runs data. Please try again in a moment. If the issue persists, ${supportMessage}`,
  privacyStatus: `There was a problem in updating privacy status. Please try again in a moment. If the issue persists, ${supportMessage}`,
  checkoutError: `There was a problem in creating checkout session. Please try again in a moment. If the issue persists, ${supportMessage}`,
  accessTokenError: `There was a problem in getting access token. Please try again in a moment. If the issue persists, ${supportMessage}`,
};
