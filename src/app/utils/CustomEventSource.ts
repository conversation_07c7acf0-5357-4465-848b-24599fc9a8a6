/* eslint-disable no-unused-vars */
//@ts-nocheck

class CustomEventSource {
  private url: string;
  private options: any;
  private eventListeners: { [key: string]: ((event: any) => void)[] };
  private currentEvent: string | undefined;
  private reconnectAttempts: number;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private maxReconnectDelay: number;
  private isConnected: boolean;

  constructor(url: string, options: any = {}) {
    this.url = url;
    this.options = options;
    this.eventListeners = {};
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 100;
    this.reconnectDelay = 1000;
    this.maxReconnectDelay = 30000;
    this.isConnected = false;
    this.init();
  }

  private async init() {
    try {
      await this.connect();
    } catch (error) {
      this.handleError(error);
    }
  }

  private async connect() {
    const response = await fetch(this.url, {
      headers: this.options.headers,
    });

    if (response.ok) {
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      if (reader) {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.createEventSource(reader, decoder);
      }
    } else {
      throw new Error("Network response was not ok.");
    }
  }

  private createEventSource(
    reader: ReadableStreamDefaultReader<Uint8Array>,
    decoder: TextDecoder
  ) {
    const processText = ({
      done,
      value,
    }: {
      done: boolean;
      value: Uint8Array;
    }) => {
      if (done) {
        this.reconnect();
        return;
      }
      const chunk = decoder.decode(value, { stream: true });
      this.parseChunk(chunk);
      reader.read().then(processText);
    };
    reader.read().then(processText);
  }

  private parseChunk(chunk: string) {
    chunk.split("\n").forEach((line) => {
      if (line.startsWith("event:")) {
        this.currentEvent = line.replace("event:", "").trim();
      } else if (line.startsWith("data:")) {
        const data = line.replace("data:", "").trim();
        this.dispatchEvent(this.currentEvent || "message", data);
      }
    });
  }

  private dispatchEvent(type: string, data: string) {
    if (this.eventListeners[type]) {
      this.eventListeners[type].forEach((listener) => listener({ data }));
    }
  }

  public addEventListener(type: string, listener: (event: any) => void) {
    if (!this.eventListeners[type]) {
      this.eventListeners[type] = [];
    }
    this.eventListeners[type].push(listener);
  }

  public removeEventListener(type: string, listener: (event: any) => void) {
    if (!this.eventListeners[type]) return;
    const index = this.eventListeners[type].indexOf(listener);
    if (index !== -1) {
      this.eventListeners[type].splice(index, 1);
    }
  }

  public close() {
    this.isConnected = false;
    this.eventListeners = {};
  }

  private handleError(error: Error) {
    console.error("CustomEventSource error:", error);
    this.reconnect();
  }

  private reconnect() {
    if (!this.isConnected) {
      this.reconnectAttempts++;
      // Calculate delay with a maximum cap
      const delay = Math.min(
        this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1),
        this.maxReconnectDelay
      );

      // No longer check for max attempts - always try to reconnect
      console.log(
        `Attempting reconnection ${this.reconnectAttempts} after ${delay}ms`
      );
      setTimeout(() => this.init(), delay);

      // Optionally dispatch a connection-status event
      this.dispatchEvent(
        "connection-status",
        JSON.stringify({
          connected: false,
          reconnecting: true,
          attempts: this.reconnectAttempts,
        })
      );
    }
  }
}

export default CustomEventSource;
