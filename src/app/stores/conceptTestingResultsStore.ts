import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import { Run } from "../_components/_experiments/types";

interface ConceptTestingResultsState {
  // Data state
  runs: Run[];
  localRuns: Run[];
  queueCount: number;

  // UI state
  isLoading: boolean;
  collapsed: boolean;
  activeTab: string;
  selectedDropdownItem: string;
  currentPage: number;
  searchQuery: string;
  debouncedSearchQuery: string;

  // Confetti state
  showConfetti: boolean;
  confettiExperimentId: string | null;

  // Connection state
  connectionActive: boolean;
  accessToken: string | null;

  // Actions
  setRuns: (runs: Run[]) => void;
  setLocalRuns: (runs: Run[]) => void;
  setQueueCount: (count: number) => void;
  setIsLoading: (loading: boolean) => void;
  setCollapsed: (collapsed: boolean) => void;
  setActiveTab: (tab: string) => void;
  setSelectedDropdownItem: (item: string) => void;
  setCurrentPage: (page: number) => void;
  setSearchQuery: (query: string) => void;
  setDebouncedSearchQuery: (query: string) => void;
  setShowConfetti: (show: boolean) => void;
  setConfettiExperimentId: (id: string | null) => void;
  setConnectionActive: (active: boolean) => void;
  setAccessToken: (token: string | null) => void;

  // Computed getters
  getFilteredRuns: () => Run[];
  getFilteredLocalRuns: () => Run[];

  // Complex actions
  updatePrivacy: (id: string, isPrivate: boolean) => void;
  resetToDefaults: () => void;
}

const initialState = {
  runs: [],
  localRuns: [],
  queueCount: 0,
  isLoading: true,
  collapsed: true,
  activeTab: "myConceptTests",
  selectedDropdownItem: "All Concept Tests",
  currentPage: 1,
  searchQuery: "",
  debouncedSearchQuery: "",
  showConfetti: false,
  confettiExperimentId: null,
  connectionActive: true,
  accessToken: null,
};

export const useConceptTestingResultsStore =
  create<ConceptTestingResultsState>()(
    subscribeWithSelector((set, get) => ({
      ...initialState,

      // Basic setters
      setRuns: (runs) => set({ runs }),
      setLocalRuns: (localRuns) => set({ localRuns }),
      setQueueCount: (queueCount) => set({ queueCount }),
      setIsLoading: (isLoading) => set({ isLoading }),
      setCollapsed: (collapsed) => set({ collapsed }),
      setActiveTab: (activeTab) => set({ activeTab, currentPage: 1 }),
      setSelectedDropdownItem: (selectedDropdownItem) =>
        set({ selectedDropdownItem, collapsed: true, currentPage: 1 }),
      setCurrentPage: (currentPage) => set({ currentPage }),
      setSearchQuery: (searchQuery) => set({ searchQuery }),
      setDebouncedSearchQuery: (debouncedSearchQuery) =>
        set({ debouncedSearchQuery }),
      setShowConfetti: (showConfetti) => set({ showConfetti }),
      setConfettiExperimentId: (confettiExperimentId) =>
        set({ confettiExperimentId }),
      setConnectionActive: (connectionActive) => set({ connectionActive }),
      setAccessToken: (accessToken) => set({ accessToken }),

      // Computed getters
      getFilteredRuns: () => {
        const { runs, activeTab, selectedDropdownItem, debouncedSearchQuery } =
          get();
        return filterConceptTests(
          runs,
          activeTab,
          selectedDropdownItem,
          debouncedSearchQuery
        );
      },

      getFilteredLocalRuns: () => {
        const {
          localRuns,
          activeTab,
          selectedDropdownItem,
          debouncedSearchQuery,
        } = get();
        return filterConceptTests(
          localRuns,
          activeTab,
          selectedDropdownItem,
          debouncedSearchQuery
        );
      },

      // Complex actions
      updatePrivacy: (id, isPrivate) => {
        set((state) => ({
          runs: state.runs.map((run) =>
            run.id === id ? { ...run, is_private: isPrivate } : run
          ),
          localRuns: state.localRuns.map((run) =>
            run.id === id ? { ...run, is_private: isPrivate } : run
          ),
        }));
      },

      resetToDefaults: () => set(initialState),
    }))
  );

// Helper function for filtering concept tests
function filterConceptTests(
  conceptTests: Run[],
  activeTab: string,
  selectedDropdownItem: string,
  searchQuery: string
): Run[] {
  if (!conceptTests?.length) return conceptTests;

  let filtered = conceptTests;

  // Apply search filter
  if (searchQuery.trim()) {
    const searchTerms = searchQuery.toLowerCase().trim().split(/\s+/);
    filtered = filtered.filter((conceptTest) => {
      const searchableText = [
        conceptTest.name,
        conceptTest.id,
        conceptTest.state,
        conceptTest.created_at,
        conceptTest.confidence,
        conceptTest.question,
        conceptTest.survey_prompt,
        conceptTest.r_squared?.toString(),
        conceptTest.sample_size?.toString(),
        conceptTest.total_number_of_tasks?.toString(),
        conceptTest.task_count?.toString(),
        conceptTest.is_private ? "private" : "public",
      ]
        .filter(Boolean)
        .join(" ")
        .toLowerCase();

      return searchTerms.every((term) => searchableText.includes(term));
    });
  }

  // Apply tab and dropdown filters
  if (activeTab === "communityConceptTests") {
    filtered = filtered.filter((run) => !run.is_private);
  } else {
    if (selectedDropdownItem === "Your Public Concept Tests") {
      filtered = filtered.filter((run) => !run.is_private);
    } else if (selectedDropdownItem === "Private Concept Tests") {
      filtered = filtered.filter((run) => run.is_private);
    }
  }

  return filtered;
}

// Selector hooks for optimized re-renders
export const useConceptTestingRuns = () =>
  useConceptTestingResultsStore((state) => state.getFilteredRuns());

export const useConceptTestingLocalRuns = () =>
  useConceptTestingResultsStore((state) => state.getFilteredLocalRuns());

export const useConceptTestingUI = () =>
  useConceptTestingResultsStore((state) => ({
    isLoading: state.isLoading,
    collapsed: state.collapsed,
    activeTab: state.activeTab,
    selectedDropdownItem: state.selectedDropdownItem,
    currentPage: state.currentPage,
    searchQuery: state.searchQuery,
    queueCount: state.queueCount,
    showConfetti: state.showConfetti,
  }));

export const useConceptTestingActions = () =>
  useConceptTestingResultsStore((state) => ({
    setCollapsed: state.setCollapsed,
    setActiveTab: state.setActiveTab,
    setSelectedDropdownItem: state.setSelectedDropdownItem,
    setCurrentPage: state.setCurrentPage,
    setSearchQuery: state.setSearchQuery,
    updatePrivacy: state.updatePrivacy,
  }));
