"use client";

import { useState, useEffect } from "react";
import { useUser } from "@auth0/nextjs-auth0/client";
import * as Sentry from "@sentry/react";
import { getUserSubscription } from "@/app/actions/subscription";
import {
  createCheckoutSession,
  createCustomerPortalSession,
} from "@/app/actions/stripe";
import type { SubscriptionData } from "@/app/actions/subscription/types";
import type {
  CheckoutSessionResponse,
  CustomerPortalResponse,
} from "@/app/actions/stripe/types";

export function useSubscription() {
  const { user, isLoading: isUserLoading } = useUser();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    stripeCustomerId: null,
    subscriptionStatus: null,
    renewalDate: null,
    roles: [],
    isSubscriber: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch subscription data when user is available
  useEffect(() => {
    async function fetchSubscriptionData() {
      if (!user?.sub) return;
      setIsLoading(true);
      setError(null);

      try {
        const data = (await getUserSubscription(user.sub)) as SubscriptionData;
        setSubscriptionData(data);
      } catch (err: any) {
        console.error("Error fetching subscription data:", err);
        setError(err?.message || "Failed to load subscription data");
        Sentry.captureException(err, {
          tags: { action: "fetch_subscription" },
          extra: { userId: user.sub },
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchSubscriptionData();
  }, [user]);

  // Function to handle checkout
  const handleCheckout = async () => {
    if (!subscriptionData.stripeCustomerId) {
      setError("No customer ID available");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { session_id } = (await createCheckoutSession(
        subscriptionData.stripeCustomerId
      )) as CheckoutSessionResponse;

      if (!session_id) {
        throw new Error(
          "No session ID returned from checkout session creation"
        );
      }

      // Load Stripe and redirect
      const stripeJs = await import("@stripe/stripe-js");
      const stripe = await stripeJs.loadStripe(
        process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? ""
      );

      if (!stripe) {
        throw new Error("Failed to load Stripe");
      }

      const result = await stripe.redirectToCheckout({ sessionId: session_id });

      if (result.error) {
        throw new Error(
          result.error.message || "Error redirecting to checkout"
        );
      }
    } catch (err: any) {
      console.error("Error during checkout:", err);
      setError(err?.message || "Failed to start checkout process");
      Sentry.captureException(err, {
        tags: { action: "checkout" },
        extra: { customerId: subscriptionData.stripeCustomerId },
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle customer portal
  const handleCustomerPortal = async () => {
    if (!subscriptionData.stripeCustomerId) {
      setError("No customer ID available");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { url } = (await createCustomerPortalSession(
        subscriptionData.stripeCustomerId
      )) as CustomerPortalResponse;

      if (!url) {
        throw new Error(
          "No URL returned from customer portal session creation"
        );
      }

      window.location.href = url;
    } catch (err: any) {
      console.error("Error opening customer portal:", err);
      setError(err?.message || "Failed to open customer portal");
      Sentry.captureException(err, {
        tags: { action: "customer_portal" },
        extra: { customerId: subscriptionData.stripeCustomerId },
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    ...subscriptionData,
    isLoading: isLoading || isUserLoading,
    error,
    handleCheckout,
    handleCustomerPortal,
    refresh: async () => {
      if (user?.sub) {
        setIsLoading(true);
        setError(null);
        try {
          const data = (await getUserSubscription(
            user.sub
          )) as SubscriptionData;
          setSubscriptionData(data);
        } catch (err: any) {
          console.error("Error refreshing subscription data:", err);
          setError(err?.message || "Failed to refresh subscription data");
          Sentry.captureException(err, {
            tags: { action: "refresh_subscription" },
            extra: { userId: user.sub },
          });
        } finally {
          setIsLoading(false);
        }
      }
    },
  };
}
