/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import React from "react";
import { withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import ApiAccessComponent from "../_components/_settings/ApiAccessComponent";
import { Check, ArrowUpRight } from "lucide-react";
import { useSubscription } from "@/app/hooks/useSubscription";

export default withPageAuthRequired(
  function SettingsPage() {
    const {
      subscriptionStatus,
      renewalDate,
      handleCheckout,
      handleCustomerPortal,
    } = useSubscription();

    const featuresData = [
      { text: "Run unlimited experiments" },
      { text: "Predict market impacts" },
      { text: "Tailor unique and effective messaging" },
      { text: "Measure demand, market share, price elasticity" },
    ];

    return (
      <div className="z-10 py-8 px-10 w-full flex flex-col font-roboto gap-3">
        <h1 className="text-text-dark font-medium text-3xl pb-6">Settings</h1>
        <div>
          <div className="flex items-center gap-4">
            <p className="text-[#101828] text-xl font-medium ">Your plan</p>
            <div className="flex items-center justify-center py-1 px-3 rounded-md bg-[#FFF5F1] text-[#BB0F04] text-sm font-medium ">
              {subscriptionStatus || "Loading..."}
            </div>
          </div>
          <div className="flex w-full gap-8 mt-4">
            <div className="flex flex-col w-1/2 rounded-xl subscription-box border-[#D0D5DD] border-2 p-6 px-10 bg-white">
              <p className="font-semibold text-lg text-[#101828]">
                Plan features
              </p>
              {featuresData.map((feature, index) => (
                <div key={index} className="flex items-center gap-2 mt-4">
                  <Check className="w-4 h-4 font-semibold" />
                  <p className=" font-normal text-base ">{feature.text}</p>
                </div>
              ))}
            </div>
            <div className="flex flex-col w-1/2 rounded-xl subscription-box border-[#D0D5DD] border-2  bg-white">
              <div className=" pt-8 ">
                <p className="font-semibold px-8 text-lg text-[#101828]">
                  Subconscious AI Standard Plan
                </p>
                {subscriptionStatus === "Active" && (
                  <p className="text-sm px-8 text-[#667085] mt-2 font-normal">
                    Renews on {renewalDate}
                  </p>
                )}

                <div className="flex items-end px-8 mt-4">
                  <p className=" text-[32px] font-medium text-[#312E81]">
                    $1,000
                  </p>
                  <p className="pb-1 text-base font-medium text-[#667085]">
                    /month
                  </p>
                </div>

                {subscriptionStatus === "Active" ? (
                  <div className="settings-footer mt-6 py-4 flex justify-end gap-6 px-8">
                    <div
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={handleCustomerPortal}
                    >
                      <p className="font-semibold text-sm text-[#312E81]">
                        Manage Plan
                      </p>
                      <ArrowUpRight className="w-4 h-4 font-semibold text-[#312E81]" />
                    </div>
                  </div>
                ) : (
                  <div className="flex mt-6 px-8 w-full">
                    <button
                      onClick={handleCheckout}
                      className=" text-base font-semibold flex w-1/3 py-3 px-2 justify-center items-center rounded-lg bg-[#312E81] text-white"
                    >
                      Subscribe
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <div>
          <ApiAccessComponent />
        </div>
      </div>
    );
  },
  {
    returnTo: "/settings",
  }
);
