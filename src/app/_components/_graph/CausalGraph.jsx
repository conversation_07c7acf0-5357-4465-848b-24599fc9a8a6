/* eslint-disable prettier/prettier */
import React, { useRef, useEffect, useState, useMemo } from "react";

import * as THREE from "three";
import dynamic from "next/dynamic";

const ForceGraph3D = dynamic(() => import("react-force-graph-3d"), {
  ssr: false,
});

const getLinkMaterial = (link) => {
  const material = new THREE.LineBasicMaterial({
    color: link.value >= 0 ? "#8ACC49" : "red",
    opacity: getNodeOpacity(link.p_value), // Using the same opacity logic for nodes
    transparent: true,
  });
  return material;
};

const generateNodeLabelHTML = (node) => {
  const primaryDarkColor = "#1C1D47";
  if (node.id === "center_node") {
    return `
        <div
          style="
            color: white;
            font-weight: 400;
            max-width: 500px;
            font-size: 16px;
            font-family: 'Arial', sans-serif;
            border-radius: 8px;
            padding: 8px;
            border: 1px solid #027EAC;
            background-color: ${primaryDarkColor};
          "
        >
          <p style="color: white;">
            Dependent Variable
          </p>
        </div>
      `;
  } else {
    // Return the usual label for other nodes
    return `
        <div
          style="
            color: white;
            font-weight: 400;
            max-width: 500px;
            font-size: 16px;
            font-family: 'Arial', sans-serif;
            border-radius: 8px;
            padding: 8px;
            border: 1px solid #027EAC;
            background-color: ${primaryDarkColor};
          "
        >
          <p style="color: white;">
            <span style="color: white; font-weight: 600;">Attribute:</span> ${node.type || "N/A"}
          </p>
          <p>
            <span style="color: white; font-weight: 600;">Level:</span> ${node.name || "N/A"}
          </p>
          <p style="color: white;">
            <span style="color: white; font-weight: 600;">Importance:</span> ${typeof node.value === "number" ? node.value.toFixed(2) : "N/A"}
          </p>
          <p>
            <span style="color: white; font-weight: 600;">T-Stat:</span> ${node.p_value}
          </p>
        </div>
      `;
  }
};

const getLinkColorAndOpacity = (link) => {
  const opacity = getNodeOpacity(link.p_value);
  const color = link.value >= 0 ? "#8ACC49" : "red";
  return `rgba(${new THREE.Color(color).r * 255}, ${new THREE.Color(color).g * 255}, ${new THREE.Color(color).b * 255}, ${opacity})`;
};

const getLinkColor = (link) => {
  const color = link.value >= 0 ? "#8ACC49" : "red";
  return color;
};

const greekAlphabets = [
  "α",
  "β",
  "γ",
  "δ",
  "ε",
  "ζ",
  "η",
  "θ",
  "ι",
  "κ",
  "λ",
  "μ",
  "ν",
  "ξ",
  "ο",
  "π",
  "ρ",
  "σ",
  "τ",
  "υ",
  "φ",
  "χ",
  "ψ",
  "ω",
];

const getNodeOpacity = (p_value) => {
  if (Math.abs(p_value) >= 1.96) {
    return 1;
  } else if (Math.abs(p_value) >= 1.64 && Math.abs(p_value) < 1.96) {
    return 0.85;
  } else if (Math.abs(p_value) >= 1.44 && Math.abs(p_value) < 1.64) {
    return 0.7;
  } else if (Math.abs(p_value) >= 1.28 && Math.abs(p_value) < 1.44) {
    return 0.5;
  } else {
    return 0.3;
  }
};

const CausalGraph = ({ graphWidth, graphHeight, data }) => {
  const [selectedNode, setSelectedNode] = useState(null);
  const [expandedAttributes, setExpandedAttributes] = useState({});
  const [selectedAttribute, setSelectedAttribute] = useState(null);

  const toggleAttribute = (attrName) => {
    setExpandedAttributes((prev) => ({ ...prev, [attrName]: !prev[attrName] }));
  };

  const graphRef = useRef(null);

  useEffect(() => {
    if (graphRef.current) {
      graphRef.current.cameraPosition({ z: 50 });
    }
  }, []);

  const attributeColors = [
    "#4A90E2", // Sky Blue
    "#ff1493", // Deep Pink
    "#191970", // Midnight Blue
    "#36454f", // Charcoal Grey
    "#008080", // Teal
    "#8b008b", // Dark Magenta
    "#708090", // Slate Grey
    "#6b8e23", // Olive Drab
    "#a0522d", // Sienna

    "#2e8b57", // Sea Green
  ];

  if (!data || !data.attributes) {
    return;
  }

  const nodes = [{ id: data.id, type: data.type, value: 0, color: "black" }];
  const links = [];
  const attributeDetails = {};
  data.attributes.forEach((attr, attrIdx) => {
    const color = attributeColors[attrIdx % attributeColors.length];
    attributeDetails[attr.name] = [];
    attr.levels.forEach((level, levelIdx) => {
      const nodeId = `${greekAlphabets[attrIdx % greekAlphabets.length]}${
        levelIdx + 1
      }`;
      if (
        !(level.value > 0 && level.value - level.std_error * 1.96 < 0) &&
        !(level.value < 0 && level.value + level.std_error * 1.96 > 0) &&
        !(level.value === 0) &&
        !level.id.includes("Base")
      ) {
        nodes.push({
          id: nodeId,
          type: attr.name,
          name: level.id,
          value: level.value,
          p_value: parseFloat(level.p_value).toFixed(2),
          color: color,
          label: `Value: ${parseFloat(level.value).toFixed(2)}`,
        });
        attributeDetails[attr.name].push({
          id: nodeId,
          value: level.value,
          p_value: level.p_value,
          description: `${level.id}: ${parseFloat(level.value).toFixed(2)}`,
        });
        links.push({
          source: nodeId,
          target: data.id,
          value: level.value,
          p_value: level.p_value,
        });
      } else {
        attributeDetails[attr.name].push({
          id: nodeId,
          value: level.value,
          p_value: level.p_value,

          description: !level.id.includes("Base")
            ? `${level.id}: ${parseFloat(level.value).toFixed(2)}`
            : `${level.id}`,
        });
      }
    });
  });

  const getLinkWidth = (link) => {
    if (
      parseFloat(Math.abs(link.value)) >= 0.8 &&
      parseFloat(Math.abs(link.value)) <= 1
    ) {
      return 5;
    } else if (
      parseFloat(Math.abs(link.value)) >= 0.6 &&
      parseFloat(Math.abs(link.value)) < 0.8
    ) {
      return 4;
    } else if (
      parseFloat(Math.abs(link.value)) >= 0.4 &&
      parseFloat(Math.abs(link.value)) < 0.6
    ) {
      return 3;
    } else if (
      parseFloat(Math.abs(link.value)) >= 0.25 &&
      parseFloat(Math.abs(link.value)) < 0.4
    ) {
      return 2;
    }
    return 1;
  };

  const getArrowLength = (link) => {
    const linkWidth = getLinkWidth(link);
    if (linkWidth > 1) {
      return linkWidth * 4;
    }
    return 0;
  };

  const particleDirection = (link) => {
    return parseFloat(link.value) > 0 ? 0.007 : +0.0015;
  };

  const handleNodeClick = (node) => {
    const distance = 40;
    const distRatio = 1 + distance / Math.hypot(node.x, node.y, node.z);

    const newPos =
      node.x || node.y || node.z
        ? {
            x: node.x * distRatio,
            y: node.y * distRatio,
            z: node.z * distRatio,
          }
        : { x: 0, y: 0, z: distance }; // special case if node is at (0,0,0)
  };

  const Legend = () => (
    <div
      style={{
        position: "absolute",
        top: "10px",
        left: "10px",
        backgroundColor: "white",
        padding: "10px",
        fontSize: "14px",
        border: "1px solid black",

        maxWidth: "200px",
      }}
    >
      <h3>
        <span style={{ textDecoration: "underline", fontWeight: "bold" }}>
          Attributes
        </span>
      </h3>

      {Object.keys(attributeDetails).map((attrName, index) => (
        <div
          key={index}
          onMouseDown={() => toggleAttribute(attrName)}
          style={{
            cursor: "pointer",
            color: attributeColors[index % attributeColors.length],
          }}
        >
          {attrName}
          <span style={{ marginLeft: "5px" }}>
            {expandedAttributes[attrName] ? "-" : "+"}
          </span>
          {expandedAttributes[attrName] && (
            <div style={{ paddingLeft: "20px" }}>
              {attributeDetails[attrName].length ? (
                attributeDetails[attrName].map((detail) => {
                  if (detail.description.includes("(Base)")) {
                    return (
                      <div key={detail.id} className="font-bold ">
                        {detail.description}
                      </div>
                    );
                  } else {
                    return <div key={detail.id}>{detail.description}</div>;
                  }
                })
              ) : (
                <div>No Significance</div>
              )}
            </div>
          )}
        </div>
      ))}

      <div style={{ marginTop: "10px" }}>
        <h3>
          <span style={{ textDecoration: "underline", fontWeight: "bold" }}>
            Link Colors
          </span>
        </h3>
        <div>
          <span style={{ color: "green" }}>Green:</span> Positive Effect
        </div>
        <div>
          <span style={{ color: "red" }}>Red:</span> Negative Effect
        </div>
      </div>
    </div>
  );

  // const handleNodeClick = (node) => {
  //   setSelectedNode(selectedNode && selectedNode.id === node.id ? null : node);
  // };

  // const data_graph = useMemo(() => {
  const graphData = { nodes, links };
  //   return graphData;
  // }, [nodes, links]);

  // const data_graph = { nodes, links };

  return (
    <div
      style={{
        width: graphWidth,
        height: graphHeight,
        backgroundColor: "white",
        overflow: "hidden",
        display: "flex",
        justifyContent: "center",
      }}
    >
      <ForceGraph3D
        ref={graphRef}
        graphData={graphData}
        backgroundColor="white"
        onNodeClick={handleNodeClick}
        zoom={5}
        nodeThreeObject={(node) => {
          const nodeSize = node.id === "center_node" ? 4 : getLinkWidth(node);
          const geometry = new THREE.SphereGeometry(nodeSize, 16, 16);
          const opacity = getNodeOpacity(node.p_value);
          const material = new THREE.MeshBasicMaterial({
            color: node.color,
            opacity: opacity,
            transparent: true,
          });
          const mesh = new THREE.Mesh(geometry, material);
          const group = new THREE.Group();
          group.add(mesh);
          return group;
        }}
        nodeAutoColorBy="type"
        linkDirectionalArrowColor={(link) => getLinkColorAndOpacity(link)}
        linkMaterial={getLinkMaterial}
        linkWidth={getLinkWidth}
        linkOpacity={1}
        nodeLabel={generateNodeLabelHTML}
        linkDirectionalArrowLength={(link) => getArrowLength(link)}
        linkDirectionalArrowRelPos={1}
        linkDirectionalParticles={3}
        linkDirectionalParticleSpeed={particleDirection}
        linkDirectionalParticleColor={getLinkColor}
        linkDirectionalParticleWidth={(link) => getLinkWidth(link) * 1.3}
        linkCurvature={0.25}
      />
      <Legend />
    </div>
  );
};

export default CausalGraph;
