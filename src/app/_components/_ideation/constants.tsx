const FOCUS_CARDS = [
  {
    title: "Engineering",
    subtitle: "What would make you use the product?",
    active: true,
  },
  {
    title: "Product",
    subtitle: "What features would delight you?",
    active: false,
  },
  {
    title: "Marketing",
    subtitle: "What makes you aware of and desire the product?",
    active: false,
  },
  {
    title: "Sales",
    subtitle: "What would make you purchase the product?",
    active: false,
  },
  {
    title: "Customer success",
    subtitle: "Would you share the product with friends?",
    active: false,
  },
];

import {
  CircleDollarSign,
  Microscope,
  Scale,
  Users,
  Heart,
} from "lucide-react";

// Define specialist traits by country
const US_SPECIALIST_TRAITS = {
  Economist: [
    "Vehicles In Household",
    "Census Division",
    "Family Size",
    "Migration Status",
    "Price Sensitivity",
    "Brand Loyalty",
    "Quality Orientation",
    "Risk Aversion",
    "Time Orientation",
    "Materialism",
    "Big five conscientiousness",
    "Political ideology",
  ],
  PoliticalScientist: [
    "Census Division",
    "Migration Status",
    "Hispanic Latino",
    "Veteran Status",
    "Environmental Concern",
    "Innovation Adoption",
    "Self-Monitoring",
    "Self-Construal",
    "Ethnocentrism",
    "8 values diplomatic",
    "8 values civil",
    "Political ideology",
  ],
  Psychologist: [
    "Marital Status",
    "Family Size",
    "Health Insurance Coverage",
    "Health Consciousness",
    "Convenience Priority",
    "Need for Uniqueness",
    "Self-Monitoring",
    "Impulsiveness",
    "Need for Cognition",
    "Big five extroversion",
    "Big five openness",
    "Big five conscientiousness",
    "Big five emotional stability",
  ],
  SocialScientist: [
    "Census Division",
    "Hispanic Latino",
    "Migration Status",
    "Speaks English",
    "Environmental Concern",
    "Innovation Adoption",
    "Self-Construal",
    "Need for Cognition",
    "Ethnocentrism",
    "8 values diplomatic",
    "8 values civil",
    "Political ideology",
  ],
  AffectiveScientist: [
    "Marital Status",
    "Family Size",
    "Health Consciousness",
    "Convenience Priority",
    "Self-Monitoring",
    "Impulsiveness",
    "Big five emotional stability",
    "Emotional valence",
    "Big five agreeableness",
  ],
};

const NON_US_SPECIALIST_TRAITS = {
  Economist: [
    "Education Level",
    "Home Ownership",
    "Household Size",
    "Number of Vehicles in Household",
    "Price Sensitivity",
    "Brand Loyalty",
    "Quality Orientation",
    "Risk Aversion",
    "Time Orientation",
    "Materialism",
    "Big five conscientiousness",
    "Political ideology",
  ],
  PoliticalScientist: [
    "Education Level",
    "Race",
    "Hispanic/Latino Origin",
    "Environmental Concern",
    "Innovation Adoption",
    "Self-Monitoring",
    "Self-Construal",
    "Ethnocentrism",
    "8 values diplomatic",
    "8 values civil",
    "Political ideology",
  ],
  Psychologist: [
    "Gender",
    "Age",
    "Education Level",
    "Health Consciousness",
    "Convenience Priority",
    "Need for Uniqueness",
    "Self-Monitoring",
    "Impulsiveness",
    "Need for Cognition",
    "Big five extroversion",
    "Big five openness",
    "Big five conscientiousness",
    "Big five emotional stability",
  ],
  SocialScientist: [
    "Race",
    "Hispanic/Latino Origin",
    "Household Size",
    "Environmental Concern",
    "Innovation Adoption",
    "Self-Construal",
    "Need for Cognition",
    "Ethnocentrism",
    "8 values diplomatic",
    "8 values civil",
    "Political ideology",
  ],
  AffectiveScientist: [
    "Gender",
    "Age",
    "Health Consciousness",
    "Convenience Priority",
    "Self-Monitoring",
    "Impulsiveness",
    "Big five emotional stability",
    "Emotional valence",
    "Big five agreeableness",
  ],
};

const SPECIALISTS = [
  {
    title: "Economist",
    icon: <CircleDollarSign strokeWidth={1} />,
    traits: [], // Will be populated based on country selection
  },
  {
    title: "Political Scientist",
    icon: <Scale strokeWidth={1} />,
    traits: [], // Will be populated based on country selection
  },
  {
    title: "Psychologist",
    icon: <Microscope strokeWidth={1} />,
    traits: [], // Will be populated based on country selection
  },
  {
    title: "Social Scientist",
    icon: <Users strokeWidth={1} />,
    traits: [], // Will be populated based on country selection
  },
  {
    title: "Affective Scientist",
    icon: <Heart strokeWidth={1} />,
    traits: [], // Will be populated based on country selection
  },
];

export { SPECIALISTS, US_SPECIALIST_TRAITS, NON_US_SPECIALIST_TRAITS };
