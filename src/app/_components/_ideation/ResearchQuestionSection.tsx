import React, { ReactNode, useEffect, useState } from "react";
import { UserSearch } from "lucide-react";

interface ResearchQuestionSectionProps {
  question: string;
  heading?: string;
  subheading?: string | ReactNode;
  headingId?: string;
  className?: string;
}

const ResearchQuestionSection: React.FC<ResearchQuestionSectionProps> = ({
  question,
  heading,
  subheading,
  headingId,
  className = "",
}) => {
  const isShortQuestion = question.length < 100;

  return (
    <div className={`flex flex-col gap-2 items-stretch w-full ${className}`}>
      {/* Question Card */}
      <div className="border-[#D0D5DD] border-2 rounded-lg py-3 text-center bg-blue-50">
        <h3 className="font-semibold text-primary flex justify-center">
          <UserSearch />
          <span className="ml-1">Question</span>
        </h3>
        <h3
          className={`text-text-placeholder p-5 font-roboto text-lg font-normal ${
            isShortQuestion
              ? "text-center" // Centered for short questions
              : "text-justify" // Justified for longer ones
          }`}
        >
          {question}
        </h3>
      </div>

      {/* Main Heading */}
      {heading && (
        <h2
          id={headingId}
          className="text-text-dark font-roboto font-semibold text-2xl"
        >
          {heading}
        </h2>
      )}

      {/* Subheading - can be string or ReactNode */}
      {subheading && (
        <div>
          {typeof subheading === "string" ? (
            <h3 className="text-text-placeholder font-roboto text-lg font-normal">
              {subheading}
            </h3>
          ) : (
            subheading
          )}
        </div>
      )}
    </div>
  );
};

export default ResearchQuestionSection;
