import React from "react";
import { TriangleAlert } from "lucide-react";

export interface ErrorDisplayProps {
  message: string | null;
  showTryAgain?: boolean;
  onTryAgain?: () => void;
  className?: string;
  fixed?: boolean;
}

/**
 * Reusable error display component for ideation pages
 * Can be used both inline and fixed at bottom of page
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  message,
  showTryAgain = false,
  onTryAgain,
  className = "",
  fixed = false,
}) => {
  if (!message) return null;

  const baseClasses =
    "w-full flex gap-2 text-error-red items-center bg-red-100 p-3 rounded-md";
  const fixedClasses = fixed ? "sticky bottom-[60px]" : "";
  const combinedClasses = `${baseClasses} ${fixedClasses} ${className}`;

  return (
    <div className={combinedClasses}>
      <div className="flex items-start w-full">
        <div className="flex-shrink-0 mt-0.5">
          <TriangleAlert className="h-5 w-5 text-red-500" />
        </div>
        <div className="ml-3 flex-1">
          <h3
            className="text-sm font-medium text-red-800"
            dangerouslySetInnerHTML={{ __html: message }}
          ></h3>
          {showTryAgain && onTryAgain && (
            <div className="mt-1 text-sm text-red-700">
              Please adjust the traits and{" "}
              <button
                type="button"
                onClick={onTryAgain}
                className="text-sm font-medium text-red-800 hover:text-red-600 focus:outline-none"
              >
                Try Again
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay;
