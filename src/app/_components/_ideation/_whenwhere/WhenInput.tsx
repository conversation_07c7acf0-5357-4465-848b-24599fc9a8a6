import React from "react";
import { Info } from "lucide-react";
import Tooltip from "../../_util/ToolTip";
import { WhenInputProps } from "./types";

const WhenInput: React.FC<WhenInputProps> = ({ value, onChange }) => {
  return (
    <div className="flex flex-col items-stretch gap-1.5">
      <div className="flex gap-2 items-center">
        <div
          id="ideation-when-guide-9"
          className="group relative flex gap-2 font-medium text-text-placeholder text-sm py-1 justify-start items-center cursor-pointer"
        >
          When
          <Info />
          <Tooltip
            message="Select the year for your experiment. You can choose any year (e.g., 2023, nights, weekends, holidays, early '90s, etc.)"
            position="right"
          />
        </div>
      </div>

      <input
        className="px-3.5 py-2.5 font-inter placeholder-text-text-placeholder text-base rounded-xl border border-[#D0D5DD] focus:border-2 focus:border-primary focus:outline-none focus:ring-0)"
        type="text"
        value={value}
        onChange={onChange}
        placeholder="Enter a time period"
      />
    </div>
  );
};

export default WhenInput;
