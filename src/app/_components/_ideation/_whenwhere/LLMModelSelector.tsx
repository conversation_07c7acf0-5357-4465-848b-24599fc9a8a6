import { ChevronDown } from "lucide-react";
import { Menu, Transition } from "@headlessui/react";
import { Fragment, useContext } from "react";
import { LLMModel } from "../objects";
import ExperimentCreationContext from "../ExperimentCreationContext";

interface LLMModelSelectorProps {
  models: LLMModel[];
}

const LLMModelSelector = ({ models }: LLMModelSelectorProps) => {
  const { selectedLlmModel, setSelectedLlmModel } = useContext(
    ExperimentCreationContext
  );
  return (
    <Menu as="div" className="relative w-full">
      <p className="font-medium mb-2 text-text-placeholder text-sm">
        Select an LLM model
      </p>
      <Menu.Button className="w-full bg-white px-3.5 py-2.5 font-inter text-base rounded-xl border border-[#D0D5DD] focus:border-2 focus:border-primary focus:outline-none focus:ring-0">
        <div className="flex flex-row justify-center items-center ">
          <p className="text-base text-text-dark font-roboto flex-grow text-start">
            {selectedLlmModel ? selectedLlmModel.name : "gpt3"}
          </p>
          <ChevronDown className="w-5 h-5" />
        </div>
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-full h-56 overflow-y-scroll origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          {models.map((model, idx) => {
            return (
              <Menu.Item key={idx}>
                <div
                  className="flex flex-row gap-2 px-3 py-2 hover:cursor-pointer"
                  onMouseDown={() => setSelectedLlmModel(model)}
                >
                  <p>{model.name}</p>
                </div>
              </Menu.Item>
            );
          })}
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default LLMModelSelector;
