import React from "react";
import { States } from "../_whenwhere/PopulationTraitsData";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StateSelectorProps {
  selectedState: string | null;
  onStateChange: (state: string) => void;
}

const StateSelector: React.FC<StateSelectorProps> = ({
  selectedState,
  onStateChange,
}) => {
  const handleValueChange = (value: string) => {
    onStateChange(value);
  };

  return (
    <div className="flex flex-col items-stretch gap-1.5">
      <div className="flex gap-2 items-center">
        <p className="font-medium text-text-placeholder text-sm py-1">
          Select State (Optional)
        </p>
      </div>
      <Select value={selectedState || ""} onValueChange={handleValueChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select State" />
        </SelectTrigger>
        <SelectContent>
          {Object.keys(States)
            .filter((state) => state !== "USA")
            .map((state) => (
              <SelectItem key={state} value={state}>
                {state}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default StateSelector;
