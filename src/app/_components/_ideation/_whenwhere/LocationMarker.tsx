import Image from "next/image";
import { useEffect } from "react";
import { LocationMarkerProps } from "./types";

const LocationMarker = ({ x, y }: LocationMarkerProps) => {
  function updateLocationMarker() {
    let marker = document.getElementById("marker");
    if (marker !== undefined && marker?.style !== undefined) {
      marker!.style.left = x + "px";
      marker!.style.top = y + "px";
    }
  }

  useEffect(() => {
    if (x !== undefined && y !== undefined) updateLocationMarker();
  }, [x, y]);

  return (
    <div
      id="marker"
      style={{ position: "absolute", height: 16, width: 16 }}
      className={`${x === undefined && y === undefined ? "hidden" : "block"}`}
    >
      <Image
        src="/selected-country.svg"
        height={16}
        width={16}
        alt="Country Marker"
      />
    </div>
  );
};

export default LocationMarker;
