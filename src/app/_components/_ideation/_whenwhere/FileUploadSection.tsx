import React from "react";
import { Download } from "lucide-react";
import { FileUpload } from "../../_util/FileUpload";
import Tooltip from "../../_util/ToolTip";
import { FileUploadSectionProps } from "./types";

const FileUploadSection: React.FC<FileUploadSectionProps> = ({
  onFileSelect,
  onFileRemove,
  onDownloadSample,
  selectedFile,
  error,
}) => {
  return (
    <div className="w-full">
      <div className="flex items-center justify-end mb-2">
        <div className="relative flex items-center group">
          <button
            onClick={onDownloadSample}
            className="flex items-center gap-1 text-primary hover:text-primary-dark"
          >
            <Download size={18} />
            <span className="text-sm">Download sample CSV</span>
          </button>
          <Tooltip
            message="Download an example CSV file to see how to structure your attributes data"
            position="top"
          />
        </div>
      </div>
      <FileUpload
        onFileSelect={onFileSelect}
        onFileRemove={onFileRemove}
        selectedFile={selectedFile}
        error={error}
      />
    </div>
  );
};

export default FileUploadSection;
