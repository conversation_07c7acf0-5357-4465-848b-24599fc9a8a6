import { UseFileUploadProps, UseFileUploadReturn } from "../types";

/**
 * Custom hook to handle file upload functionality for CSV attribute files
 */
export function useFileUpload({
  fileState,
  setFileState,
  setDisplayAttributes,
}: UseFileUploadProps): UseFileUploadReturn {
  const handleRemoveFile = () => {
    setFileState({ file: null, data: null, error: null });
    setDisplayAttributes([]);
  };

  const handleFileUpload = (file: File) => {
    if (!file.name.endsWith(".csv")) {
      setFileState({
        file: null,
        data: null,
        error: "Please upload a valid CSV file",
      });
      return;
    }
    setFileState({ ...fileState, file, error: null });

    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        let text = e.target?.result as string;

        // Ensure proper text decoding
        text = new TextDecoder("utf-8", { fatal: false }).decode(
          new TextEncoder().encode(text)
        );

        // Ensure special characters (², ³, ±) are not lost
        text = text
          .replace(/m�/g, "m²") // Fix cases where "m²" is misrepresented
          .replace(/m�/g, "m³") // Fix cases where "m³" is misrepresented
          .replace(/±/g, "±") // Preserve ± symbols properly
          .replace(/�/g, ""); // Remove only genuinely corrupted characters

        const rows = text
          .split(/\r?\n/)
          .map((row) => row.trim())
          .filter((row) => row);

        // Validate CSV structure
        if (rows.length < 2) {
          setFileState({
            file: null,
            data: null,
            error: "CSV file must contain headers and at least one data row",
          });
          return;
        }

        // Check for required headers
        const headers = rows[0]
          .split(",")
          .map((h) => h.trim().replace(/^"|"$/g, ""));

        // Attributes is required
        if (!headers.includes("Attributes")) {
          setFileState({
            file: null,
            data: null,
            error:
              "CSV format is incorrect. Missing required 'Attributes' column",
          });
          return;
        }

        // At least one Level column is required
        const levelColumns = ["Level1", "Level2", "Level3", "Level4", "Level5"];
        const hasAnyLevelColumn = levelColumns.some((header) =>
          headers.includes(header)
        );

        if (!hasAnyLevelColumn) {
          setFileState({
            file: null,
            data: null,
            error:
              "CSV format is incorrect. At least one Level column (Level1-Level5) is required",
          });
          return;
        }

        const attributes = rows
          .slice(1)
          .slice(0, 7)
          .map((row) => {
            const values = row.match(/(".*?"|[^",]+)(?=\s*,|\s*$)/g) || [];
            const [attribute, ...levels] = values.map((val) =>
              val.replace(/^"|"$/g, "").trim()
            );

            return {
              active: true,
              attribute: attribute,
              attribute_type: "text",
              levels: levels
                .filter((level) => level)
                .map((level) => ({
                  level: level,
                  active: true,
                })),
            };
          });

        setDisplayAttributes(attributes);
      } catch (error) {
        console.error("Error processing CSV file:", error);
        setFileState({
          file: null,
          data: null,
          error: "Failed to read CSV file",
        });
      }
    };

    reader.onerror = () => {
      console.error("Error reading file");
      setFileState({
        file: null,
        data: null,
        error: "Failed to read CSV file",
      });
    };

    reader.readAsText(file, "utf-8"); // Read CSV file with UTF-8 encoding
  };

  const handleDownloadSampleCSV = () => {
    const link = document.createElement("a");
    link.href = "/attribute_example.csv";
    link.download = "attribute_example.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return {
    handleFileUpload,
    handleRemoveFile,
    handleDownloadSampleCSV,
  };
}
