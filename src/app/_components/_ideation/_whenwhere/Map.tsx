import { useEffect, useState } from "react";
import LocationMarker from "./LocationMarker";
import { MapProps } from "./types";

const Map = ({ selected, x, y }: MapProps) => {
  const [placeLocationMarker, setPlaceLocationMarker] = useState(selected);

  useEffect(() => {
    setPlaceLocationMarker(selected);
  }, [selected]);

  return (
    <div className="relative h-[260px] w-[800px] bg-cover bg-center bg-no-repeat bg-map">
      {placeLocationMarker && <LocationMarker x={x} y={y} />}
    </div>
  );
};

export default Map;
