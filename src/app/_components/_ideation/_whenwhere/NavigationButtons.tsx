import React from "react";
import { NavigationButtonsProps } from "./types";

const NavigationButtons: React.FC<NavigationButtonsProps> = ({
  onBack,
  onContinue,
  isContinueDisabled,
}) => {
  return (
    <div className="sticky bottom-0 flex w-full justify-between bg-background py-2">
      <button
        className="font-inter text-base font-medium py-2.5 px-6 bg-white hover:bg-secondary-grey border border-card-border shadow-sm rounded-lg"
        onClick={onBack}
      >
        Back
      </button>
      <button
        className={`font-inter text-base font-semibold py-2.5 ${
          isContinueDisabled
            ? "bg-[#ECEDFB] cursor-not-allowed"
            : "bg-primary hover:bg-primary-dark"
        } text-white rounded-lg shadow-sm w-[170px]`}
        onClick={onContinue}
        disabled={isContinueDisabled}
      >
        Continue
      </button>
    </div>
  );
};

export default NavigationButtons;
