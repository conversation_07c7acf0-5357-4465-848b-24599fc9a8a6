export const education = [
  "Less than high school",
  "High School but no diploma",
  "High School Diploma",
  "Some College",
  "Associates",
  "Bachelors",
  "Masters",
  "PhD",
];
export const gender = ["Male", "Female"];

export const children = ["0", "1", "2", "3", "4+"];

export const race = [
  "Mixed race",
  "White",
  "African American",
  "Asian or Pacific Islander",
  "Other race",
];

export const States = {
  Alabama: {
    "Household income": {
      max: 1084000,
      min: 0,
      "90th_percentile": 180084,
      "95th_percentile": 234560,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "Less than high school",
      "High School but no diploma",
      "Bachelors",
      "Associates",
      "PhD",
      "Masters",
    ],
    "Number of Children Category": ["0", "3", "2", "1", "4+"],
    "Racial Group": [
      "Mixed race",
      "White",
      "African American",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  Alaska: {
    "Household income": {
      max: 941800,
      min: 0,
      "90th_percentile": 213854,
      "95th_percentile": 274300,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 89,
      min: 18,
      "90th_percentile": 72,
      "95th_percentile": 76,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "Associates",
      "High School but no diploma",
      "Bachelors",
      "Masters",
      "Less than high school",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "4+", "1", "3"],
    "Racial Group": [
      "White",
      "Asian or Pacific Islander",
      "African American",
      "Other race",
      "Mixed race",
    ],
  },
  Arizona: {
    "Household income": {
      max: 1996000,
      min: 0,
      "90th_percentile": 213000,
      "95th_percentile": 287579,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "Less than high school",
      "High School but no diploma",
      "Associates",
      "Bachelors",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "3", "1", "2", "4+"],
    "Racial Group": [
      "Other race",
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  Arkansas: {
    "Household income": {
      max: 1268800,
      min: 0,
      "90th_percentile": 163600,
      "95th_percentile": 216740,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "Less than high school",
      "Associates",
      "High School but no diploma",
      "Masters",
      "PhD",
      "Bachelors",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "African American",
      "Asian or Pacific Islander",
      "White",
      "Other race",
      "Mixed race",
    ],
  },
  California: {
    "Household income": {
      max: 2247000,
      min: 0,
      "90th_percentile": 294760,
      "95th_percentile": 408600,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School Diploma",
      "Less than high school",
      "Some College",
      "High School but no diploma",
      "Associates",
      "PhD",
      "Bachelors",
      "Masters",
    ],
    "Number of Children Category": ["0", "1", "3", "2", "4+"],
    "Racial Group": [
      "White",
      "Other race",
      "Mixed race",
      "African American",
      "Asian or Pacific Islander",
    ],
  },
  Colorado: {
    "Household income": {
      max: 1752800,
      min: 0,
      "90th_percentile": 258000,
      "95th_percentile": 350000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 92,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 80,
    },
    "Education Level": [
      "Some College",
      "High School but no diploma",
      "High School Diploma",
      "Less than high school",
      "Associates",
      "Bachelors",
      "PhD",
      "Masters",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "Mixed race",
      "White",
      "Asian or Pacific Islander",
      "Other race",
      "African American",
    ],
  },
  Connecticut: {
    "Household income": {
      max: 2310000,
      min: 0,
      "90th_percentile": 301000,
      "95th_percentile": 444259,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 96,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Some College",
      "High School but no diploma",
      "Bachelors",
      "Masters",
      "Less than high school",
      "Associates",
      "High School Diploma",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Other race",
      "Asian or Pacific Islander",
      "Mixed race",
    ],
  },
  Delaware: {
    "Household income": {
      max: 1053330,
      min: 0,
      "90th_percentile": 226700,
      "95th_percentile": 300000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Bachelors",
      "High School Diploma",
      "Some College",
      "High School but no diploma",
      "Masters",
      "Less than high school",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "4+", "1", "3", "2"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  "District of Columbia": {
    "Household income": {
      max: 1767500,
      min: 0,
      "90th_percentile": 391300,
      "95th_percentile": 521149,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 72,
      "95th_percentile": 79,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "Masters",
      "Bachelors",
      "Less than high school",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "African American",
      "White",
      "Other race",
      "Asian or Pacific Islander",
      "Mixed race",
    ],
  },
  Florida: {
    "Household income": {
      max: 2481200,
      min: 0,
      "90th_percentile": 219400,
      "95th_percentile": 310000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 78,
      "95th_percentile": 83,
    },
    "Education Level": [
      "High School Diploma",
      "Masters",
      "High School but no diploma",
      "Some College",
      "Associates",
      "Less than high school",
      "Bachelors",
      "PhD",
    ],
    "Number of Children Category": ["0", "3", "2", "1", "4+"],
    "Racial Group": [
      "African American",
      "White",
      "Asian or Pacific Islander",
      "Other race",
      "Mixed race",
    ],
  },
  Georgia: {
    "Household income": {
      max: 1484000,
      min: 0,
      "90th_percentile": 224000,
      "95th_percentile": 301000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 92,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 80,
    },
    "Education Level": [
      "High School but no diploma",
      "Some College",
      "High School Diploma",
      "Masters",
      "Bachelors",
      "Less than high school",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "1", "3", "4+"],
    "Racial Group": [
      "African American",
      "White",
      "Asian or Pacific Islander",
      "Other race",
      "Mixed race",
    ],
  },
  Hawaii: {
    "Household income": {
      max: 1072600,
      min: 0,
      "90th_percentile": 289310,
      "95th_percentile": 387840,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 97,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 82,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "Associates",
      "High School but no diploma",
      "Bachelors",
      "Masters",
      "PhD",
      "Less than high school",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "Mixed race",
      "Asian or Pacific Islander",
      "African American",
      "Other race",
    ],
  },
  Idaho: {
    "Household income": {
      max: 1295000,
      min: 0,
      "90th_percentile": 190720,
      "95th_percentile": 252500,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "Associates",
      "Less than high school",
      "Bachelors",
      "PhD",
      "Masters",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Asian or Pacific Islander",
      "Other race",
      "Mixed race",
    ],
  },
  Illinois: {
    "Household income": {
      max: 1848000,
      min: 0,
      "90th_percentile": 232400,
      "95th_percentile": 320000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "PhD",
      "High School but no diploma",
      "Some College",
      "High School Diploma",
      "Less than high school",
      "Bachelors",
      "Associates",
      "Masters",
    ],
    "Number of Children Category": ["0", "3", "1", "2", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  Indiana: {
    "Household income": {
      max: 1352520,
      min: 0,
      "90th_percentile": 182700,
      "95th_percentile": 245000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "PhD",
      "Less than high school",
      "Bachelors",
      "Masters",
      "Associates",
    ],
    "Number of Children Category": ["0", "1", "3", "2", "4+"],
    "Racial Group": [
      "White",
      "Asian or Pacific Islander",
      "African American",
      "Mixed race",
      "Other race",
    ],
  },
  Iowa: {
    "Household income": {
      max: 1268000,
      min: 0,
      "90th_percentile": 177400,
      "95th_percentile": 230000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 83,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "PhD",
      "Masters",
      "Bachelors",
      "Associates",
      "Less than high school",
    ],
    "Number of Children Category": ["0", "3", "1", "2", "4+"],
    "Racial Group": [
      "African American",
      "White",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  Kansas: {
    "Household income": {
      max: 1210000,
      min: 0,
      "90th_percentile": 187300,
      "95th_percentile": 250000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "Bachelors",
      "Less than high school",
      "High School but no diploma",
      "Associates",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  Kentucky: {
    "Household income": {
      max: 1373000,
      min: 0,
      "90th_percentile": 179000,
      "95th_percentile": 239800,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School but no diploma",
      "High School Diploma",
      "Some College",
      "Less than high school",
      "Associates",
      "Bachelors",
      "PhD",
      "Masters",
    ],
    "Number of Children Category": ["0", "1", "2", "4+", "3"],
    "Racial Group": [
      "White",
      "Other race",
      "Mixed race",
      "African American",
      "Asian or Pacific Islander",
    ],
  },
  Louisiana: {
    "Household income": {
      max: 1392300,
      min: 0,
      "90th_percentile": 181000,
      "95th_percentile": 239500,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School but no diploma",
      "Less than high school",
      "High School Diploma",
      "Associates",
      "Bachelors",
      "Some College",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "1", "4+", "3"],
    "Racial Group": [
      "African American",
      "White",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  Maine: {
    "Household income": {
      max: 936000,
      min: 0,
      "90th_percentile": 185840,
      "95th_percentile": 251000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 78,
      "95th_percentile": 83,
    },
    "Education Level": [
      "Some College",
      "High School but no diploma",
      "High School Diploma",
      "Less than high school",
      "Bachelors",
      "Associates",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "Mixed race",
      "White",
      "Asian or Pacific Islander",
      "African American",
      "Other race",
    ],
  },
  Maryland: {
    "Household income": {
      max: 1701000,
      min: 0,
      "90th_percentile": 295500,
      "95th_percentile": 380000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "PhD",
      "Bachelors",
      "High School but no diploma",
      "Associates",
      "Less than high school",
      "Masters",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "African American",
      "White",
      "Asian or Pacific Islander",
      "Mixed race",
      "Other race",
    ],
  },
  Massachusetts: {
    "Household income": {
      max: 2344000,
      min: 0,
      "90th_percentile": 307400,
      "95th_percentile": 417800,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "Associates",
      "Bachelors",
      "Less than high school",
      "High School but no diploma",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "3", "1", "4+"],
    "Racial Group": [
      "White",
      "Other race",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  Michigan: {
    "Household income": {
      max: 2007000,
      min: 0,
      "90th_percentile": 195600,
      "95th_percentile": 259000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "Less than high school",
      "Masters",
      "Bachelors",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "4+", "2", "3"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  Minnesota: {
    "Household income": {
      max: 1323000,
      min: 0,
      "90th_percentile": 220000,
      "95th_percentile": 295000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "Bachelors",
      "High School but no diploma",
      "Associates",
      "Masters",
      "Less than high school",
      "PhD",
    ],
    "Number of Children Category": ["0", "3", "1", "2", "4+"],
    "Racial Group": [
      "White",
      "Other race",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  Mississippi: {
    "Household income": {
      max: 802300,
      min: 0,
      "90th_percentile": 162000,
      "95th_percentile": 212600,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "Less than high school",
      "Associates",
      "Bachelors",
      "High School but no diploma",
      "PhD",
      "Masters",
    ],
    "Number of Children Category": ["0", "2", "3", "1", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  Missouri: {
    "Household income": {
      max: 1575000,
      min: 0,
      "90th_percentile": 184300,
      "95th_percentile": 250000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "High School but no diploma",
      "Masters",
      "Less than high school",
      "Bachelors",
      "PhD",
      "Associates",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "African American",
      "Mixed race",
      "White",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  Montana: {
    "Household income": {
      max: 1036000,
      min: 0,
      "90th_percentile": 194430,
      "95th_percentile": 253000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "Less than high school",
      "Masters",
      "Associates",
      "Bachelors",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "4+", "2", "3"],
    "Racial Group": [
      "White",
      "Mixed race",
      "Other race",
      "African American",
      "Asian or Pacific Islander",
    ],
  },
  Nebraska: {
    "Household income": {
      max: 1253500,
      min: 0,
      "90th_percentile": 188000,
      "95th_percentile": 246470,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "High School but no diploma",
      "High School Diploma",
      "Some College",
      "Less than high school",
      "Bachelors",
      "PhD",
      "Associates",
      "Masters",
    ],
    "Number of Children Category": ["0", "2", "1", "4+", "3"],
    "Racial Group": [
      "White",
      "African American",
      "Asian or Pacific Islander",
      "Other race",
      "Mixed race",
    ],
  },
  Nevada: {
    "Household income": {
      max: 2121450,
      min: 0,
      "90th_percentile": 206000,
      "95th_percentile": 278020,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 92,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 80,
    },
    "Education Level": [
      "High School but no diploma",
      "High School Diploma",
      "Some College",
      "Bachelors",
      "PhD",
      "Less than high school",
      "Associates",
      "Masters",
    ],
    "Number of Children Category": ["0", "1", "3", "4+", "2"],
    "Racial Group": [
      "African American",
      "White",
      "Asian or Pacific Islander",
      "Mixed race",
      "Other race",
    ],
  },
  "New Hampshire": {
    "Household income": {
      max: 1253000,
      min: 0,
      "90th_percentile": 250000,
      "95th_percentile": 330919,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Bachelors",
      "Less than high school",
      "Masters",
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  "New Jersey": {
    "Household income": {
      max: 1628100,
      min: 0,
      "90th_percentile": 313000,
      "95th_percentile": 419000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Less than high school",
      "Some College",
      "High School Diploma",
      "Bachelors",
      "High School but no diploma",
      "PhD",
      "Masters",
      "Associates",
    ],
    "Number of Children Category": ["0", "4+", "3", "1", "2"],
    "Racial Group": [
      "African American",
      "White",
      "Other race",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  "New Mexico": {
    "Household income": {
      max: 1344000,
      min: 0,
      "90th_percentile": 179350,
      "95th_percentile": 243530,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School but no diploma",
      "Masters",
      "Bachelors",
      "Some College",
      "High School Diploma",
      "Less than high school",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "3", "4+", "1", "2"],
    "Racial Group": [
      "Other race",
      "White",
      "Mixed race",
      "African American",
      "Asian or Pacific Islander",
    ],
  },
  "New York": {
    "Household income": {
      max: 2221200,
      min: 0,
      "90th_percentile": 264300,
      "95th_percentile": 371000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "High School Diploma",
      "High School but no diploma",
      "Some College",
      "Less than high school",
      "Bachelors",
      "Masters",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "2", "4+", "3"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  "North Carolina": {
    "Household income": {
      max: 1860000,
      min: 0,
      "90th_percentile": 204000,
      "95th_percentile": 282100,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Less than high school",
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "Associates",
      "Bachelors",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "3", "1", "4+"],
    "Racial Group": [
      "African American",
      "White",
      "Mixed race",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  "North Dakota": {
    "Household income": {
      max: 1584000,
      min: 0,
      "90th_percentile": 195500,
      "95th_percentile": 252849,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Some College",
      "High School but no diploma",
      "Bachelors",
      "High School Diploma",
      "Less than high school",
      "Masters",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "4+", "3", "1"],
    "Racial Group": [
      "White",
      "Asian or Pacific Islander",
      "Mixed race",
      "Other race",
      "African American",
    ],
  },
  Ohio: {
    "Household income": {
      max: 1459000,
      min: 0,
      "90th_percentile": 197100,
      "95th_percentile": 262600,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "High School Diploma",
      "Associates",
      "Some College",
      "Less than high school",
      "High School but no diploma",
      "Masters",
      "PhD",
      "Bachelors",
    ],
    "Number of Children Category": ["0", "1", "3", "2", "4+"],
    "Racial Group": [
      "White",
      "Mixed race",
      "Asian or Pacific Islander",
      "African American",
      "Other race",
    ],
  },
  Oklahoma: {
    "Household income": {
      max: 1355000,
      min: 0,
      "90th_percentile": 168800,
      "95th_percentile": 227000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 80,
    },
    "Education Level": [
      "High School but no diploma",
      "High School Diploma",
      "Some College",
      "Masters",
      "Less than high school",
      "Bachelors",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "3", "2", "4+"],
    "Racial Group": [
      "Mixed race",
      "White",
      "African American",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  Oregon: {
    "Household income": {
      max: 1479600,
      min: 0,
      "90th_percentile": 223800,
      "95th_percentile": 304000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "High School but no diploma",
      "Bachelors",
      "Masters",
      "PhD",
      "Associates",
      "Less than high school",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "Other race",
      "Mixed race",
      "African American",
      "Asian or Pacific Islander",
    ],
  },
  Pennsylvania: {
    "Household income": {
      max: 1523000,
      min: 0,
      "90th_percentile": 213675,
      "95th_percentile": 290000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 95,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 82,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "Less than high school",
      "High School but no diploma",
      "PhD",
      "Masters",
      "Bachelors",
      "Associates",
    ],
    "Number of Children Category": ["0", "3", "2", "1", "4+"],
    "Racial Group": [
      "White",
      "Mixed race",
      "Asian or Pacific Islander",
      "African American",
      "Other race",
    ],
  },
  "Rhode Island": {
    "Household income": {
      max: 1010000,
      min: 0,
      "90th_percentile": 240500,
      "95th_percentile": 310000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Some College",
      "Masters",
      "High School Diploma",
      "Associates",
      "High School but no diploma",
      "Less than high school",
      "Bachelors",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "1", "3", "4+"],
    "Racial Group": [
      "Asian or Pacific Islander",
      "White",
      "African American",
      "Mixed race",
      "Other race",
    ],
  },
  "South Carolina": {
    "Household income": {
      max: 1367000,
      min: 0,
      "90th_percentile": 191450,
      "95th_percentile": 259300,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "Bachelors",
      "Associates",
      "High School but no diploma",
      "PhD",
      "Less than high school",
      "Masters",
    ],
    "Number of Children Category": ["0", "2", "1", "3", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  "South Dakota": {
    "Household income": {
      max: 1082000,
      min: 0,
      "90th_percentile": 182185,
      "95th_percentile": 241000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 84,
    },
    "Education Level": [
      "High School Diploma",
      "Bachelors",
      "Some College",
      "High School but no diploma",
      "Associates",
      "Less than high school",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "3", "2", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Other race",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  Tennessee: {
    "Household income": {
      max: 1086000,
      min: 0,
      "90th_percentile": 194000,
      "95th_percentile": 265674,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "High School but no diploma",
      "Bachelors",
      "PhD",
      "Masters",
      "Less than high school",
      "Associates",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  Texas: {
    "Household income": {
      max: 1881000,
      min: 0,
      "90th_percentile": 228000,
      "95th_percentile": 312500,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 92,
      min: 18,
      "90th_percentile": 74,
      "95th_percentile": 80,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "High School but no diploma",
      "Less than high school",
      "Bachelors",
      "Associates",
      "PhD",
      "Masters",
    ],
    "Number of Children Category": ["0", "2", "1", "3", "4+"],
    "Racial Group": [
      "White",
      "Mixed race",
      "African American",
      "Other race",
      "Asian or Pacific Islander",
    ],
  },
  Utah: {
    "Household income": {
      max: 1739000,
      min: 0,
      "90th_percentile": 232000,
      "95th_percentile": 316500,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 92,
      min: 18,
      "90th_percentile": 73,
      "95th_percentile": 79,
    },
    "Education Level": [
      "High School Diploma",
      "Associates",
      "Bachelors",
      "Masters",
      "Some College",
      "PhD",
      "High School but no diploma",
      "Less than high school",
    ],
    "Number of Children Category": ["0", "2", "1", "4+", "3"],
    "Racial Group": [
      "White",
      "Mixed race",
      "Other race",
      "Asian or Pacific Islander",
      "African American",
    ],
  },
  Vermont: {
    "Household income": {
      max: 758000,
      min: 0,
      "90th_percentile": 207000,
      "95th_percentile": 273000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 93,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Some College",
      "Bachelors",
      "High School Diploma",
      "PhD",
      "Less than high school",
      "High School but no diploma",
      "Associates",
      "Masters",
    ],
    "Number of Children Category": ["0", "2", "3", "1", "4+"],
    "Racial Group": [
      "Asian or Pacific Islander",
      "White",
      "Mixed race",
      "African American",
      "Other race",
    ],
  },
  Virginia: {
    "Household income": {
      max: 1772500,
      min: 0,
      "90th_percentile": 271940,
      "95th_percentile": 360219,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "Bachelors",
      "High School Diploma",
      "High School but no diploma",
      "Less than high school",
      "Associates",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "1", "3", "4+"],
    "Racial Group": [
      "White",
      "Asian or Pacific Islander",
      "African American",
      "Mixed race",
      "Other race",
    ],
  },
  Washington: {
    "Household income": {
      max: 2195000,
      min: 0,
      "90th_percentile": 272100,
      "95th_percentile": 385000,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 81,
    },
    "Education Level": [
      "Some College",
      "High School Diploma",
      "Masters",
      "Associates",
      "High School but no diploma",
      "Less than high school",
      "Bachelors",
      "PhD",
    ],
    "Number of Children Category": ["0", "3", "2", "1", "4+"],
    "Racial Group": [
      "Other race",
      "African American",
      "White",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  "West Virginia": {
    "Household income": {
      max: 1034500,
      min: 0,
      "90th_percentile": 166040,
      "95th_percentile": 214549,
    },
    Gender: ["Female", "Male"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 77,
      "95th_percentile": 82,
    },
    "Education Level": [
      "Some College",
      "Bachelors",
      "High School Diploma",
      "High School but no diploma",
      "Less than high school",
      "Masters",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "2", "4+", "1", "3"],
    "Racial Group": [
      "African American",
      "White",
      "Other race",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  Wisconsin: {
    "Household income": {
      max: 1086000,
      min: 0,
      "90th_percentile": 190000,
      "95th_percentile": 250889,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 94,
      min: 18,
      "90th_percentile": 76,
      "95th_percentile": 81,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "High School but no diploma",
      "Less than high school",
      "Bachelors",
      "Masters",
      "Associates",
      "PhD",
    ],
    "Number of Children Category": ["0", "3", "1", "2", "4+"],
    "Racial Group": [
      "African American",
      "White",
      "Asian or Pacific Islander",
      "Mixed race",
      "Other race",
    ],
  },
  Wyoming: {
    "Household income": {
      max: 896000,
      min: 0,
      "90th_percentile": 180000,
      "95th_percentile": 232000,
    },
    Gender: ["Male", "Female"],
    Age: {
      max: 92,
      min: 18,
      "90th_percentile": 75,
      "95th_percentile": 80,
    },
    "Education Level": [
      "High School Diploma",
      "Some College",
      "High School but no diploma",
      "Less than high school",
      "Associates",
      "Bachelors",
      "Masters",
      "PhD",
    ],
    "Number of Children Category": ["0", "1", "2", "3", "4+"],
    "Racial Group": [
      "White",
      "Other race",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
    ],
  },
  USA: {
    "Household income": {
      max: 2481200, // Florida
      min: 0,
      "90th_percentile": 307400, // Massachusetts
      "95th_percentile": 417800, // Massachusetts
    },
    Gender: gender,
    Age: {
      max: 95, // Florida, New York, Pennsylvania, etc.
      min: 18,
      "90th_percentile": 78, // Florida
      "95th_percentile": 83, // Florida
    },
    "Education Level": education,
    "Number of Children Category": children,
    "Racial Group": race,
  },
};
