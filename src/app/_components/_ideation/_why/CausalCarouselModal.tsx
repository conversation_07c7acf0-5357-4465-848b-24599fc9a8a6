/* eslint-disable react/jsx-key */
import { Fragment, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import {
  CausalCarouselIcon1,
  CausalCarouselIcon2,
  CausalCarouselIcon3,
  ModalClosureXIcon,
} from "../../../../../public/icons/CausalCarouselIcons";

interface CausalCarouselModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
}

const CausalCarouselModal = ({
  showModal,
  setShowModal,
}: CausalCarouselModalProps) => {
  const [currentIcon, setCurrentIcon] = useState(1);
  const icons = [
    <CausalCarouselIcon1 />,
    <CausalCarouselIcon2 />,
    <CausalCarouselIcon3 />,
  ];

  const handleContinue = () => {
    if (currentIcon < icons.length) {
      setCurrentIcon(currentIcon + 1);
    }
  };

  const handleBack = () => {
    if (currentIcon > 1) {
      setCurrentIcon(currentIcon - 1);
    }
  };

  return (
    <Transition.Root show={showModal} as={Fragment}>
      <Dialog
        as="div"
        static
        className="fixed z-50 inset-0 overflow-y-auto flex items-center justify-center"
        onClose={() => setShowModal(false)}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Dialog.Overlay className="fixed inset-0 bg-[#00000099] bg-opacity-75 transition-opacity" />
        </Transition.Child>
        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enterTo="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100 translate-y-0 sm:scale-100"
          leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div className="flex flex-col gap-5 justify-between bg-white rounded-lg text-left shadow-xl transform transition-all sm:align-middle sm:max-w-lg sm:w-full p-6 relative">
            <button
              onMouseDown={() => setShowModal(false)}
              className="absolute top-0 right-0 m-5"
            >
              <ModalClosureXIcon />
            </button>
            <div>{icons[currentIcon - 1]}</div>
            <div className="grid grid-cols-[170px_1fr_170px] items-center gap-6 grid-flow-col">
              <div>
                {currentIcon > 1 && (
                  <button
                    className="font-inter text-base font-semibold py-2.5 bg-white hover:bg-secondary-grey text-dark border border-dark rounded-lg shadow-sm w-[170px] h-[44px]"
                    onMouseDown={handleBack}
                  >
                    <span id="back-question">{"Back"}</span>
                  </button>
                )}
              </div>
              <div className="flex justify-center gap-3 ">
                {icons.map((_, index) => (
                  <div
                    key={index}
                    className={`h-2 w-2 rounded-full ${currentIcon === index + 1 ? "bg-gray-800" : "bg-gray-400"}`}
                  />
                ))}
              </div>
              <button
                className="font-inter text-base font-semibold py-2.5 bg-primary hover:bg-primary-dark text-white rounded-lg shadow-sm w-[170px]"
                onMouseDown={() => {
                  if (currentIcon === icons.length) {
                    setShowModal(false);
                  } else {
                    handleContinue();
                  }
                }}
              >
                <span id="continue-question">{"Continue"}</span>
              </button>
            </div>
          </div>
        </Transition.Child>
      </Dialog>
    </Transition.Root>
  );
};

export default CausalCarouselModal;
