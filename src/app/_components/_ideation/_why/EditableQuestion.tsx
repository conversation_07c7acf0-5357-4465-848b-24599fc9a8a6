import React from "react";
import { ArrowRight } from "lucide-react";
import { CausalQuestion } from "../ExperimentCreationContext";

interface EditableQuestionProps {
  question: CausalQuestion;
  editingQuestionId: string | null;
  editedText: string;
  onEditStart: (question: CausalQuestion) => void;
  onEditSave: (question: CausalQuestion) => void;
  onEditCancel: () => void;
  onEditChange: (text: string) => void;
  onQuestionSelect: (question: CausalQuestion) => void;
}

const EditableQuestion: React.FC<EditableQuestionProps> = ({
  question,
  editingQuestionId,
  editedText,
  onEditStart,
  onEditSave,
  onEditCancel,
  onEditChange,
  onQuestionSelect,
}) => {
  const isEditing = editingQuestionId === question.text;

  return (
    <div
      className={`w-full p-4 border rounded-lg flex flex-col group ${
        isEditing
          ? "ring-2 ring-[#1c1d47] bg-white"
          : "hover:border-[#1c1d47]/20"
      }`}
    >
      <div className="flex-1">
        {isEditing ? (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-600 mb-2">
              Edit Question
            </label>
            <textarea
              value={editedText}
              onChange={(e) => onEditChange(e.target.value)}
              className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#1c1d47] text-lg font-medium min-h-[100px]"
              autoFocus
            />
            <div className="flex gap-2 mt-2">
              <button
                onClick={() => onEditSave(question)}
                className="px-4 py-2 bg-[#1c1d47] text-white rounded-lg hover:bg-[#1c1d47]/80 text-sm flex items-center gap-2"
              >
                Save & Continue
                <ArrowRight size={16} />
              </button>
              <button
                onClick={onEditCancel}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <>
            <p className="font-medium text-lg mb-4">{question.text}</p>
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">
                Attributes
              </h4>
              <div className="flex flex-wrap gap-2">
                {question.attributes.map((attr, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-gray-100 rounded-full text-sm"
                  >
                    {attr}
                  </span>
                ))}
              </div>
            </div>
            <div className="flex justify-between items-center mt-6 pt-4 border-t">
              <button
                onClick={() => onEditStart(question)}
                className="flex items-center gap-2 text-gray-500 hover:text-[#1c1d47] px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z" />
                </svg>
                Edit Question
              </button>
              <button
                onClick={() => onQuestionSelect(question)}
                disabled={editingQuestionId !== null}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors group relative ${
                  editingQuestionId !== null
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "text-white bg-[#1c1d47] hover:bg-[#1c1d47]/90"
                }`}
              >
                <span>Continue with this question</span>
                <ArrowRight size={20} />
                <div className="absolute bottom-full mb-2 right-0 w-48 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                  Select to measure the effect of these attributes on decision
                  making
                </div>
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default EditableQuestion;
