/* eslint-disable no-unused-vars */
import React from "react";
import { ArrowRight } from "lucide-react";

interface QuestionProps {
  textColor: string;
  bgColor: string;
  question: string;
  onSelect: (question: string) => void;
}

interface SuggestionQuestionsProps {
  suggestion: string[];
  onSuggestionClick: (question: string) => void; // This is the function that will be called when the user clicks on a suggestion
}

const Question = ({
  textColor,
  bgColor,
  question,
  onSelect,
}: QuestionProps) => {
  return (
    <div className="flex flex-row p-3 justify-between bg-background rounded-lg hover:bg-gray-50 transition-all duration-200">
      <div className="flex text-text-dark items-center font-roboto text-lg font-normal">
        <p>{question}</p>
      </div>
      <div className="flex-shrink-0 justify-end">
        <div className="flex items-center h-full">
          <button
            onMouseDown={() => onSelect(question)}
            className="focus:outline-none"
            aria-label="Select suggestion"
          >
            <div className="border rounded-full p-2 bg-secondary-grey hover:bg-[#C2C1DA] transition-all duration-200">
              <ArrowRight className="w-3 h-3 text-primary font-roboto font-medium " />
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

const SuggestionQuestions = ({
  suggestion,
  onSuggestionClick,
}: SuggestionQuestionsProps) => {
  if (!suggestion || suggestion.length === 0) {
    return (
      <div className="flex flex-col p-4 bg-white gap-3 rounded-xl border border-card-border/40 text-justify">
        <p className="text-gray-500 italic">No suggestions available</p>
      </div>
    );
  }

  return (
    <div
      id="ideation-why-guide-5"
      className="flex flex-col p-4 bg-white gap-3 rounded-xl border border-card-border/40 text-justify"
    >
      {suggestion.map((eachQuestion: string, idx: number) => {
        return (
          <Question
            key={idx}
            bgColor="#E9FCE3"
            textColor="#078B04"
            question={eachQuestion}
            onSelect={onSuggestionClick}
          />
        );
      })}
    </div>
  );
};

export default SuggestionQuestions;
