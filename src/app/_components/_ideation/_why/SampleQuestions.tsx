const SAMPLE_QUESTIONS = [
  {
    type: "Public Policy",
    textColor: "#0C5394",
    bgColor: "#E9F6FF",
    question:
      "How does US Immigration Policy affect individuals' decisions to immigrate?",
  },
  {
    type: "Market Research",
    textColor: "#E45301",
    bgColor: "#FFF2E6",
    question:
      "What factors influence attitudes towards teleworking colleagues?",
  },
  {
    type: "Sales Management",
    textColor: "#7900C2",
    bgColor: "#F3EAFF",
    question: "What causes individuals to buy a generative AI product?",
  },
  {
    type: "Product Management",
    textColor: "#078B04",
    bgColor: "#E9FCE3",
    question: "What causes a person to want to drive a car?",
  },
];

interface QuestionProps {
  type: string;
  textColor: string;
  bgColor: string;
  question: string;
}

const Question = ({ type, textColor, bgColor, question }: QuestionProps) => {
  return (
    <div className="flex flex-row p-3 gap-3 bg-background rounded-lg">
      <div
        className="px-2 py-1 rounded-md"
        style={{ backgroundColor: `${bgColor}` }}
      >
        <p
          className="font-roboto text-base font-medium"
          style={{ color: `${textColor}` }}
        >
          {type}
        </p>
      </div>
      <div className="flex text-text-dark items-center font-roboto text-lg font-normal">
        <p>{question}</p>
      </div>
    </div>
  );
};

const SampleQuestions = () => {
  return (
    <div className="flex flex-col p-4 bg-white gap-3 rounded-xl border border-card-border/40 justify-center">
      {SAMPLE_QUESTIONS.map((q, idx) => {
        return (
          <Question
            key={idx}
            type={q.type}
            bgColor={q.bgColor}
            textColor={q.textColor}
            question={q.question}
          />
        );
      })}
    </div>
  );
};

export default SampleQuestions;
