interface FocusCardProps {
  title: string;
  subtitle: string;
  active: boolean;
  // eslint-disable-next-line no-unused-vars
  setActive: (title: string) => void;
}

const FocusCard = ({ title, subtitle, active, setActive }: FocusCardProps) => {
  return (
    <div
      className={`flex flex-col gap-3 h-full bg-white py-4 px-3 rounded-lg border ${active ? "border-primary" : "border-custom-gray"} hover:cursor-pointer`}
      onMouseDown={() => {
        setActive(title);
      }}
    >
      <div>
        <div
          className={`relative flex h-4 w-4 items-center justify-center rounded-full border-2 ${active ? "border-primary" : ""} bg-white`}
          style={{ borderColor: active ? "initial" : "#667085" }}
        >
          {active ? (
            <span
              className="h-2 w-2 rounded-full bg-primary"
              aria-hidden="true"
            />
          ) : (
            <span
              className="h-2 w-2 rounded-full bg-custom-gray"
              aria-hidden="true"
            />
          )}
        </div>
      </div>
      <div className="font-roboto">
        <p className="font-medium text-text-dark text-base tracking-tight">
          {title}
        </p>
        <p className="font-normal text-text-placeholder text-xs">{subtitle}</p>
      </div>
    </div>
  );
};

export default FocusCard;
