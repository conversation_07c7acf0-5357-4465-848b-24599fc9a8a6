import React from "react";
import { Pencil, ArrowRight } from "lucide-react";

interface CausalQuestion {
  text: string;
  attributes: string[];
}

interface CausalQuestionListProps {
  questions: CausalQuestion[];
  onSelectQuestion: (question: CausalQuestion) => void;
  onEditQuestion: (questionText: string) => void;
}

const CausalQuestionList: React.FC<CausalQuestionListProps> = ({
  questions,
  onSelectQuestion,
  onEditQuestion,
}) => {
  if (questions.length === 0) {
    return null;
  }

  return (
    <div className="mt-8 space-y-6 animate-fade-text">
      <h3 className="text-lg font-semibold text-gray-800">
        Select Question to proceed:
      </h3>
      <div className="space-y-4">
        {questions.map((question, index) => (
          <div
            key={question.text}
            className="border rounded-xl p-5 hover:border-[#2D2E61]/20 transition-all duration-200 bg-white shadow-sm"
            style={{
              animation: `fadeIn 0.5s ease-in-out ${index * 0.1}s backwards`,
            }}
          >
            <p className="text-gray-800 mb-4 font-medium">{question.text}</p>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-700">Attributes</h3>
              <p className="text-gray-600 text-sm bg-gray-50 p-2 rounded-lg">
                {question.attributes.join(", ")}
              </p>
            </div>
            <div className="flex gap-3 justify-end mt-4">
              <button
                onClick={() => onEditQuestion(question.text)}
                className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 flex items-center gap-2 font-medium"
              >
                <Pencil size={14} />
                Refine/Brainstorm
              </button>
              <button
                onClick={() => onSelectQuestion(question)}
                className="px-4 py-2 text-sm bg-[#2D2E61] text-white rounded-lg hover:bg-[#2D2E61]/90 transition-all duration-200 flex items-center gap-2 font-medium shadow-sm"
              >
                Continue
                <ArrowRight size={14} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CausalQuestionList;
