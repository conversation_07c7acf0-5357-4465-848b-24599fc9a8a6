import React from "react";
import { History, RefreshCw } from "lucide-react";

import Tooltip from "@/app/_components/_util/ToolTip";

interface QueryHistoryItem {
  query: string;
  timestamp: number;
  causalQuestions: Array<any>;
}

interface HistoryPanelProps {
  queryHistory: QueryHistoryItem[];
  showHistory: boolean;
  toggleHistory: () => void;
  clearHistory: () => void;
  loadFromHistory: (historyItem: QueryHistoryItem) => void;
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({
  queryHistory,
  showHistory,
  toggleHistory,
  clearHistory,
  loadFromHistory,
}) => {
  if (queryHistory.length === 0) {
    return null;
  }

  return (
    <>
      <div className="flex gap-3">
        <button
          onClick={toggleHistory}
          className="text-sm text-gray-600 hover:text-[#2D2E61] flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
          aria-label={showHistory ? "Hide history" : "Show history"}
        >
          <History size={16} />
          {showHistory ? "Hide history" : "Show history"}
        </button>
        <div className="relative group">
          <button
            onClick={clearHistory}
            className="text-sm text-gray-600 hover:text-red-500 flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
            aria-label="Clear history"
          >
            <RefreshCw size={16} />
            Clear history
          </button>
          <Tooltip
            message="Clear all previous queries and their suggestions"
            position="top"
          />
        </div>
      </div>

      {showHistory && (
        <div className="border rounded-xl p-5 bg-gray-50/50 backdrop-blur-sm shadow-sm mt-4">
          <h4 className="text-sm font-semibold text-gray-700 mb-4">
            Previous Queries
          </h4>
          <div className="space-y-3 max-h-60 overflow-y-auto pr-2">
            {queryHistory.map((hist) => (
              <div
                key={hist.timestamp}
                className="border-b last:border-b-0 pb-3 last:pb-0"
              >
                <button
                  onClick={() => loadFromHistory(hist)}
                  className="w-full text-left hover:bg-white rounded-lg p-3 transition-all duration-200"
                >
                  <p className="text-sm font-medium text-gray-800 truncate">
                    {hist.query}
                  </p>
                  <p className="text-xs text-gray-500 mt-1.5">
                    {new Date(hist.timestamp).toLocaleString()} •{" "}
                    {hist.causalQuestions.length} questions
                  </p>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default HistoryPanel;
