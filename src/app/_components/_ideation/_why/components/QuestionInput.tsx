import React, { useEffect, useState, useMemo } from "react";
import { ResearchQuestionFormSchema } from "@/lib/schemas";

interface QuestionInputProps {
  query: string;
  setQuery: (query: string) => void;
  errorMessage: string | null;
  showCausalitySuggestions: boolean;
}

const QuestionInput: React.FC<QuestionInputProps> = ({
  query,
  setQuery,
  errorMessage,
  showCausalitySuggestions,
}) => {
  // Simple validation state
  const [validationError, setValidationError] = useState<string | undefined>();
  const [isValidating, setIsValidating] = useState(false);

  // Debounced validation function
  const debouncedValidate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return (value: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsValidating(true);
        try {
          ResearchQuestionFormSchema.shape.question.parse(value);
          setValidationError(undefined);
        } catch (err) {
          if (err instanceof Error) {
            setValidationError(err.message);
          } else {
            setValidationError("Invalid question");
          }
        } finally {
          setIsValidating(false);
        }
      }, 500);
    };
  }, []);

  // Validate on query change
  useEffect(() => {
    if (query.trim()) {
      debouncedValidate(query);
    } else {
      setValidationError(undefined);
    }
  }, [query, debouncedValidate]);

  const handleQueryChange = (newQuery: string) => {
    setQuery(newQuery);
  };

  // Show validation error only if there's no external error and no causality suggestions
  const showValidationError =
    validationError && !errorMessage && !showCausalitySuggestions;

  return (
    <div className="space-y-4 w-full">
      <textarea
        value={query}
        onChange={(e) => handleQueryChange(e.target.value)}
        placeholder="Describe the experiment you want to create..."
        className={`flex-1 w-full h-32 p-4 border rounded-xl focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 resize-none text-gray-800 placeholder:text-gray-400 shadow-sm ${
          showValidationError
            ? "border-red-300 focus:ring-red-500"
            : "border-gray-200 focus:ring-[#2D2E61]"
        }`}
      />

      {/* Show external error message (takes priority) */}
      {errorMessage && !showCausalitySuggestions && (
        <div className="text-sm text-red-500 bg-red-50 p-3 rounded-lg border border-red-100 animate-fade-text">
          <p>{errorMessage}</p>
        </div>
      )}

      {/* Show validation error if no external error */}
      {showValidationError && (
        <div className="text-sm text-red-500 bg-red-50 p-3 rounded-lg border border-red-100 animate-fade-text">
          <p>{validationError}</p>
        </div>
      )}
    </div>
  );
};

export default QuestionInput;
