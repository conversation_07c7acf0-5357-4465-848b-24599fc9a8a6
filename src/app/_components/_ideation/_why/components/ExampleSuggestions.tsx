import React, { useState, useEffect, useMemo } from "react";
import { exampleThemes } from "../../data/exampleThemes";

interface ExampleSuggestionsProps {
  onSelectExample: (example: string) => void;
  queryHistory: Array<any>;
  showCausalitySuggestions: boolean;
  errorMessage: string | null;
}

const ExampleSuggestions: React.FC<ExampleSuggestionsProps> = ({
  onSelectExample,
  queryHistory,
  showCausalitySuggestions,
  errorMessage,
}) => {
  const [rotatingExampleQuery, setRotatingExampleQuery] = useState<string>("");

  // Flatten all queries into a single array
  const allQueries = useMemo(
    () => exampleThemes.flatMap((theme) => theme.questions),
    []
  );

  // Auto-rotate example queries (only if no history)
  useEffect(() => {
    if (queryHistory.length > 0) {
      return; // Stop the effect if there's history
    }

    // Initialize with a random query
    if (!rotatingExampleQuery || !allQueries.includes(rotatingExampleQuery)) {
      if (allQueries.length > 0) {
        const randomIndex = Math.floor(Math.random() * allQueries.length);
        setRotatingExampleQuery(allQueries[randomIndex]);
      }
    }

    const intervalId = setInterval(() => {
      setRotatingExampleQuery((prevExample) => {
        if (allQueries.length === 0) return "";
        if (allQueries.length === 1) return allQueries[0];

        const currentIndex = allQueries.indexOf(prevExample);
        let nextIndex;
        do {
          nextIndex = Math.floor(Math.random() * allQueries.length);
        } while (nextIndex === currentIndex && allQueries.length > 1);
        return allQueries[nextIndex];
      });
    }, 3000); // Change every 3 seconds

    return () => clearInterval(intervalId);
  }, [queryHistory.length, allQueries, rotatingExampleQuery]);

  const handleSelectExample = (example: string) => {
    onSelectExample(example);
  };

  // Don't render if we don't meet the display conditions
  if (
    queryHistory.length > 0 ||
    showCausalitySuggestions ||
    !(
      errorMessage === null ||
      errorMessage?.includes("Unable to validate your question as causal")
    )
  ) {
    return null;
  }

  return (
    <div className="animate-carousel">
      <button
        onClick={() => handleSelectExample(rotatingExampleQuery)}
        className="italic text-left text-sm text-gray-600 hover:text-[#2D2E61] hover:bg-white p-3 rounded-lg w-full transition-all duration-200 border border-yellow-400 bg-white/80 shadow-sm hover:shadow"
      >
        {rotatingExampleQuery}
      </button>
    </div>
  );
};

export default ExampleSuggestions;
