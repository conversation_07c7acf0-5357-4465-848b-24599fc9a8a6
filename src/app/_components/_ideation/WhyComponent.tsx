import React, { useState } from "react";
import SubscribeModal from "../_payments/SubscribeModal";
import useWhyComponent from "./_why/hooks/useWhyComponent";
import QuestionInput from "./_why/components/QuestionInput";
import ExampleSuggestions from "./_why/components/ExampleSuggestions";
import HistoryPanel from "./_why/components/HistoryPanel";
import ActionButtons from "./_why/components/ActionButtons";
import CausalQuestionList from "./_why/components/CausalQuestionList";
import SuggestionPanel from "./_why/components/SuggestionPanel";
import { getExampleQueries } from "./data/exampleThemes";
import { useWhyCausalityStore } from "./whyCausalityStore";

interface WhyComponentProps {
  onComplete: () => void;
  setTraits: React.Dispatch<React.SetStateAction<Record<string, string[]>>>;
  onRunExperiment: () => void;
  setExperimentPublic: () => void;
  initialQuery?: string;
  errorMessage: string | null;
  setErrorMessage: React.Dispatch<React.SetStateAction<string | null>>;
  hint?: string;
}

interface WhyComponentExtraProps {
  roles: string[];
  showSubscribeModal: boolean;
  setShowSubscribeModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const WhyComponent = ({
  onComplete,
  setTraits,
  initialQuery,
  errorMessage,
  setErrorMessage,
  setExperimentPublic: _setExperimentPublic,
  onRunExperiment: _onRunExperiment,
  roles,
  showSubscribeModal,
  setShowSubscribeModal,
}: WhyComponentProps & WhyComponentExtraProps) => {
  const {
    // State
    query,
    isLoading,
    causalQuestions,
    queryHistory,
    showHistory,
    causalitySuggestions,
    validatedQuestions,

    // Actions
    setQuery,
    handleGenerateQuestions,
    handleQuestionSelect,
    loadFromHistory,
    toggleHistory,
    getButtonText,
    setCausalQuestions,
    setQueryHistory,
    setShowHistory,
    checkCausality,
    setValidatedQuestions,
  } = useWhyComponent({
    initialQuery,
    setErrorMessage,
    onComplete,
    setTraits,
    roles,
    showSubscribeModal,
    setShowSubscribeModal,
  });

  const showCausalitySuggestions = useWhyCausalityStore(
    (s) => s.showCausalitySuggestions
  );
  const setShowCausalitySuggestions = useWhyCausalityStore(
    (s) => s.setShowCausalitySuggestions
  );

  const isCurrentQueryCausal = useWhyCausalityStore(
    (s) => s.isCurrentQueryCausal
  );
  const setIsCurrentQueryCausal = useWhyCausalityStore(
    (s) => s.setIsCurrentQueryCausal
  );
  const setCausalitySuggestions = useWhyCausalityStore(
    (s) => s.setCausalitySuggestions
  );

  const [isCheckingCausality, setIsCheckingCausality] = useState(false);
  const [lastCausalityCheckedQuery, setLastCausalityCheckedQuery] =
    useState<string>("");

  const handleSuggestionSelection = (selectedSuggestion: string) => {
    setQuery(selectedSuggestion);
    // setShowCausalitySuggestions(false);
    setErrorMessage(null);
    handleQuestionSelect({
      text: selectedSuggestion,
      attributes: [],
    });
  };

  const handleContinueWithCurrentQuery = () => {
    if (isCurrentQueryCausal) {
      handleQuestionSelect({
        text: query,
        attributes: [],
      });
    } else {
      setErrorMessage(
        "Please select a suggestion from the list or edit your question to make it causal."
      );
    }
  };

  const handleEditQuestion = (questionText: string) => {
    setQuery(questionText);
    setCausalQuestions([]);
  };

  const clearHistory = () => {
    setQueryHistory([]);
    setShowHistory(false);
    setCausalQuestions([]);
  };

  const handleContinue = async () => {
    const currentQueryText = query.trim();
    if (!currentQueryText) return;

    if (
      showCausalitySuggestions &&
      !validatedQuestions.has(currentQueryText) &&
      currentQueryText === lastCausalityCheckedQuery
    ) {
      setErrorMessage(
        "Please select a suggestion from the list or edit your question to make it causal."
      );
      return;
    }

    // Case 1: Already validated question - proceed directly
    if (validatedQuestions && validatedQuestions.has(currentQueryText)) {
      handleQuestionSelect({
        text: currentQueryText,
        attributes: [],
      });
      return;
    }

    // Case 2: Already showing causality suggestions and no error - proceed directly
    if (showCausalitySuggestions && !errorMessage) {
      handleQuestionSelect({
        text: currentQueryText,
        attributes: [],
      });
      return;
    }

    // Case 3: Question was previously generated
    const existingSuggestion = queryHistory.some((hist) =>
      hist.causalQuestions.some(
        (question) => question.text === currentQueryText
      )
    );

    if (existingSuggestion) {
      // Add to validated questions and proceed directly
      const newValidated = new Set(validatedQuestions);
      newValidated.add(currentQueryText);
      setValidatedQuestions(newValidated);
      handleQuestionSelect({
        text: currentQueryText,
        attributes: [],
      });
      return;
    }

    // Case 4: Check if current query is one of the predefined example queries
    const allQueries = getExampleQueries();
    const isExampleQuery = allQueries.includes(currentQueryText);

    if (isExampleQuery) {
      // Example queries are considered causal by default - proceed directly
      const newValidated = new Set(validatedQuestions);
      newValidated.add(currentQueryText);
      setValidatedQuestions(newValidated);
      handleQuestionSelect({
        text: currentQueryText,
        attributes: [],
      });
      return;
    }

    // Case 5: Check if the query is one of the current causality suggestions
    const isSuggestion = causalitySuggestions.includes(currentQueryText);
    if (isSuggestion) {
      // This is a suggestion from a previous causality check - proceed directly
      const newValidated = new Set(validatedQuestions);
      newValidated.add(currentQueryText);
      setValidatedQuestions(newValidated);
      handleQuestionSelect({
        text: currentQueryText,
        attributes: [],
      });
      return;
    }

    // Case 6: New query that needs causality check
    setIsCheckingCausality(true);
    setErrorMessage(null);
    setLastCausalityCheckedQuery(currentQueryText);
    try {
      const isDeemedCausal = await checkCausality(currentQueryText);
      setIsCurrentQueryCausal(isDeemedCausal);
      setShowCausalitySuggestions(true);

      if (isDeemedCausal) {
        const newValidated = new Set(validatedQuestions);
        newValidated.add(currentQueryText);
        setValidatedQuestions(newValidated);
      }
    } catch (error) {
      console.error("Error during causality check:", error);
      setErrorMessage(
        "Unable to validate your question as causal. Try phrasing it as 'What factors influence...' or 'What causes...' Click on one of the rotating examples below for inspiration."
      );
    } finally {
      setIsCheckingCausality(false);
    }
  };
  const isAlreadyCausal =
    validatedQuestions && validatedQuestions.has(query.trim());

  return (
    <div className="flex flex-col gap-8 items-start max-w-4xl w-[800px] py-8">
      <div className="flex flex-col gap-6 w-full">
        <div className="flex max-xl:flex-col justify-between items-center gap-4 w-full">
          <h2
            id="ideation-why-guide-2"
            className="max-xl:flex max-xl:justify-start max-xl:w-full text-text-dark font-roboto font-semibold text-2xl tracking-tight animate-fade-text"
          >
            Which decision scenario would you like to investigate?
          </h2>
        </div>

        <div className="w-full space-y-6">
          {/* Question Input */}
          <QuestionInput
            query={query}
            setQuery={(newQuery) => {
              setQuery(newQuery);
              setIsCurrentQueryCausal(false);
              setShowCausalitySuggestions(false);
              setCausalitySuggestions([]);
              setErrorMessage(null);
            }}
            errorMessage={errorMessage}
            showCausalitySuggestions={showCausalitySuggestions}
          />

          {/* Example Suggestions */}
          <ExampleSuggestions
            onSelectExample={(example) => {
              setQuery(example);
              // When selecting an example, validate it and proceed immediately
              const newValidated = new Set(validatedQuestions);
              newValidated.add(example.trim());
              setValidatedQuestions(newValidated);
              // Proceed directly with the selected example
              handleQuestionSelect({
                text: example,
                attributes: [],
              });
            }}
            queryHistory={queryHistory}
            showCausalitySuggestions={showCausalitySuggestions}
            errorMessage={errorMessage}
          />

          <div className="flex justify-between items-center gap-4">
            {/* History Controls */}
            <HistoryPanel
              queryHistory={queryHistory}
              showHistory={showHistory}
              toggleHistory={toggleHistory}
              clearHistory={clearHistory}
              loadFromHistory={loadFromHistory}
            />

            <div className="flex-1" />

            {/* Action Buttons */}
            <ActionButtons
              isLoading={isLoading}
              isCheckingCausality={isCheckingCausality}
              isAlreadyCausal={isAlreadyCausal}
              showCausalitySuggestions={showCausalitySuggestions}
              errorMessage={errorMessage}
              query={query}
              handleGenerateQuestions={handleGenerateQuestions}
              handleContinue={handleContinue}
              getButtonText={getButtonText}
            />
          </div>

          {/* Suggestion Panel */}
          <SuggestionPanel
            showCausalitySuggestions={showCausalitySuggestions}
            causalitySuggestions={causalitySuggestions}
            errorMessage={errorMessage}
            onSuggestionSelect={handleSuggestionSelection}
            onContinueWithCurrent={handleContinueWithCurrentQuery}
            onCancel={() => setShowCausalitySuggestions(false)}
            query={query}
            isCausal={isCurrentQueryCausal}
          />

          {/* History Panel */}
          {showHistory && queryHistory.length > 0 && (
            <div className="border rounded-xl p-5 bg-gray-50/50 backdrop-blur-sm shadow-sm">
              <h4 className="text-sm font-semibold text-gray-700 mb-4">
                Previous Queries
              </h4>
              <div className="space-y-3 max-h-60 overflow-y-auto pr-2">
                {queryHistory.map((hist) => (
                  <div
                    key={hist.timestamp}
                    className="border-b last:border-b-0 pb-3 last:pb-0"
                  >
                    <button
                      onClick={() => loadFromHistory(hist)}
                      className="w-full text-left hover:bg-white rounded-lg p-3 transition-all duration-200"
                    >
                      <p className="text-sm font-medium text-gray-800 truncate">
                        {hist.query}
                      </p>
                      <p className="text-xs text-gray-500 mt-1.5">
                        {new Date(hist.timestamp).toLocaleString()} •{" "}
                        {hist.causalQuestions.length} questions
                      </p>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {causalQuestions.length > 0 && (
            <p className="text-sm text-gray-500 italic bg-gray-50/50 p-3 rounded-lg border border-gray-100 animate-fade-text">
              You can edit your query above and generate new causal questions at
              any time
            </p>
          )}

          {/* Causal Question List */}
          <CausalQuestionList
            questions={causalQuestions}
            onSelectQuestion={handleQuestionSelect}
            onEditQuestion={handleEditQuestion}
          />
        </div>
      </div>

      {showSubscribeModal &&
        !roles.includes("employee") &&
        !roles.includes("customer") && (
          <SubscribeModal
            showModal={true}
            setShowModal={setShowSubscribeModal}
          />
        )}
    </div>
  );
};

export default WhyComponent;
