// ProgressIndicator.test.tsx

import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ProgressIndicator from "../ProgressIndicator";

describe("ProgressIndicator Component", () => {
  const steps = [
    { name: "Step 1", status: "complete" },
    { name: "Step 2", status: "current" },
    { name: "Step 3", status: "upcoming" },
  ];

  it("should call setCurrStep when a step is clicked", () => {
    const setCurrStep = jest.fn();
    render(<ProgressIndicator steps={steps} setCurrStep={setCurrStep} />);

    const stepButton = screen.getByRole("button", { name: "Step 2" });
    fireEvent.mouseDown(stepButton);

    expect(setCurrStep).toHaveBeenCalledWith(1);
  });
});
