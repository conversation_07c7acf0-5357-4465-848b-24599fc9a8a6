import { create } from "zustand";

interface WhyCausalityState {
  showCausalitySuggestions: boolean;
  setShowCausalitySuggestions: (show: boolean) => void;
  isCurrentQueryCausal: boolean;
  setIsCurrentQueryCausal: (isCausal: boolean) => void;
  causalitySuggestions: string[];
  setCausalitySuggestions: (suggestions: string[]) => void;
}

export const useWhyCausalityStore = create<WhyCausalityState>((set) => ({
  showCausalitySuggestions: false,
  setShowCausalitySuggestions: (show) =>
    set({ showCausalitySuggestions: show }),
  isCurrentQueryCausal: false,
  setIsCurrentQueryCausal: (isCausal) =>
    set({ isCurrentQueryCausal: isCausal }),
  causalitySuggestions: [],
  setCausalitySuggestions: (suggestions) =>
    set({ causalitySuggestions: suggestions }),
}));
