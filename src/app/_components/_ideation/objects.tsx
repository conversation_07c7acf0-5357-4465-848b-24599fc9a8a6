// Type definitions for trait categories
type TraitCategory =
  | "usTraits"
  | "nonUsTraits"
  | "lifestyleTraits"
  | "psychologicalTraits"
  | "customTraits"
  | "personalityTraits";

// Base interfaces
interface Attribute {
  attribute: string;
  active: boolean;
  levels: string[];
  category?: TraitCategory;
  attribute_type: string;
}

interface FileState {
  file: File | null;
  data: any[] | null;
  error: string | null;
}

interface AttributeResponse {
  attribute: string;
  levels: string[];
  category?: TraitCategory;
  attribute_type: string;
}

interface AttributeLevel {
  attribute: string;
  levels: string[];
  category?: TraitCategory;
}

interface BrandAttributeCombination {
  [brand: string]: {
    [attribute: string]: string;
  };
}

// Response interfaces
interface RealWorldAttributesResponse {
  attributes_levels: AttributeLevel[];
  brand_attribute_combinations: BrandAttributeCombination[];
}

// Request interfaces
interface orthogonalAttributesLevelsRequest {
  existing_attributes_levels: AttributeResponse[];
  why_prompt: string;
  country?: string;
  new_attribute_count: number;
  level_count: number;
  number_of_new_attributes: number;
  number_of_levels: number;
  category?: TraitCategory;
}

interface AddNewAttributeCardLevelsObject {
  why_prompt: string;
  attributes: string[];
  country?: string;
  level_count: 4;
  existing_attributes: string[];
  category?: TraitCategory;
}

interface AddNewTraitLabelsObject {
  why_prompt: string;
  new_trait: string;
  existing_traits: string[];
  levels_count: number;
  category?: TraitCategory;
}

// Geographic interfaces
interface Coordinates {
  x: number;
  y: number;
}

interface State {
  name: string;
  code: string;
  flag: string;
  coords: Coordinates;
}

interface Country {
  name: string;
  flag: string;
  coords: Coordinates;
  states?: State[];
}

// Display interfaces
interface Level {
  level: string;
  active: boolean;
}

interface DisplayAttribute {
  attribute: string;
  active: boolean;
  levels: Level[];
  category?: TraitCategory;
  attribute_type: string;
}

interface DisplayTrait {
  title: string;
  active: boolean;
  values: string[];
  category?: TraitCategory;
}

// Population interfaces
interface PopulationTraits {
  // US Specific Traits
  state: string | null;
  census_division?: string;
  marital_status?: string;
  family_size?: string;
  hispanic_latino?: boolean;
  veteran_status?: string;
  migration_status?: string;
  speaks_english?: string;
  health_insurance?: boolean;
  vehicles_in_household?: string;

  // Non-US Demographics
  gender: string[];
  age: number[];
  education_level: string[];
  racial_group: string[];
  hispanic_latino_origin?: string;
  home_ownership?: string;
  household_size?: string;
  household_vehicles?: string;
  household_with_children?: boolean;
  household_income: number[];
  number_of_children: string[];

  // Lifestyle Traits
  lifestyle_traits?: {
    health_consciousness?: string;
    price_sensitivity?: string;
    brand_loyalty?: string;
    environmental_concern?: string;
    innovation_adoption?: string;
    quality_orientation?: string;
    convenience_priority?: string;
  };

  // Psychological Traits
  psychological_traits?: {
    need_for_uniqueness?: string;
    self_monitoring?: string;
    risk_aversion?: string;
    impulsiveness?: string;
    need_for_cognition?: string;
    time_orientation?: string;
    self_construal?: string;
    materialism?: string;
  };
}

// Experiment interfaces
interface CreateExperimentRequest {
  question: string;
  population_traits: DisplayTrait[];
  populationTraits?: PopulationTraits;
  displayAttributes: DisplayAttribute[];
  realworld_products: BrandAttributeCombination[];
  year: string;
  country: string;
  is_private?: boolean;
  state?: string | null;
  external_personas?: Persona[];
  experiment_type: string;
  concept_description: string;
  concept_statements: any[];
  image_name: string;
}

interface attributesLevelsRequest {
  why_prompt: string;
  country?: string;
  level_count: number;
  attribute_count: number;
  year?: string;
  category?: TraitCategory;
}

interface CreateLevelsRequest {
  why_prompt: string;
  country?: string;
  level_count: number;
  values: string[];
  category?: TraitCategory;
}

// Product interfaces
interface RealWorldProductCheckRequest {
  question: string;
}

interface RealWorldProductAttributeRequest {
  number_of_attributes: number;
  country: string;
  why_prompt: string;
  category?: TraitCategory;
}

// Trait interface
interface Trait {
  id: string;
  set_type: string | null;
  type: string;
  short_description: string;
  long_description: string;
  measurement_type: string;
  ordinal_rank: number | null;
  category?: TraitCategory;
}

// Model interface
interface LLMModel {
  name: string;
}

// State data interfaces
interface StateData {
  Age: {
    min: number;
    max: number;
    "90th_percentile": number;
    "95th_percentile": number;
    userMin?: number; // User's selected min value
    userMax?: number; // User's selected max value
  };
  "Household income": {
    min: number;
    max: number;
    "90th_percentile": number;
    "95th_percentile": number;
    userMin?: number; // User's selected min value
    userMax?: number; // User's selected max value
  };
}

// Population trait interfaces
interface FinalSelectedPopulationTraits {
  population_size: number;
  population_traits: Record<string, string | number | Array<string | number>>;
}

interface OneTraitChangeTraits {
  [key: string]: {
    population_size: number;
    population_traits: any;
  };
}

interface ValidationResponse {
  original: FinalSelectedPopulationTraits;
  suggestion: FinalSelectedPopulationTraits;
  one_trait_change: OneTraitChangeTraits;
}

// Product levels request
type ProductLevelsRequest = {
  attribute_count: number;
  level_count: number;
  country?: string;
  why_prompt: string;
  num_levels: number;
  num_attrs: number;
  category?: TraitCategory;
};

interface Persona {
  id: string;
  name: string;
  age: string;
  gender: string;
  maritalStatus: string;
  income: string;
  education: string;
  racialGroup: string;
  homeOwnership: string;
  vehiclesOwned: string;
  hasDrivingLicense: string;
  location: string;
  occupation: string;
  background: string;
  goals: string;
  painPoints: string;
  personalityTraits: string;
  behaviors: string;
  isDescriptionBased?: boolean;
  description?: string;
  linkedInSource?: string;
}

// all interfaces and types
export type {
  TraitCategory,
  Attribute,
  AttributeResponse,
  AttributeLevel,
  BrandAttributeCombination,
  RealWorldAttributesResponse,
  orthogonalAttributesLevelsRequest,
  AddNewAttributeCardLevelsObject,
  AddNewTraitLabelsObject,
  Coordinates,
  State,
  Country,
  Level,
  DisplayAttribute,
  DisplayTrait,
  PopulationTraits,
  CreateExperimentRequest,
  attributesLevelsRequest,
  CreateLevelsRequest,
  RealWorldProductCheckRequest,
  RealWorldProductAttributeRequest,
  Trait,
  LLMModel,
  StateData,
  FinalSelectedPopulationTraits,
  OneTraitChangeTraits,
  ValidationResponse,
  FileState,
  ProductLevelsRequest,
  Persona,
};
