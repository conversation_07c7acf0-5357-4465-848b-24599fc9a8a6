const traitValues = {
  // US Specific Traits
  usTraits: {
    "Vehicles In Household": ["0", "1", "2", "3", "4", "4+"],
    "Census Division": [
      "East South Central",
      "New England",
      "East North Central",
      "Pacific",
      "West South Central",
      "Middle Atlantic",
      "South Atlantic",
      "West North Central",
      "Mountain",
    ],
    "Marital Status": [
      "Never married/single",
      "Married, spouse present",
      "Widowed",
      "Divorced",
      "Separated",
      "Married, spouse absent",
    ],
    "Family Size": ["1", "2", "3", "4", "4+"],
    "Hispanic Latino": ["Yes", "No"],
    "Veteran Status": ["Not a veteran", "Veteran"],
    "Migration Status": [
      "Moved within state",
      "Abroad one year ago",
      "Same house",
      "Moved between states",
    ],
    "Speaks English": [
      "Yes, speaks well",
      "Yes, but not well",
      "Does not speak English",
      "Yes, speaks very well",
      "Yes, speaks only English",
    ],
    "Health Insurance Coverage": [
      "No health insurance coverage",
      "With health insurance coverage",
    ],
  },

  // Non-US Demographics
  nonUsTraits: {
    Gender: ["Female", "Male"],
    Age: ["18 to 92"],
    "Education Level": [
      "Masters",
      "Bachelors",
      "Some College",
      "PhD",
      "High School but no diploma",
      "High School Diploma",
      "Associates",
      "Less than high school",
    ],
    Race: [
      "White",
      "African American",
      "Asian",
      "Other race",
      "American Indian/Alaska Native",
      "Native Hawaiian/Pacific Islander",
    ],
    "Hispanic/Latino Origin": ["No", "Yes"],
    "Home Ownership": ["Own", "Rent", "Other"],
    "Household Size": ["1", "2", "3", "4", "4+"],
    "Number of Vehicles in Household": ["0", "1", "2", "3", "4", "4+"],
    "Household with Children": ["Yes", "No"],
  },

  // Lifestyle Traits
  lifestyleTraits: {
    "Health Consciousness": [
      "Health Fanatic",
      "Health Aware",
      "Moderately Health Conscious",
      "Indifferent",
    ],
    "Price Sensitivity": [
      "Highly Price Sensitive",
      "Value Seeker",
      "Premium Acceptor",
      "Price Indifferent",
    ],
    "Brand Loyalty": [
      "Brand Loyal",
      "Brand Flexible",
      "Brand Explorer",
      "Brand Indifferent",
    ],
    "Environmental Concern": [
      "Eco-Warrior",
      "Eco-Conscious",
      "Occasionally Concerned",
      "Unconcerned",
    ],
    "Innovation Adoption": [
      "Early Adopter",
      "Early Majority",
      "Late Majority",
      "Laggard",
    ],
    "Quality Orientation": [
      "Premium Seeker",
      "Quality Conscious",
      "Moderate Quality",
      "Basic Acceptor",
    ],
    "Convenience Priority": [
      "Convenience First",
      "Balance Seeker",
      "Traditional Process",
      "Time Abundant",
    ],
  },

  // Psychological Traits
  psychologicalTraits: {
    "Need for Uniqueness": [
      "High Distinctiveness Seeker",
      "Moderately Unique",
      "Conformity Comfortable",
      "Mainstream Oriented",
    ],
    "Self-Monitoring": [
      "Highly Self-Monitoring",
      "Situationally Aware",
      "Occasionally Mindful",
      "Low Self-Monitor",
    ],
    "Risk Aversion": [
      "Highly Risk Averse",
      "Cautious",
      "Risk Tolerant",
      "Risk Seeking",
    ],
    Impulsiveness: [
      "Highly Impulsive",
      "Moderately Impulsive",
      "Generally Planned",
      "Strictly Planned",
    ],
    "Need for Cognition": [
      "Deep Thinker",
      "Analytical",
      "Surface Processor",
      "Quick Decider",
    ],
    "Time Orientation": [
      "Future Focused",
      "Present Focused",
      "Past Oriented",
      "Balanced Timeline",
    ],
    "Self-Construal": [
      "Highly Independent",
      "Moderately Independent",
      "Interdependent",
      "Highly Interdependent",
    ],
    Materialism: [
      "Highly Materialistic",
      "Moderately Materialistic",
      "Low Materialism",
      "Anti-Materialistic",
    ],
  },

  // Personality Traits
  personalityTraits: {
    "Big five extroversion": ["Low", "Medium", "High"],
    "Big five openness": [
      "Traditional",
      "Moderate",
      "Exploratory",
      "Progressive",
    ],
    "Big five conscientiousness": ["Flexible", "Balanced", "Structured"],
    "Big five agreeableness": [
      "Reserved",
      "Adaptive",
      "Cooperative",
      "Compassionate",
    ],
    "Big five emotional stability": ["Reactive", "Steady", "Resilient"],
    "Emotional valence": ["Negative", "Neutral", "Positive"],
    Ethnocentrism: ["Inclusive", "Moderate", "Exclusive"],
    "8 values diplomatic": [
      "Nationalist",
      "Balanced",
      "Globalist",
      "Internationalist",
    ],
    "8 values civil": ["Libertarian", "Moderate", "Authoritarian"],
    "Political ideology": [
      "Conservative",
      "Centrist",
      "Progressive",
      "Liberal",
    ],
  },
};

export default traitValues;
