"use client";

import { useState, useCallback, useContext } from "react";
import ExperimentCreationContext from "../../ExperimentCreationContext";
import {
  AttributeLevel,
  DisplayAttribute,
  Level,
  BrandAttributeCombination,
} from "../../objects";
import * as Sentry from "@sentry/react";

/**
 * Custom hook for handling attribute refinement
 */
export function useAttributeRefinement() {
  const { displayAttributes, setDisplayAttributes, question, where } =
    useContext(ExperimentCreationContext);

  const [isRefining, setIsRefining] = useState<boolean>(false);
  const [refinedAttributes, setRefinedAttributes] = useState<AttributeLevel[]>(
    []
  );
  const [brandCombinations, setBrandCombinations] = useState<
    BrandAttributeCombination[]
  >([]);
  const [refinementError, setRefinementError] = useState<string>("");

  /**
   * Fetch refined attributes from the API
   */
  const fetchRefinedAttributes = useCallback(async () => {
    if (isRefining) return;

    setIsRefining(true);
    setRefinementError("");

    try {
      // Transform displayAttributes into the required format
      const transformedAttributes = displayAttributes
        .filter((attr: DisplayAttribute) => attr.active)
        .map((attr: DisplayAttribute) => ({
          attribute: attr.attribute,
          levels: attr.levels
            .filter((level: Level) => level.active)
            .map((level: Level) => level.level),
        }));

      // Prepare request body
      const requestBody = {
        existing_attributes_levels: transformedAttributes,
        why_prompt: question,
        country: where?.name,
      };

      // Make API call
      const response = await fetch("/api/attributes-levels/enhance", {
        method: "POST",
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Set refined attributes and brand combinations
      setRefinedAttributes(data.attributes_levels || []);
      setBrandCombinations(data.brand_attribute_combinations || []);
    } catch (error) {
      console.error("Error refining attributes:", error);
      Sentry.captureException(error);
      setRefinementError("Failed to refine attributes. Please try again.");
    } finally {
      setIsRefining(false);
    }
  }, [displayAttributes, question, where, isRefining]);

  /**
   * Accept refined attributes
   */
  const acceptRefinedAttributes = useCallback(() => {
    if (refinedAttributes.length === 0) return;

    // Convert refined attributes to the DisplayAttribute format
    const newDisplayAttributes: DisplayAttribute[] = refinedAttributes.map(
      (refinedAttr: AttributeLevel) => {
        return {
          attribute: refinedAttr.attribute,
          active: true,
          levels: refinedAttr.levels.map((level: string) => ({
            level,
            active: true,
          })),
          attribute_type: "custom", // Default attribute type
        };
      }
    );

    setDisplayAttributes(newDisplayAttributes);
    setRefinedAttributes([]);
    setBrandCombinations([]);
  }, [refinedAttributes, setDisplayAttributes]);

  /**
   * Cancel/dismiss refined attributes
   */
  const cancelRefinedAttributes = useCallback(() => {
    setRefinedAttributes([]);
    setBrandCombinations([]);
  }, []);

  return {
    // State
    isRefining,
    refinedAttributes,
    brandCombinations,
    refinementError,

    // Actions
    fetchRefinedAttributes,
    acceptRefinedAttributes,
    cancelRefinedAttributes,
    setRefinementError,
  };
}
