"use client";

import { DisplayAttribute } from "../../objects";
import AttributeEditor from "./AttributeEditor";
import NewAttributeForm from "./NewAttributeForm";

interface AttributeListProps {
  attributes: DisplayAttribute[];
  toggleAttribute: (idx: number) => void;
  toggleLevel: (idx: number, levelIdx: number) => void;
  toggleAllLevels: (idx: number) => void;
  isLoading: boolean;
  isMaxLevels: (index: number) => boolean;
  maxAttributesReached: () => boolean;
  addNewAttribute: (attributeName: string) => Promise<void>;
  setIsLoading: (isLoading: boolean) => void;
}

/**
 * Component for displaying the list of attributes
 */
export default function AttributeList({
  attributes,
  toggleAttribute,
  toggleLevel,
  toggleAllLevels,
  isLoading,
  isMaxLevels,
  maxAttributesReached,
  addNewAttribute,
  setIsLoading,
}: AttributeListProps) {
  return (
    <div className="flex flex-col items-start w-full gap-2 overflow-y-auto pb-4">
      {/* Attribute Cards */}
      {attributes.map((_, idx) => (
        <AttributeEditor
          key={idx}
          index={idx}
          toggleAttribute={toggleAttribute}
          toggleLevel={toggleLevel}
          toggleAllLevels={toggleAllLevels}
          isLoading={isLoading}
          isMaxLevels={isMaxLevels}
        />
      ))}

      {/* Add New Attribute Form */}
      {attributes.length < 15 && (
        <NewAttributeForm
          addNewAttribute={addNewAttribute}
          setIsLoading={setIsLoading}
          isMaxAttr={maxAttributesReached}
        />
      )}
    </div>
  );
}
