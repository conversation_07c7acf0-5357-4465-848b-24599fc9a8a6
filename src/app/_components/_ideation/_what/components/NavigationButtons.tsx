"use client";
import { memo } from "react";

interface NavigationButtonsProps {
  onBack: () => void;
  onRun: () => void;
  continueDisabled: boolean;
}

/**
 * Component for navigation buttons (back and run experiment)
 */
const NavigationButtons = memo(function NavigationButtons({
  onBack,
  onRun,
  continueDisabled,
}: NavigationButtonsProps) {
  return (
    <div className="sticky bottom-0 flex w-full justify-between bg-background py-2">
      {/* Back Button */}
      <button
        className="font-inter text-base font-medium py-2.5 px-6 bg-white hover:bg-secondary-grey border border-card-border shadow-sm rounded-lg"
        onClick={onBack}
      >
        Back
      </button>

      {/* Run Experiment Button */}
      <button
        id="ask-question-button"
        className={`font-inter text-base font-semibold py-2.5 ${
          continueDisabled
            ? "bg-[#ECEDFB] cursor-not-allowed"
            : "bg-primary hover:bg-primary-dark"
        } text-white rounded-lg shadow-sm w-[170px]`}
        onClick={onRun}
        disabled={continueDisabled}
        type="button"
        aria-label="Run experiment"
      >
        Run experiment
      </button>
    </div>
  );
});

export default NavigationButtons;
