"use client";

import { useState } from "react";
import PublicExperimentQuestionModal from "../../PublicExperimentQuestionModal";

interface ExperimentSettingsProps {
  setExperimentPrivate: () => void;
  setExperimentPublic: () => void;
  onModalClose: () => void;
  canRunExperiment: boolean;
}

/**
 * Component for handling experiment visibility settings
 */
export default function ExperimentSettings({
  setExperimentPrivate,
  setExperimentPublic,
  onModalClose,
  canRunExperiment,
}: ExperimentSettingsProps) {
  const [
    showPublicExperimentQuestionModal,
    setShowPublicExperimentQuestionModal,
  ] = useState(false);

  return (
    <>
      {/* Public/Private Modal */}
      <PublicExperimentQuestionModal
        setExperimentPrivate={setExperimentPrivate}
        setExperimentPublic={setExperimentPublic}
        showModal={showPublicExperimentQuestionModal}
        setShowModal={setShowPublicExperimentQuestionModal}
        onModalClose={onModalClose}
      />
    </>
  );
}
