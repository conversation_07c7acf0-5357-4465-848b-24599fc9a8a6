"use client";

import React, { useContext, useEffect, useState } from "react";
import { ChevronUpIcon } from "@heroicons/react/24/solid";
import { ThreeDotsBounce } from "../../../../../../public/icons/LoaderAnimations";
import LevelEditor from "./LevelEditor";
import AddNewLevel from "./AddNewLevel";
import { Level } from "../../objects";
import ExperimentCreationContext from "../../ExperimentCreationContext";
import Checkbox from "@/app/_components/_util/Checkbox";

interface AttributeEditorProps {
  index: number;
  toggleAttribute: (attributeIndex: number) => void;
  toggleLevel: (attributeIndex: number, levelIndex: number) => void;
  toggleAllLevels?: (attributeIndex: number) => void;
  isLoading: boolean;
  isMaxLevels: (attributeIndex: number) => boolean;
}

/**
 * Component for editing an attribute and its levels
 */
export default function AttributeEditor({
  index,
  toggleAttribute,
  toggleLevel,
  // toggleAllLevels is not used but kept for interface compatibility
  toggleAllLevels: _toggleAllLevels,
  isLoading,
  isMaxLevels,
}: AttributeEditorProps) {
  const { displayAttributes, setDisplayAttributes } = useContext(
    ExperimentCreationContext
  );

  const [attribute, setAttribute] = useState(
    displayAttributes[index].attribute
  );
  const [collapsed, setCollapsed] = useState(false);
  const [toggleMainCardCheckbox, setToggleMainCardCheckbox] = useState(
    displayAttributes[index].active
  );

  // Update attribute state when displayAttributes changes
  useEffect(() => {
    setAttribute(displayAttributes[index].attribute);
    setToggleMainCardCheckbox(displayAttributes[index].active);
  }, [displayAttributes, index]);

  // Count number of selected levels
  const numLevelsSelected = () => {
    return displayAttributes[index].levels.filter(
      (level: Level) => level.active
    ).length;
  };

  // Handle attribute name change
  const handleAttributeNameChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newName = e.target.value;
    setAttribute(newName);

    const newDisplayAttributes = [...displayAttributes];
    newDisplayAttributes[index].attribute = newName;
    setDisplayAttributes(newDisplayAttributes);
  };

  return (
    <div
      className={`flex flex-col gap-4 w-full bg-white rounded-lg border ${
        displayAttributes[index].active
          ? numLevelsSelected() < 2 && numLevelsSelected() > 0
            ? "border-[#FC0005]"
            : "border-primary"
          : "border-card-border"
      } hover:cursor-pointer`}
    >
      {/* Attribute Header */}
      <div
        className={`flex flex-row w-full px-3 items-center gap-4 justify-start ${
          collapsed ? "py-4" : "pt-4"
        }`}
      >
        <div className="flex flex-row flex-grow gap-4">
          {/* Checkbox */}
          <div
            onClick={() => {
              toggleAttribute(index);
              // Let the context update handle this state
              // The useEffect will sync the state
            }}
          >
            <Checkbox selected={displayAttributes[index].active} />
          </div>

          {/* Attribute Name Input */}
          <input
            className={`flex-grow text-text-dark text-base font-roboto font-medium bg-transparent focus:border-b focus:outline-none ${
              !toggleMainCardCheckbox ? "text-gray-500" : ""
            }`}
            value={attribute}
            onChange={handleAttributeNameChange}
          />
        </div>

        <div className="flex flex-row items-center justify-center gap-3">
          {/* Number Selected */}
          <p
            className={`w-fit ${
              numLevelsSelected() === 1 ? "text-[#FC0005]" : "text-primary"
            } font-roboto text-base ${
              numLevelsSelected() === 0 ? "hidden" : "block"
            }`}
          >
            {toggleMainCardCheckbox && `${numLevelsSelected()} selected`}
            {!toggleMainCardCheckbox && `0 selected`}
          </p>

          {/* Chevron Icon */}
          <ChevronUpIcon
            className={`h-5 w-5 transition-all duration-300 ${
              collapsed ? "rotate-180" : "rotate-0"
            }`}
            onMouseDown={() => setCollapsed(!collapsed)}
          />
        </div>
      </div>

      {/* Levels Section */}
      <div
        className={`px-8 pb-4 w-full transition-ease-in duration-500 ${
          collapsed ? "hidden" : "block"
        }`}
      >
        {isLoading && index === displayAttributes.length - 1 ? (
          <div className="flex flex-col gap-3 px-8 pb-4 justify-center items-center space-x-3 w-full h-full">
            <div>Generating levels...</div>
            <ThreeDotsBounce />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* Level Editors */}
            {displayAttributes[index].levels.map((_, idx: number) => (
              <LevelEditor
                key={idx}
                attributeIndex={index}
                levelIndex={idx}
                setLevelSelected={() => toggleLevel(index, idx)}
                cardMainActiveStatus={toggleMainCardCheckbox}
                selectedLevelsCount={displayAttributes[index].levels.length}
              />
            ))}

            {/* Add New Level */}
            <AddNewLevel attributeIndex={index} isMaxLevels={isMaxLevels} />
          </div>
        )}
      </div>
    </div>
  );
}
