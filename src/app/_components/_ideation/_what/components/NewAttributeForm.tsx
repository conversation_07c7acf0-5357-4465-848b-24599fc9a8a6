"use client";

import React, { useState, useMemo, useEffect } from "react";
import Checkbox from "@/app/_components/_util/Checkbox";
import { NonEmptyStringSchema } from "@/lib/schemas";

interface NewAttributeFormProps {
  addNewAttribute: (attributeName: string) => Promise<void>;
  setIsLoading?: (isLoading: boolean) => void;
  isMaxAttr: () => boolean;
}

/**
 * Component for adding a new attribute
 */
export default function NewAttributeForm({
  addNewAttribute,
  // setIsLoading is not used but kept for interface compatibility
  setIsLoading: _setIsLoading,
  // isMaxAttr is no longer used since we allow adding attributes even when max is reached
  isMaxAttr: _isMaxAttr,
}: NewAttributeFormProps) {
  const [inputValue, setInputValue] = useState("");
  const [validationError, setValidationError] = useState<string | undefined>();

  // Simple validation function
  const validate = (value: string): boolean => {
    try {
      NonEmptyStringSchema.parse(value);
      setValidationError(undefined);
      return true;
    } catch (err) {
      if (err instanceof Error) {
        setValidationError(err.message);
      } else {
        setValidationError("Invalid input");
      }
      return false;
    }
  };

  // Debounced validation
  const debouncedValidate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return (value: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => validate(value), 300);
    };
  }, []);

  // Validate on input change
  useEffect(() => {
    if (inputValue.trim()) {
      debouncedValidate(inputValue);
    } else {
      setValidationError(undefined);
    }
  }, [inputValue, debouncedValidate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate before submission
    const isValidInput = validate(inputValue);
    if (!isValidInput) return;

    await addNewAttribute(inputValue);
    setInputValue("");
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
  };

  return (
    <div className="w-full">
      <form className="w-full" onSubmit={handleSubmit}>
        <div
          className={`flex flex-row flex-grow justify-center gap-3 items-center p-4 self-stretch w-full bg-white rounded-lg border ${
            validationError ? "border-red-300" : "border-gray-200"
          }`}
        >
          <div className="flex flex-row gap-4">
            <Checkbox selected={false} />
          </div>
          <input
            className={`flex-grow text-text-dark text-base font-roboto font-medium bg-transparent focus:border-b focus:outline-none ${
              validationError ? "border-red-300" : ""
            }`}
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            placeholder="Type new attribute..."
          />
        </div>

        {/* Show validation error */}
        {validationError && (
          <div className="mt-2 text-sm text-red-500 bg-red-50 p-2 rounded border border-red-100">
            {validationError}
          </div>
        )}
      </form>
    </div>
  );
}
