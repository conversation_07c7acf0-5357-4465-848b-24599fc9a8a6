"use client";

import React, { useContext, useState } from "react";
import ExperimentCreationContext from "../../ExperimentCreationContext";
import Checkbox from "@/app/_components/_util/Checkbox";

interface LevelEditorProps {
  attributeIndex: number;
  levelIndex: number;
  setLevelSelected: (levelIndex: number) => void;
  cardMainActiveStatus: boolean;
  selectedLevelsCount: number;
}

/**
 * Component for editing a level within an attribute
 */
export default function LevelEditor({
  attributeIndex,
  levelIndex,
  setLevelSelected,
  cardMainActiveStatus,
  selectedLevelsCount,
}: LevelEditorProps) {
  const { displayAttributes, setDisplayAttributes } = useContext(
    ExperimentCreationContext
  );

  const [level, setLevel] = useState(
    displayAttributes[attributeIndex].levels[levelIndex].level
  );
  const [isHover, setIsHover] = useState(false);

  // Check if level is selected
  function isSelected(): boolean {
    if (!cardMainActiveStatus) {
      return false;
    }
    return displayAttributes[attributeIndex].levels[levelIndex].active;
  }

  // Handle level text change
  const handleLevelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newLevel = e.target.value;
    setLevel(newLevel);

    const newDisplayAttributes = [...displayAttributes];
    newDisplayAttributes[attributeIndex].levels[levelIndex].level = newLevel;
    setDisplayAttributes(newDisplayAttributes);
  };

  return (
    <div className="flex flex-row gap-3">
      {/* Checkbox */}
      <div
        onMouseDown={() => setLevelSelected(levelIndex)}
        className="relative group"
        onMouseEnter={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
      >
        <Checkbox selected={isSelected()} />

        {/* Tooltip for max levels */}
        {isHover && selectedLevelsCount > 6 && (
          <div
            className="absolute text-sm bg-[#1C1D47] text-white p-2 whitespace-nowrap rounded hover:opa"
            style={{ top: "-10px", left: "40px" }}
          >
            You can only select up to 10
          </div>
        )}
      </div>

      {/* Level Text Input */}
      <input
        className={`flex-grow text-base font-roboto font-medium bg-transparent focus:border-b focus:outline-none ${
          !isSelected() ? "text-gray-500" : "text-text-dark"
        }`}
        value={level || ""}
        onChange={handleLevelChange}
      />
    </div>
  );
}
