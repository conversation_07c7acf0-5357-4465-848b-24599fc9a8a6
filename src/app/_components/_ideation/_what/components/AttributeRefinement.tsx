"use client";

import { RefreshCw, <PERSON>, X, <PERSON><PERSON>lert } from "lucide-react";
import { AttributeLevel } from "../../objects";
import ErrorDisplay from "../../ErrorDisplay";

interface AttributeRefinementProps {
  isRefining: boolean;
  refinedAttributes: AttributeLevel[];
  refinementError: string;
  fetchRefinedAttributes: () => Promise<void>;
  acceptRefinedAttributes: () => void;
  cancelRefinedAttributes: () => void;
}

/**
 * Component for attribute refinement functionality
 */
export default function AttributeRefinement({
  isRefining,
  refinedAttributes,
  refinementError,
  fetchRefinedAttributes,
  acceptRefinedAttributes,
  cancelRefinedAttributes,
}: AttributeRefinementProps) {
  // If no refined attributes and no refinement in progress, show the refine button
  if (refinedAttributes.length === 0 && !isRefining) {
    return (
      <button
        className="font-inter p-2 flex flex-row gap-2 items-center text-base font-semibold bg-[#313462] hover:bg-[#252850] text-white rounded-lg shadow-sm w-[fit-content]"
        onClick={fetchRefinedAttributes}
      >
        Refine characteristics
        <RefreshCw className="h-4 w-4" />
      </button>
    );
  }

  // If refinement is in progress, show loading
  if (isRefining) {
    return (
      <div className="flex flex-row items-center gap-2 p-2 bg-gray-100 rounded-lg">
        <span>Refining...</span>
        <RefreshCw className={`w-5 h-5 ${isRefining ? "animate-spin" : ""}`} />
      </div>
    );
  }

  // If there's a refinement error, show error message
  if (refinementError) {
    return (
      <div className="w-full flex items-center">
        <ErrorDisplay message={refinementError} className="flex-1" />
        <button
          className="ml-2 p-1.5 bg-gray-100 hover:bg-gray-200 rounded-full flex-shrink-0"
          onClick={cancelRefinedAttributes}
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    );
  }

  // If there are refined attributes, show accept/reject options
  return (
    <div className="flex flex-col gap-4 p-4 bg-gray-100 rounded-lg w-full">
      <div className="flex flex-row justify-between items-center">
        <h3 className="font-semibold">Refined Characteristics</h3>
        <div className="flex flex-row gap-2">
          <button
            className="p-2 flex flex-row gap-2 items-center text-sm font-medium bg-gray-600 hover:bg-gray-700 text-white rounded-lg"
            onClick={cancelRefinedAttributes}
          >
            <X className="h-4 w-4" />
            Cancel
          </button>
          <button
            className="p-2 flex flex-row gap-2 items-center text-sm font-medium bg-green-600 hover:bg-green-700 text-white rounded-lg"
            onClick={acceptRefinedAttributes}
          >
            <Check className="h-4 w-4" />
            Accept
          </button>
        </div>
      </div>

      {/* Display refined attributes */}
      <div className="flex flex-col gap-2">
        {refinedAttributes.map((attr, index) => (
          <div key={index} className="p-3 bg-white rounded-lg shadow-sm">
            <p className="font-medium">{attr.attribute}</p>
            <div className="mt-2 flex flex-wrap gap-2">
              {attr.levels.map((level, idx) => (
                <span
                  key={idx}
                  className="px-2 py-1 bg-gray-100 text-sm rounded-md"
                >
                  {level}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
