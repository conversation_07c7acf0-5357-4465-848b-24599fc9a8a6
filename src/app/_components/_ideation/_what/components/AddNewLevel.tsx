"use client";

import React, { useContext, useState, useMemo, useEffect } from "react";
import ExperimentCreationContext from "../../ExperimentCreationContext";
import Checkbox from "@/app/_components/_util/Checkbox";
import { NonEmptyStringSchema } from "@/lib/schemas";

interface AddNewLevelProps {
  attributeIndex: number;
  isMaxLevels: (attributeIndex: number) => boolean;
}

/**
 * Component for adding a new level to an attribute
 */
export default function AddNewLevel({
  attributeIndex,
  isMaxLevels,
}: AddNewLevelProps) {
  const { displayAttributes, setDisplayAttributes } = useContext(
    ExperimentCreationContext
  );
  const [inputValue, setInputValue] = useState("");
  const [validationError, setValidationError] = useState<string | undefined>();

  // Simple validation function
  const validate = (value: string): boolean => {
    try {
      NonEmptyStringSchema.parse(value);
      setValidationError(undefined);
      return true;
    } catch (err) {
      if (err instanceof Error) {
        setValidationError(err.message);
      } else {
        setValidationError("Invalid input");
      }
      return false;
    }
  };

  // Debounced validation
  const debouncedValidate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return (value: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => validate(value), 300);
    };
  }, []);

  // Validate on input change
  useEffect(() => {
    if (inputValue.trim()) {
      debouncedValidate(inputValue);
    } else {
      setValidationError(undefined);
    }
  }, [inputValue, debouncedValidate]);

  // Add a new level to the attribute
  const renderNewLevel = () => {
    // Validate before adding
    const isValidInput = validate(inputValue);
    if (!isValidInput) return;

    const newDisplayAttributes = [...displayAttributes];
    newDisplayAttributes[attributeIndex].levels.push({
      level: inputValue,
      active: false,
    });

    const levelIndex = newDisplayAttributes[attributeIndex].levels.length - 1;

    // If max has not been reached, activate level
    if (!isMaxLevels(attributeIndex)) {
      newDisplayAttributes[attributeIndex].levels[levelIndex].active = true;
    }

    setDisplayAttributes(newDisplayAttributes);
    setInputValue("");
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    renderNewLevel();
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-3">
        <form className="flex flex-row gap-3 w-full" onSubmit={handleSubmit}>
          <div>
            <Checkbox
              selected={false}
              toggleSelected={() => {
                renderNewLevel();
                setInputValue("");
              }}
            />
          </div>
          <input
            className={`flex-grow w-full text-text-dark text-base font-roboto font-medium bg-transparent focus:border-b focus:outline-none ${
              validationError ? "border-red-300" : ""
            }`}
            placeholder="Add new level..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
          />
        </form>
      </div>

      {/* Show validation error */}
      {validationError && (
        <div className="ml-8 text-sm text-red-500 bg-red-50 p-2 rounded border border-red-100">
          {validationError}
        </div>
      )}
    </div>
  );
}
