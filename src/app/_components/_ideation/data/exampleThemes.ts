export const exampleThemes = [
  {
    domain: "mobile",
    questions: [
      "What factors influence smartphone brand loyalty?",
      "What causes people to upgrade their mobile devices?",
      "How do mobile app features affect user engagement?",
    ],
  },
  {
    domain: "workplace",
    questions: [
      "What influences employee job satisfaction?",
      "What factors drive workplace productivity?",
      "What causes employees to change jobs?",
    ],
  },
  {
    domain: "technology",
    questions: [
      "What influences AI tool adoption?",
      "What factors affect technology learning curves?",
      "What drives cloud service adoption?",
    ],
  },
  {
    domain: "social",
    questions: [
      "What influences social media engagement?",
      "What causes online community participation?",
      "What factors affect digital communication preferences?",
    ],
  },
  {
    domain: "environment",
    questions: [
      "What drives sustainable product purchases?",
      "What influences recycling behavior?",
      "What causes people to adopt green technologies?",
    ],
  },
];

// Helper function to get all example queries as a flat array
export const getExampleQueries = (): string[] => {
  return exampleThemes.flatMap((theme) => theme.questions);
};
