import React, { useContext, useEffect, useState, useRef } from "react";
import { User, BarChart3 } from "lucide-react";
import { usePathname } from "next/navigation";
import ExperimentCreationContext from "./ExperimentCreationContext";
import { useApiStatus } from "./_who/ApiStatusContext";
import { baseUrls } from "../../../../tests/config";
import PersonasComponent from "./_who/components/personas/PersonasComponent";
import ErrorDisplay from "./ErrorDisplay";
import { useCountrySpecificLogic } from "./_who/hooks/useCountrySpecificLogic";
import { useDemographicTraits } from "./_who/hooks/useDemographicTraits";
import { useTraitValidation } from "./_who/hooks/useTraitValidation";
import { useSpecialistSelection } from "./_who/hooks/useSpecialistSelection";
import DemographicTraitsForm from "./_who/components/demographic-traits/DemographicTraitsForm";
import DemographicTraitsValidation from "./_who/components/validation/DemographicTraitsValidation";
import SpecialistSelection from "./_who/components/specialist/SpecialistSelection";
import { getErrorMessage } from "./_who/utils/errorMessage";

interface WhoComponentProps {
  onComplete: () => void;
  onBack: () => void;
  existingYear: String | null;
  existingCountry: String | null;
  existingQuestion: String | null;
  setPopulationTraits: (traits: any) => void;
  previousStep: number;
  currentStep: "first" | "second" | "third";
  setCurrentStep: (step: "first" | "second" | "third") => void;
}

const WhoComponent: React.FC<WhoComponentProps> = ({
  onComplete,
  onBack,
  setPopulationTraits,
  existingYear,
  existingCountry,
  existingQuestion,
  currentStep,
  setCurrentStep,
}) => {
  const {
    activeSpecialist,
    setActiveSpecialist,
    displayTraits,
    setDisplayTraits,
    setDisplayAttributes,
    question,
    when,
    where,
    selectedState,
    setRealWorldBrandAttributeCombinations,
    setProductExists,
    selectedSection,
    setSelectedSection,
    personas,
    specialistTraits,
    setSpecialistTraits,
    fileState,
  } = useContext(ExperimentCreationContext);

  const {
    isApiInProgress,
    setIsApiInProgress,
    setIsApiFailed,
    shouldFetchAttributes,
    updateLastParameters,
  } = useApiStatus();

  // State for UI
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [loadingMessage] = useState("");
  const [apiFailure, setApiFailure] = useState(false);
  const pathname = usePathname();
  const { isUSA, stateData } = useCountrySpecificLogic(
    where?.name,
    selectedState
  );

  const {
    // Get handlers
    handleGetAgeRange,
    handleGetIncomeRange,
    getEduValue,
    getGenderValue,
    getChildrenValue,
    getRaceValue,
    getCurrentTraits,
    haveTraitsChanged,
    markTraitsAsValidated,
    // Get state setters
    setAgeRange,
    setIncomeRange,
  } = useDemographicTraits(isUSA, selectedState);

  const {
    validationLoading,
    validationError,
    setValidationError,
    validationResults,
    populationSizeCount,
    selectedPopulationTraits,
    handleValidation,
    handleFinalTraits,
  } = useTraitValidation(
    getCurrentTraits,
    setPopulationTraits,
    setProductExists,
    setCurrentStep,
    haveTraitsChanged,
    markTraitsAsValidated
  );

  const {
    toggleSelectedTrait,
    selectSpecialist,
    getNumSelected,
    addNewTrait,
    activateNewTrait,
    groupTraitsByCategory,
    areTraitsInitialized,
    deleteTrait,
  } = useSpecialistSelection(
    isUSA,
    displayTraits,
    activeSpecialist,
    setActiveSpecialist,
    setDisplayTraits,
    specialistTraits,
    setSpecialistTraits
  );

  // Use a ref to track if an API call is in progress
  const isApiCallInProgressRef = useRef(false);

  // Transform attributes function
  const transformAttributes = (attrs: any[]) => {
    const transformedLevels = attrs.map((attr, attrIndex) => {
      return attr.levels.map((level: string) => {
        return {
          level: level,
          active: attrIndex < 8 ? true : false, // Only show first 8 levels
        };
      });
    });

    return attrs.map((attr, idx) => {
      return {
        attribute: attr.attribute,
        active: idx < 8 ? true : false,
        levels: transformedLevels[idx],
        attribute_type: attr.attribute_type,
      };
    });
  };

  // Fetch attributes data
  const fetchData = async () => {
    // Check if an API call is already in progress using the ref
    if (isApiCallInProgressRef.current) {
      return;
    }

    // Check if API is in progress according to context
    if (isApiInProgress) {
      return;
    }

    // Set both the ref and the state to indicate API call is in progress
    isApiCallInProgressRef.current = true;
    setIsLoading(true);
    setErrorMessage(null);
    setApiFailure(false);
    setIsApiInProgress(true);
    setIsApiFailed(false);

    if (
      question === existingQuestion &&
      when === existingYear &&
      where?.name === existingCountry
    ) {
      setIsLoading(false);
      isApiCallInProgressRef.current = false;
      setIsApiInProgress(false);
      return;
    }

    try {
      setDisplayAttributes([]);
      const fetchAttributesLevels = async () => {
        const attributesLevelsBody = {
          why_prompt: question,
          country: where?.name,
          level_count: 5,
          attribute_count: 7,
          year: when,
        };

        const attributesLevelsResponse = await fetch("/api/attributes-levels", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(attributesLevelsBody),
        });

        if (!attributesLevelsResponse.ok) {
          setDisplayAttributes([]); //if the request fails, clear the displayAttributes
          throw new Error(
            `Error fetching attributes levels: ${attributesLevelsResponse.status}`
          );
        }

        const data = await attributesLevelsResponse.json();

        // Verify that the response contains the expected data
        if (!data || !data.attributes_levels) {
          throw new Error(
            "Invalid response data from attributes-levels endpoint"
          );
        }

        return data;
      };

      const retryFetch = async (
        fetchFunction: () => Promise<any>,
        retries = 3
      ) => {
        for (let i = 0; i <= retries; i++) {
          try {
            return await fetchFunction();
          } catch (error) {
            if (i === retries) throw error;
          }
        }
      };

      const response = await retryFetch(fetchAttributesLevels, 3);
      const { attributes_levels, brand_attribute_combinations } = response;

      setRealWorldBrandAttributeCombinations(
        brand_attribute_combinations || []
      );

      setDisplayAttributes(transformAttributes(attributes_levels));

      // Update last parameters after successful API call
      updateLastParameters(question, where?.name);
    } catch (error: any) {
      setIsApiFailed(true);
      console.error("Error fetching data:", error);
      setErrorMessage("Failed to fetch attributes. Please try again.");
      setApiFailure(true);
    } finally {
      // Reset both the ref and the state
      isApiCallInProgressRef.current = false;
      setIsApiInProgress(false);
      setIsLoading(false);
    }
  };

  function shouldCallApi(pathname: string): boolean {
    if (pathname === "/ideation") {
      return true;
    }

    const parts = pathname.split("/");
    const id = parts[2];

    if (id && id.length > 15) {
      return true;
    }

    return false;
  }

  // Simplified useEffect for fetching data
  useEffect(() => {
    // Skip if API call is in progress
    if (isApiCallInProgressRef.current || isApiInProgress) {
      return;
    }

    // Determine if we should fetch attributes based on current parameters
    if (
      shouldCallApi(pathname) &&
      shouldFetchAttributes(question, where?.name, fileState)
    ) {
      fetchData();
    }
  }, [
    question,
    where,
    pathname,
    fileState,
    shouldFetchAttributes,
    isApiInProgress,
  ]);

  // Handle back button
  const handleBack = () => {
    setValidationError(null);

    if (currentStep === "second") {
      setCurrentStep("first");
    } else if (currentStep === "third") {
      if (!isUSA) {
        onBack();
      }
      if (populationSizeCount < 300) {
        setCurrentStep("second");
      } else {
        setCurrentStep("first");
      }
    } else {
      onBack();
    }
  };

  // Handle continue button
  const handleContinue = async () => {
    setValidationError(null);

    if (selectedSection === "personas") {
      if (personas.length === 0) {
        setValidationError("Please create at least one persona");
        return;
      }

      onComplete();
      return;
    }

    if (currentStep === "first" && isUSA) {
      try {
        const validationData = await handleValidation();

        // Check if validation data is null or missing required properties
        if (!validationData || !validationData.original) {
          setValidationError(
            "Validation failed. Please check your inputs and try again."
          );
          return;
        }

        if (validationData.original.population_size <= 300) {
          setCurrentStep("second");
        } else {
          setCurrentStep("third");
          if (setProductExists) {
            setProductExists(validationData.original);
          }
          // Set both selectedPopulationTraits and populationTraits when skipping to step three
          handleFinalTraits(validationData.original);
          setPopulationTraits(validationData.original.population_traits);
        }
      } catch (error) {
        console.error("Error during validation:", error);
        setValidationError(
          "Failed to validate population traits. Please try again."
        );
      }
    } else if (currentStep === "second") {
      if (
        !selectedPopulationTraits ||
        selectedPopulationTraits.population_size === 0
      ) {
        setValidationError("Select a non-zero population configuration");
        return;
      }
      if (selectedPopulationTraits?.population_traits) {
        setPopulationTraits(selectedPopulationTraits.population_traits);
      }
      setCurrentStep("third");
    } else {
      if (getNumSelected() > 0 || isUSA) {
        if (selectedPopulationTraits?.population_traits) {
          setPopulationTraits(selectedPopulationTraits.population_traits);
        } else {
          // Use the current traits as fallback
          const currentTraits = getCurrentTraits();
          if (currentTraits) {
            setPopulationTraits(currentTraits);
          }
        }
        onComplete();
      }
    }
  };

  useEffect(() => {
    setValidationError(null);

    if (validationResults) {
      setValidationError(null);
    }
  }, [currentStep, selectedPopulationTraits, validationResults]);

  useEffect(() => {
    setValidationError(null);
  }, [selectedState, setValidationError]);

  // Track previous state for comparison
  const prevStateRef = useRef<string | null>(null);

  // Update range values in context when stateData changes
  useEffect(() => {
    if (!stateData) return;

    // Check if the state has changed
    const stateChanged = prevStateRef.current !== selectedState;

    // Update the previous state reference
    prevStateRef.current = selectedState;

    // Always reset age and income ranges when state changes
    if (stateChanged) {
      // Clear any validation errors when state changes
      setValidationError(null);

      // Set age range to state-specific values
      const newAgeRange: [number, number] = [
        stateData.Age.min,
        stateData.Age.max,
      ];
      setAgeRange(newAgeRange);

      // Set income range to state-specific values
      const newIncomeRange: [number, number] = [
        stateData["Household income"].min,
        stateData["Household income"]["95th_percentile"],
      ];
      setIncomeRange(newIncomeRange);
    } else {
      // Get current traits directly to avoid any potential race conditions
      const currentTraits = getCurrentTraits();

      // Only adjust ranges if they're outside valid bounds for current state
      let ageRangeUpdated = false;
      let incomeRangeUpdated = false;
      const currentAgeRange = currentTraits.age;
      const currentIncomeRange = currentTraits.household_income;

      // Create new ranges with values clamped to valid bounds
      const newAgeRange: [number, number] = [
        Math.max(currentAgeRange[0], stateData.Age.min),
        Math.min(currentAgeRange[1], stateData.Age.max),
      ];

      const newIncomeRange: [number, number] = [
        Math.max(currentIncomeRange[0], stateData["Household income"].min),
        Math.min(
          currentIncomeRange[1],
          stateData["Household income"]["95th_percentile"]
        ),
      ];

      // Only update if the ranges have actually changed
      if (
        newAgeRange[0] !== currentAgeRange[0] ||
        newAgeRange[1] !== currentAgeRange[1]
      ) {
        setAgeRange(newAgeRange);
        ageRangeUpdated = true;
      }

      if (
        newIncomeRange[0] !== currentIncomeRange[0] ||
        newIncomeRange[1] !== currentIncomeRange[1]
      ) {
        setIncomeRange(newIncomeRange);
        incomeRangeUpdated = true;
      }
    }
  }, [
    stateData,
    selectedState,
    setAgeRange,
    setIncomeRange,
    getCurrentTraits,
    setValidationError,
  ]);

  // Check if continue button should be disabled
  const continueDisabled = () => {
    return (
      (getNumSelected() < 1 && !isUSA) || // Only check trait selection for non-USA
      isLoading ||
      isApiInProgress ||
      apiFailure ||
      validationLoading
    );
  };

  // Section change handling
  const [showSectionChangeAlert, setShowSectionChangeAlert] = useState(false);
  const [sectionToChangeTo, setSectionToChangeTo] = useState<
    "characteristics" | "personas" | null
  >(null);

  const handleSectionChange = (newSection: "characteristics" | "personas") => {
    if (selectedSection !== newSection) {
      setSectionToChangeTo(newSection);
      setShowSectionChangeAlert(true);
    }
  };

  const confirmSectionChange = () => {
    if (sectionToChangeTo) {
      setSelectedSection(sectionToChangeTo);
    }
    setShowSectionChangeAlert(false);
    setSectionToChangeTo(null);
  };

  return (
    <>
      <div className="flex flex-col h-full w-full max-w-4xl">
        {/* Section Selector */}
        <div className="mb-6">
          {typeof window !== "undefined" &&
            !window.location.href.startsWith(baseUrls.prod) && (
              <div className="flex gap-4">
                <button
                  className={`flex flex-col items-center justify-center gap-2 h-32 w-full rounded-lg text-center transition-all ${
                    selectedSection === "characteristics"
                      ? "bg-primary text-white"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200 border border-card-border"
                  }`}
                  onClick={() => handleSectionChange("characteristics")}
                >
                  <BarChart3 className="w-8 h-8" />
                  <span className="font-medium">Define characteristics</span>
                </button>
                <button
                  className={`flex flex-col items-center justify-center gap-2 w-full h-32 rounded-lg text-center transition-all ${
                    selectedSection === "personas"
                      ? "bg-primary text-white"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200 border border-card-border "
                  }`}
                  onClick={() => handleSectionChange("personas")}
                >
                  <User className="w-8 h-8" />
                  <span className="font-medium">Create your personas</span>
                </button>
              </div>
            )}
        </div>

        {/* Section Change Alert */}
        {showSectionChangeAlert && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Warning</h3>
              <p className="mb-6">
                Switching sections will clear your current progress. Only the
                data from the selected section will be used in the next step.
                Are you sure you want to continue?
              </p>
              <div className="flex justify-end gap-4">
                <button
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
                  onClick={() => {
                    setShowSectionChangeAlert(false);
                    setSectionToChangeTo(null);
                  }}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
                  onClick={confirmSectionChange}
                >
                  Continue
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Characteristics Section */}
        {selectedSection === "characteristics" && (
          <>
            {/* First View - Demographic Traits Form */}
            {currentStep === "first" && isUSA && (
              <DemographicTraitsForm
                onContinue={handleContinue}
                onBack={handleBack}
                stateData={stateData}
                isUSA={isUSA}
                selectedState={selectedState}
                validationError={validationError}
                validationLoading={validationLoading}
                handleValidation={handleValidation}
                handleGetAgeRange={handleGetAgeRange}
                handleGetIncomeRange={handleGetIncomeRange}
                getEduValue={getEduValue}
                getGenderValue={getGenderValue}
                getChildrenValue={getChildrenValue}
                getRaceValue={getRaceValue}
              />
            )}

            {/* Second View - Validation Results */}
            {currentStep === "second" && isUSA && (
              <DemographicTraitsValidation
                onContinue={handleContinue}
                onBack={handleBack}
                traits={getCurrentTraits()}
                validationResults={validationResults}
                validationError={validationError}
                isLoading={isLoading}
                question={question}
                handleFinalTraits={handleFinalTraits}
              />
            )}

            {/* Third View - Specialist Selection */}
            {(currentStep === "third" || !isUSA) && (
              <SpecialistSelection
                onComplete={onComplete}
                onBack={handleBack}
                isUSA={isUSA}
                question={question}
                displayTraits={displayTraits}
                activeSpecialist={activeSpecialist}
                selectSpecialist={selectSpecialist}
                toggleSelectedTrait={toggleSelectedTrait}
                addNewTrait={addNewTrait}
                activateNewTrait={activateNewTrait}
                groupTraitsByCategory={groupTraitsByCategory}
                getNumSelected={getNumSelected}
                areTraitsInitialized={areTraitsInitialized}
                isLoading={isLoading}
                errorMessage={errorMessage}
                loadingMessage={loadingMessage}
                deleteTrait={deleteTrait}
              />
            )}
          </>
        )}

        {/* Personas Section */}
        {selectedSection === "personas" && (
          <div>
            <PersonasComponent question={question} />
            {validationError && (
              <ErrorDisplay message={validationError} className="w-full" />
            )}
          </div>
        )}

        {/* No traits selected warning */}
        {getNumSelected() === 0 &&
          currentStep === "third" &&
          !isUSA &&
          areTraitsInitialized() && (
            <ErrorDisplay
              message="No traits selected. Please select at least one trait to continue."
              fixed={true}
              className="w-full"
            />
          )}

        {(() => {
          const error = getErrorMessage({
            validationLoading,
            validationResults,
            currentStep,
            validationError,
            errorMessage,
            isLoading,
          });
          return (
            error &&
            !validationLoading &&
            !validationResults && (
              <ErrorDisplay
                message={error.message || ""}
                showTryAgain={error.showTryAgain}
                onTryAgain={handleValidation}
                fixed={true}
                className="w-full"
              />
            )
          );
        })()}

        {/* Navigation buttons */}
        <div className="sticky bottom-0 flex w-full justify-between bg-background py-2">
          <button
            className="font-inter text-base font-medium py-2.5 px-6 bg-white hover:bg-secondary-grey border border-card-border shadow-sm rounded-lg"
            onClick={handleBack}
          >
            Back
          </button>
          <button
            className={`font-inter text-base font-semibold py-2.5 ${
              validationLoading ||
              (currentStep === "third" && continueDisabled())
                ? "bg-[#ECEDFB] cursor-not-allowed"
                : "bg-primary hover:bg-primary-dark"
            } text-white rounded-lg shadow-sm w-[170px] flex items-center justify-center gap-2`}
            onClick={handleContinue}
            disabled={
              validationLoading ||
              (currentStep === "third" && continueDisabled()) ||
              (selectedSection === "personas" && personas.length === 0)
            }
          >
            Continue
          </button>
        </div>
      </div>
    </>
  );
};

export default WhoComponent;
