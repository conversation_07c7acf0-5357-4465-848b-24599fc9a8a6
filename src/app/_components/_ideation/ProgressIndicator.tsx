import { CheckIcon } from "@heroicons/react/20/solid";

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

interface ProgressIndicatorProps {
  steps: {
    name: string;
    status: string;
  }[];
  // eslint-disable-next-line no-unused-vars
  setCurrStep: (step: number) => void;
}

const ProgressIndicator = ({ steps, setCurrStep }: ProgressIndicatorProps) => {
  return (
    <div>
      <div className="flex flex-col justify-center items-center h-full w-full">
        {/* Circles Row */}
        <ol role="list" className="flex items-center w-full">
          {steps.map((step, stepIdx) => (
            <li
              key={step.name}
              className="w-28 relative pr-8 sm:pr-20 flex flex-col items-center"
            >
              <div
                className="absolute inset-0 top-1/2 -translate-y-1/2 flex items-center z-0"
                aria-hidden="true"
              >
                <div
                  className={`${stepIdx === steps.length - 1 ? "h-0" : "h-0.5"} w-full bg-gray-200`}
                />
              </div>
              {step.status === "complete" ? (
                <button
                  onMouseDown={() => setCurrStep(stepIdx)}
                  className="relative flex h-8 w-8 items-center justify-center rounded-full bg-primary hover:bg-text-dark z-10"
                >
                  <CheckIcon
                    className="h-5 w-5 text-white"
                    aria-hidden="true"
                  />
                </button>
              ) : step.status === "current" ? (
                <button
                  onMouseDown={() => setCurrStep(stepIdx)}
                  className="relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-primary bg-white z-10"
                  aria-current="step"
                >
                  <span
                    className="h-2.5 w-2.5 rounded-full bg-primary"
                    aria-hidden="true"
                  />
                  <span className="sr-only">{step.name}</span>
                </button>
              ) : (
                <a
                  href="#"
                  className="group relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-300 bg-white hover:border-gray-400 z-10"
                >
                  <span
                    className="h-2.5 w-2.5 rounded-full bg-transparent group-hover:bg-gray-300"
                    aria-hidden="true"
                  />
                </a>
              )}
            </li>
          ))}
        </ol>
        {/* Labels Row */}
        <ol role="list" className="flex items-center w-full mt-2">
          {steps.map((step, stepIdx) => (
            <li
              key={step.name}
              className="w-28 pr-8 sm:pr-20 flex justify-center"
            >
              <p className="text-sm text-primary font-roboto text-center break-words max-w-[7rem]">
                {step.name}
              </p>
            </li>
          ))}
        </ol>
      </div>
    </div>
  );
};

export default ProgressIndicator;
