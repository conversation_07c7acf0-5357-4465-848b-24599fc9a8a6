"use client";

import React, { useContext } from "react";
import ExperimentCreationContext from "./ExperimentCreationContext";
import { Country, LLMModel } from "./objects";
import ResearchQuestionSection from "./ResearchQuestionSection";
import Map from "./_whenwhere/Map";
import CountrySelector from "./_whenwhere/CountrySelector";
import StateSelector from "./_whenwhere/StateSelector";
import WhenInput from "./_whenwhere/WhenInput";
import FileUploadSection from "./_whenwhere/FileUploadSection";
import NavigationButtons from "./_whenwhere/NavigationButtons";
import { useFileUpload } from "./_whenwhere/hooks/useFileUpload";
import { useWhenWhere } from "./_whenwhere/hooks/useWhenWhere";

interface WhenWhereComponentProps {
  countries: Country[];
  onBack: () => void;
  onComplete: () => void;
  models: LLMModel[]; // Kept for API compatibility but not used in this component
}

/**
 * WhenWhereComponent handles the "When and Where" step of the experiment creation process.
 * It allows users to specify when and where their audience is located and optionally upload
 * custom attributes via CSV.
 */
const WhenWhereComponent: React.FC<WhenWhereComponentProps> = ({
  countries,
  onBack,
  onComplete,
  // models is not used in this component
}) => {
  const {
    when,
    where,
    setWhen,
    setWhere,
    question,
    selectedState,
    setSelectedState,
    setDisplayAttributes,
    fileState,
    setFileState,
  } = useContext(ExperimentCreationContext);

  // Custom hooks for handling file upload and when/where state
  const { handleFileUpload, handleRemoveFile, handleDownloadSampleCSV } =
    useFileUpload({
      fileState,
      setFileState,
      setDisplayAttributes,
    });

  const { isUSA, handleWhen, handleCountryChange, saveData, isFormValid } =
    useWhenWhere({
      initialWhen: when,
      initialWhere: where,
      initialSelectedState: selectedState,
      setWhen,
      setWhere,
      setSelectedState,
    });

  const handleBack = () => {
    saveData();
    onBack();
  };

  return (
    <div className="flex flex-col gap-6 items-center max-w-4xl w-full">
      <ResearchQuestionSection
        question={question}
        heading="When and where is your audience?"
        subheading="Feel free to be creative."
        headingId="whenAndWhere-Heading"
      />

      {/* SELECTORS */}
      <div className="flex flex-col items-stretch gap-4 justify-start w-full">
        {/* WHEN */}
        <WhenInput value={when} onChange={handleWhen} />

        {/* WHERE */}
        <CountrySelector
          countries={countries}
          selectedCountry={where}
          setSelectedCountry={handleCountryChange}
        />

        {/* State selector for USA */}
        {isUSA && (
          <StateSelector
            selectedState={selectedState}
            onStateChange={setSelectedState}
          />
        )}
      </div>

      {/* FILE UPLOAD */}
      <FileUploadSection
        onFileSelect={handleFileUpload}
        onFileRemove={handleRemoveFile}
        onDownloadSample={handleDownloadSampleCSV}
        selectedFile={fileState.file}
        error={fileState.error}
      />

      {/* MAP */}
      <div className="">
        <Map
          selected={where !== undefined}
          x={where?.coords.x}
          y={where?.coords.y}
        />
      </div>

      {/* NAVIGATION */}
      <NavigationButtons
        onBack={handleBack}
        onContinue={onComplete}
        isContinueDisabled={!isFormValid()}
      />
    </div>
  );
};

export default WhenWhereComponent;
