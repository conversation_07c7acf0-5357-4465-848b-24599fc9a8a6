"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
  useEffect,
} from "react";
import { PopulationTraits } from "../../objects";
import { getTraitConfig } from "../config/traitConfig";
import { DemographicTraitsFormSchema, validateFormFields } from "@/lib/schemas";

interface DemographicTraitsContextType {
  // State
  ageRange: [number, number];
  incomeRange: [number, number];
  educationValues: string[];
  genderValues: string[];
  childrenValues: string[];
  raceValues: string[];
  traitsChanged: boolean;
  lastValidatedTraits: PopulationTraits | null;

  // Validation state
  validationErrors: Record<string, string>;
  isValid: boolean;
  isValidating: boolean;

  // Actions
  setAgeRange: (range: [number, number]) => void;
  setIncomeRange: (range: [number, number]) => void;
  updateEducationValues: (
    value: string,
    isActive: boolean
  ) => string[] | undefined;
  updateGenderValues: (
    value: string,
    isActive: boolean
  ) => string[] | undefined;
  updateChildrenValues: (
    value: string,
    isActive: boolean
  ) => string[] | undefined;
  updateRaceValues: (value: string, isActive: boolean) => string[] | undefined;
  getCurrentTraits: () => PopulationTraits;
  resetTraits: () => void;
  haveTraitsChanged: () => boolean;
  markTraitsAsValidated: () => void;

  // Validation actions
  validateTraits: () => Promise<boolean>;
  clearValidationErrors: () => void;
}

export const DemographicTraitsContext = createContext<
  DemographicTraitsContextType | undefined
>(undefined);

interface DemographicTraitsProviderProps {
  children: ReactNode;
  selectedState: string | null;
  stateData?: {
    Age: {
      min: number;
      max: number;
      "90th_percentile": number;
      "95th_percentile": number;
    };
    "Household income": {
      min: number;
      max: number;
      "90th_percentile": number;
      "95th_percentile": number;
    };
  };
}

export const DemographicTraitsProvider: React.FC<
  DemographicTraitsProviderProps
> = ({ children, selectedState, stateData }) => {
  // Initialize state with default values that match the payload
  const [ageRange, setAgeRangeState] = useState<[number, number]>([18, 95]);
  const [incomeRange, setIncomeRangeState] = useState<[number, number]>([
    0, 417800,
  ]);
  const [educationValues, setEducationValues] = useState<string[]>([]);
  const [genderValues, setGenderValues] = useState<string[]>([]);
  const [childrenValues, setChildrenValues] = useState<string[]>([]);
  const [raceValues, setRaceValues] = useState<string[]>([]);
  const [traitsChanged, setTraitsChanged] = useState<boolean>(true);
  const [lastValidatedTraits, setLastValidatedTraits] =
    useState<PopulationTraits | null>(null);

  // Validation state
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [isValidating, setIsValidating] = useState<boolean>(false);

  // Initialize traits with default values from trait config
  const initializeTraits = useCallback(() => {
    const educationConfig = getTraitConfig("education");
    const genderConfig = getTraitConfig("gender");
    const childrenConfig = getTraitConfig("children");
    const raceConfig = getTraitConfig("race");

    setEducationValues(educationConfig.defaultValues);
    setGenderValues(genderConfig.defaultValues);
    setChildrenValues(childrenConfig.defaultValues);
    setRaceValues(raceConfig.defaultValues);

    // Note: Age and income ranges are reset separately in resetTraits
    // to ensure they're properly reset when state changes
  }, []);

  // Initialize traits on first render
  useEffect(() => {
    initializeTraits();
  }, [initializeTraits]);

  // Reset traits when state changes
  const resetTraits = useCallback(() => {
    // Reset checkbox traits only
    initializeTraits();

    // Note: We don't reset age and income ranges here anymore
    // They are now managed by the WhoComponent based on stateData

    setTraitsChanged(true);
  }, [initializeTraits]);

  // Auto-reset traits when selectedState changes
  useEffect(() => {
    resetTraits();
  }, [selectedState, resetTraits]);

  // Generic function to update trait values
  const updateTraitValues = useCallback(
    (
      traitType: string,
      value: string,
      isActive: boolean,
      currentValues: string[],
      setValues: React.Dispatch<React.SetStateAction<string[]>>
    ) => {
      const traitConfig = getTraitConfig(traitType);
      let newValues: string[];

      if (isActive) {
        // Add the value if it's not already in the array
        if (!currentValues.includes(value)) {
          newValues = [...currentValues, value];
        } else {
          // Value already exists, no change needed
          return currentValues;
        }
      } else {
        // Remove the value
        newValues = currentValues.filter((v) => v !== value);

        // Prevent unchecking all checkboxes if selection is required
        if (newValues.length === 0 && traitConfig.requireSelection) {
          return undefined; // Return undefined to indicate the update was rejected
        }
      }

      setValues(newValues);
      setTraitsChanged(true);
      return newValues;
    },
    []
  );

  // Update education values
  const updateEducationValues = useCallback(
    (value: string, isActive: boolean) => {
      const newValues = updateTraitValues(
        "education",
        value,
        isActive,
        educationValues,
        setEducationValues
      );
      return newValues;
    },
    [educationValues, updateTraitValues]
  );

  // Update gender values
  const updateGenderValues = useCallback(
    (value: string, isActive: boolean) => {
      const newValues = updateTraitValues(
        "gender",
        value,
        isActive,
        genderValues,
        setGenderValues
      );
      return newValues;
    },
    [genderValues, updateTraitValues]
  );

  // Update children values
  const updateChildrenValues = useCallback(
    (value: string, isActive: boolean) => {
      const newValues = updateTraitValues(
        "children",
        value,
        isActive,
        childrenValues,
        setChildrenValues
      );
      return newValues;
    },
    [childrenValues, updateTraitValues]
  );

  // Update race values
  const updateRaceValues = useCallback(
    (value: string, isActive: boolean) => {
      const newValues = updateTraitValues(
        "race",
        value,
        isActive,
        raceValues,
        setRaceValues
      );
      return newValues;
    },
    [raceValues, updateTraitValues]
  );

  // Custom setters for range values that mark traits as changed
  const setAgeRange = useCallback((range: [number, number]) => {
    setAgeRangeState(range);
    setTraitsChanged(true);
  }, []);

  const setIncomeRange = useCallback((range: [number, number]) => {
    setIncomeRangeState(range);
    setTraitsChanged(true);
  }, []);

  // Get current traits
  const getCurrentTraits = useCallback((): PopulationTraits => {
    return {
      age: ageRange,
      household_income: incomeRange,
      education_level: educationValues,
      gender: genderValues,
      number_of_children: childrenValues,
      racial_group: raceValues,
      state: selectedState && selectedState !== "USA" ? selectedState : null,
    };
  }, [
    ageRange,
    incomeRange,
    educationValues,
    genderValues,
    childrenValues,
    raceValues,
    selectedState,
  ]);

  // Check if traits have changed since last validation
  const haveTraitsChanged = useCallback(() => {
    return traitsChanged;
  }, [traitsChanged]);

  // Mark traits as validated
  const markTraitsAsValidated = useCallback(() => {
    const currentTraits = getCurrentTraits();
    setLastValidatedTraits(currentTraits);
    setTraitsChanged(false);
  }, [getCurrentTraits]);

  // Validation functions
  const validateTraits = useCallback(async (): Promise<boolean> => {
    setIsValidating(true);
    setValidationErrors({});

    try {
      const currentTraits = getCurrentTraits();

      // Convert to the format expected by our schema
      const formData = {
        age_range: currentTraits.age,
        income_range: currentTraits.household_income,
        education_levels: currentTraits.education_level,
        genders: currentTraits.gender,
        racial_groups: currentTraits.racial_group,
        number_of_children: currentTraits.number_of_children,
      };

      const validationResult = validateFormFields(
        formData,
        DemographicTraitsFormSchema
      );

      if (validationResult.success) {
        setValidationErrors({});
        return true;
      } else {
        setValidationErrors(validationResult.errors || {});
        return false;
      }
    } catch (error) {
      console.error("Validation error:", error);
      setValidationErrors({
        general: "Validation failed. Please check your inputs.",
      });
      return false;
    } finally {
      setIsValidating(false);
    }
  }, [getCurrentTraits]);

  const clearValidationErrors = useCallback(() => {
    setValidationErrors({});
  }, []);

  // Compute isValid based on current validation state
  const isValid = Object.keys(validationErrors).length === 0 && !isValidating;

  return (
    <DemographicTraitsContext.Provider
      value={{
        ageRange,
        incomeRange,
        educationValues,
        genderValues,
        childrenValues,
        raceValues,
        traitsChanged,
        lastValidatedTraits,
        validationErrors,
        isValid,
        isValidating,
        setAgeRange,
        setIncomeRange,
        updateEducationValues,
        updateGenderValues,
        updateChildrenValues,
        updateRaceValues,
        getCurrentTraits,
        resetTraits,
        haveTraitsChanged,
        markTraitsAsValidated,
        validateTraits,
        clearValidationErrors,
      }}
    >
      {children}
    </DemographicTraitsContext.Provider>
  );
};

export const useDemographicTraits = () => {
  const context = useContext(DemographicTraitsContext);
  if (context === undefined) {
    throw new Error(
      "useDemographicTraits must be used within a DemographicTraitsProvider"
    );
  }
  return context;
};
