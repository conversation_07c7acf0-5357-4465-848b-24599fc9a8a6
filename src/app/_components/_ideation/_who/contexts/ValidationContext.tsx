"use client";

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import {
  FinalSelectedPopulationTraits,
  ValidationResponse,
} from "@/lib/schemas";

interface ValidationContextType {
  // State
  selectedOption: "suggestion" | "original" | "one_trait_change";
  selectedTrait: string | null;
  validationResults: ValidationResponse | null;
  selectedPopulationTraits: FinalSelectedPopulationTraits | null;
  populationSizeCount: number;

  // Actions
  setSelectedOption: (
    option: "suggestion" | "original" | "one_trait_change"
  ) => void;
  setSelectedTrait: (trait: string | null) => void;
  setValidationResults: (results: ValidationResponse | null) => void;
  setSelectedPopulationTraits: (
    traits: FinalSelectedPopulationTraits | null
  ) => void;
  setPopulationSizeCount: (count: number) => void;
  handleFinalTraits: (finalData: FinalSelectedPopulationTraits) => void;
}

export const ValidationContext = createContext<
  ValidationContextType | undefined
>(undefined);

interface ValidationProviderProps {
  children: ReactNode;
  initialValidationResults?: ValidationResponse | null;
  onFinalTraitsChange?: (finalData: FinalSelectedPopulationTraits) => void;
}

export const ValidationProvider: React.FC<ValidationProviderProps> = ({
  children,
  initialValidationResults = null,
  onFinalTraitsChange,
}) => {
  // Initialize state with default values or provided initialValidationResults
  const [selectedOption, setSelectedOption] = useState<
    "suggestion" | "original" | "one_trait_change"
  >("suggestion");

  const [selectedTrait, setSelectedTrait] = useState<string | null>(null);

  const [validationResults, setValidationResults] =
    useState<ValidationResponse | null>(initialValidationResults);

  const [selectedPopulationTraits, setSelectedPopulationTraits] =
    useState<FinalSelectedPopulationTraits | null>(null);

  const [populationSizeCount, setPopulationSizeCount] = useState<number>(0);

  // Handle option change
  const handleOptionChange = (
    option: "suggestion" | "original" | "one_trait_change"
  ) => {
    setSelectedOption(option);

    // Reset selected trait when not using one_trait_change
    if (option !== "one_trait_change") {
      setSelectedTrait(null);
    }
  };

  // Handle trait change
  const handleTraitChange = (trait: string | null) => {
    setSelectedTrait(trait);
  };

  // Handle final traits selection
  const handleFinalTraits = (finalData: FinalSelectedPopulationTraits) => {
    setSelectedPopulationTraits(finalData);

    if (onFinalTraitsChange) {
      onFinalTraitsChange(finalData);
    }
  };

  return (
    <ValidationContext.Provider
      value={{
        selectedOption,
        selectedTrait,
        validationResults,
        selectedPopulationTraits,
        populationSizeCount,
        setSelectedOption: handleOptionChange,
        setSelectedTrait: handleTraitChange,
        setValidationResults,
        setSelectedPopulationTraits,
        setPopulationSizeCount,
        handleFinalTraits,
      }}
    >
      {children}
    </ValidationContext.Provider>
  );
};

export const useValidation = () => {
  const context = useContext(ValidationContext);
  if (context === undefined) {
    throw new Error("useValidation must be used within a ValidationProvider");
  }
  return context;
};
