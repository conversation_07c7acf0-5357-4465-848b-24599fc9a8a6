import React, { createContext, useState, useContext } from "react";

interface ApiStatusContextValue {
  isApiInProgress: boolean;
  isApiFailed: boolean;
  lastQuestion: string | null;
  lastWhere: string | null;
  setIsApiInProgress: React.Dispatch<React.SetStateAction<boolean>>;
  setIsApiFailed: React.Dispatch<React.SetStateAction<boolean>>;
  shouldFetchAttributes: (
    question: string,
    where: string | undefined,
    fileState: { file: File | null; data: any[] | null; error: string | null }
  ) => boolean;
  updateLastParameters: (question: string, where: string | undefined) => void;
}

export const ApiStatusContext = createContext<ApiStatusContextValue>({
  isApiInProgress: false,
  isApiFailed: false,
  lastQuestion: null,
  lastWhere: null,
  setIsApiInProgress: () => {},
  setIsApiFailed: () => {},
  shouldFetchAttributes: () => true,
  updateLastParameters: () => {},
});

export const ApiStatusProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isApiInProgress, setIsApiInProgress] = useState(false);
  const [isApiFailed, setIsApiFailed] = useState(false);
  const [lastQuestion, setLastQuestion] = useState<string | null>(null);
  const [lastWhere, setLastWhere] = useState<string | null>(null);

  // Function to determine if attributes should be fetched
  const shouldFetchAttributes = (
    question: string,
    where: string | undefined,
    fileState: { file: File | null; data: any[] | null; error: string | null }
  ): boolean => {
    // Skip API call if user uploaded CSV
    if (fileState.file !== null) {
      return false;
    }

    // If any of the parameters have changed, or we haven't fetched before, we should fetch
    const shouldFetch =
      lastQuestion !== question ||
      lastWhere !== where ||
      lastQuestion === null ||
      lastWhere === null;

    return shouldFetch;
  };

  // Function to update the last parameters after a successful API call
  const updateLastParameters = (
    question: string,
    where: string | undefined
  ): void => {
    setLastQuestion(question);
    setLastWhere(where || null);
  };

  return (
    <ApiStatusContext.Provider
      value={{
        isApiInProgress,
        isApiFailed,
        lastQuestion,
        lastWhere,
        setIsApiInProgress,
        setIsApiFailed,
        shouldFetchAttributes,
        updateLastParameters,
      }}
    >
      {children}
    </ApiStatusContext.Provider>
  );
};

// Custom hook for using the ApiStatus context
export const useApiStatus = () => {
  const context = useContext(ApiStatusContext);
  if (context === undefined) {
    throw new Error("useApiStatus must be used within an ApiStatusProvider");
  }
  return context;
};
