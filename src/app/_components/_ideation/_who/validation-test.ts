/**
 * Test file to verify Zod validation implementation in WhoComponent
 * This file demonstrates the systematic Zod usage and can be used for testing
 */

import {
  PopulationTraitsSchema,
  PopulationTraitsValidationRequestSchema,
  ValidationResponseSchema,
  FinalSelectedPopulationTraitsSchema,
  validateFormFields,
} from "@/lib/schemas";

// Test data for validation
const testPopulationTraits = {
  state: "California",
  gender: ["Male", "Female"],
  age: [25, 65],
  education_level: ["Bachelors", "Masters"],
  racial_group: ["White", "Asian or Pacific Islander"],
  household_income: [50000, 150000],
  number_of_children: ["0", "1", "2"],
};

const testValidationRequest = {
  age: [25, 65],
  education_level: ["Bachelors", "Masters"],
  gender: ["Male", "Female"],
  household_income: [50000, 150000],
  number_of_children: ["0", "1", "2"],
  number_of_records: 300,
  racial_group: ["White", "Asian or Pacific Islander"],
  state: "California",
};

const testValidationResponse = {
  original: {
    population_size: 1500,
    population_traits: {
      state: "California",
      gender: ["Male", "Female"],
      age: [25, 65],
      education_level: ["Bachelors", "Masters"],
      racial_group: ["White", "Asian or Pacific Islander"],
      household_income: [50000, 150000],
      number_of_children: ["0", "1", "2"],
    },
  },
  suggestion: {
    population_size: 2000,
    population_traits: {
      state: "California",
      gender: ["Male", "Female"],
      age: [25, 70],
      education_level: ["Bachelors", "Masters", "High School Diploma"],
      racial_group: ["White", "Asian or Pacific Islander"],
      household_income: [40000, 160000],
      number_of_children: ["0", "1", "2"],
    },
  },
  one_trait_change: {
    age_expanded: {
      population_size: 1800,
      population_traits: {
        state: "California",
        gender: ["Male", "Female"],
        age: [18, 75],
        education_level: ["Bachelors", "Masters"],
        racial_group: ["White", "Asian or Pacific Islander"],
        household_income: [50000, 150000],
        number_of_children: ["0", "1", "2"],
      },
    },
  },
};

/**
 * Test function to validate population traits
 */
export function testPopulationTraitsValidation() {
  console.log("Testing PopulationTraits validation...");
  
  const result = validateFormFields(testPopulationTraits, PopulationTraitsSchema);
  
  if (result.success) {
    console.log("✅ PopulationTraits validation passed");
    console.log("Validated data:", result.data);
  } else {
    console.log("❌ PopulationTraits validation failed");
    console.log("Errors:", result.errors);
  }
  
  return result;
}

/**
 * Test function to validate API request payload
 */
export function testValidationRequestValidation() {
  console.log("Testing ValidationRequest validation...");
  
  const result = validateFormFields(
    testValidationRequest,
    PopulationTraitsValidationRequestSchema
  );
  
  if (result.success) {
    console.log("✅ ValidationRequest validation passed");
    console.log("Validated data:", result.data);
  } else {
    console.log("❌ ValidationRequest validation failed");
    console.log("Errors:", result.errors);
  }
  
  return result;
}

/**
 * Test function to validate API response
 */
export function testValidationResponseValidation() {
  console.log("Testing ValidationResponse validation...");
  
  const result = validateFormFields(
    testValidationResponse,
    ValidationResponseSchema
  );
  
  if (result.success) {
    console.log("✅ ValidationResponse validation passed");
    console.log("Validated data:", result.data);
  } else {
    console.log("❌ ValidationResponse validation failed");
    console.log("Errors:", result.errors);
  }
  
  return result;
}

/**
 * Test function with invalid data to verify error handling
 */
export function testInvalidDataValidation() {
  console.log("Testing invalid data validation...");
  
  const invalidData = {
    state: null, // Valid
    gender: [], // Invalid - empty array
    age: [65, 25], // Invalid - min > max
    education_level: ["Invalid Level"], // Invalid value
    racial_group: ["White"],
    household_income: [-1000, 150000], // Invalid - negative income
    number_of_children: ["0"],
  };
  
  const result = validateFormFields(invalidData, PopulationTraitsSchema);
  
  if (!result.success) {
    console.log("✅ Invalid data correctly rejected");
    console.log("Validation errors:", result.errors);
  } else {
    console.log("❌ Invalid data was incorrectly accepted");
  }
  
  return result;
}

/**
 * Run all validation tests
 */
export function runAllValidationTests() {
  console.log("🧪 Running Zod validation tests for WhoComponent...\n");
  
  const results = {
    populationTraits: testPopulationTraitsValidation(),
    validationRequest: testValidationRequestValidation(),
    validationResponse: testValidationResponseValidation(),
    invalidData: testInvalidDataValidation(),
  };
  
  console.log("\n📊 Test Results Summary:");
  console.log("Population Traits:", results.populationTraits.success ? "✅ PASS" : "❌ FAIL");
  console.log("Validation Request:", results.validationRequest.success ? "✅ PASS" : "❌ FAIL");
  console.log("Validation Response:", results.validationResponse.success ? "✅ PASS" : "❌ FAIL");
  console.log("Invalid Data Rejection:", !results.invalidData.success ? "✅ PASS" : "❌ FAIL");
  
  return results;
}

// Export test data for use in other files
export {
  testPopulationTraits,
  testValidationRequest,
  testValidationResponse,
};
