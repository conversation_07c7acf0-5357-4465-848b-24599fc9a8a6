import { useState, useCallback } from "react";
import { DisplayAttribute, BrandAttributeCombination } from "@/lib/schemas";

interface UseAttributesAPIProps {
  setDisplayAttributes: (attributes: DisplayAttribute[]) => void;
  setRealWorldBrandAttributeCombinations: (
    combinations: BrandAttributeCombination[]
  ) => void;
  setIsApiInProgress: (inProgress: boolean) => void;
  setIsApiFailed: (failed: boolean) => void;
  updateLastParameters: (question: string, country: string) => void;
}

interface UseAttributesAPIReturn {
  isLoading: boolean;
  errorMessage: string | null;
  apiFailure: boolean;
  fetchAttributes: (
    question: string,
    country: string,
    year: string
  ) => Promise<void>;
  clearError: () => void;
}

export const useAttributesAPI = ({
  setDisplayAttributes,
  setRealWorldBrandAttributeCombinations,
  setIsApiInProgress,
  setIsApiFailed,
  updateLastParameters,
}: UseAttributesAPIProps): UseAttributesAPIReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [apiFailure, setApiFailure] = useState(false);

  // Track ongoing requests to prevent duplicates
  const [ongoingRequest, setOngoingRequest] = useState<string | null>(null);

  const transformAttributes = useCallback((attrs: any[]) => {
    const transformedLevels = attrs.map((attr, attrIndex) => {
      return attr.levels.map((level: string) => {
        return {
          level: level,
          active: attrIndex < 8 ? true : false,
        };
      });
    });

    return attrs.map((attr, idx) => {
      return {
        attribute: attr.attribute,
        active: idx < 8 ? true : false,
        levels: transformedLevels[idx],
        attribute_type: attr.attribute_type,
      };
    });
  }, []);

  const fetchAttributes = useCallback(
    async (question: string, country: string, year: string) => {
      // Create a unique key for this request
      const requestKey = `${question}-${country}-${year}`;

      // Prevent duplicate requests
      if (ongoingRequest === requestKey) {
        return;
      }

      setOngoingRequest(requestKey);
      setIsLoading(true);
      setErrorMessage(null);
      setApiFailure(false);
      setIsApiInProgress(true);
      setIsApiFailed(false);

      try {
        setDisplayAttributes([]);

        const attributesLevelsBody = {
          why_prompt: question,
          country: country,
          level_count: 5,
          attribute_count: 7,
          year: year,
        };

        const attributesLevelsResponse = await fetch("/api/attributes-levels", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(attributesLevelsBody),
        });

        if (!attributesLevelsResponse.ok) {
          setDisplayAttributes([]);
          throw new Error(
            `Error fetching attributes levels: ${attributesLevelsResponse.status}`
          );
        }

        const data = await attributesLevelsResponse.json();

        if (!data || !data.attributes_levels) {
          throw new Error(
            "Invalid response data from attributes-levels endpoint"
          );
        }

        const { attributes_levels, brand_attribute_combinations } = data;

        setRealWorldBrandAttributeCombinations(
          brand_attribute_combinations || []
        );

        setDisplayAttributes(transformAttributes(attributes_levels));
        updateLastParameters(question, country);
      } catch (error: any) {
        setIsApiFailed(true);
        console.error("Error fetching data:", error);
        setErrorMessage("Failed to fetch attributes. Please try again.");
        setApiFailure(true);
      } finally {
        setOngoingRequest(null);
        setIsApiInProgress(false);
        setIsLoading(false);
      }
    },
    [
      ongoingRequest,
      setDisplayAttributes,
      setRealWorldBrandAttributeCombinations,
      setIsApiInProgress,
      setIsApiFailed,
      updateLastParameters,
      transformAttributes,
    ]
  );

  const clearError = useCallback(() => {
    setErrorMessage(null);
    setApiFailure(false);
  }, []);

  return {
    isLoading,
    errorMessage,
    apiFailure,
    fetchAttributes,
    clearError,
  };
};
