import React, { useCallback, useEffect } from "react";
import { DisplayTrait, TraitCategory } from "@/lib/schemas";
import {
  US_SPECIALIST_TRAITS,
  NON_US_SPECIALIST_TRAITS,
} from "../../constants";

/**
 * Hook for handling specialist selection logic
 *
 * @param isUSA - Whether the selected country is USA
 * @param displayTraits - The traits to display
 * @param activeSpecialist - The currently active specialist
 * @param setActiveSpecialist - Function to set the active specialist
 * @param setDisplayTraits - Function to set the display traits
 * @param specialistTraits - Map of specialist trait selections by key
 * @param setSpecialistTraits - Function to update specialist trait selections map
 * @returns Specialist selection state and handlers
 */
export function useSpecialistSelection(
  isUSA: boolean,
  displayTraits: DisplayTrait[],
  activeSpecialist: string,
  setActiveSpecialist: (specialist: string) => void,
  setDisplayTraits: (traits: DisplayTrait[]) => void,
  specialistTraits: Record<string, DisplayTrait[]>,
  setSpecialistTraits: (
    specialistTraits: Record<string, DisplayTrait[]>
  ) => void
) {
  // Generate a key for storing specialist traits based on country and specialist name
  const getSpecialistKey = useCallback((specialist: string, isUS: boolean) => {
    return `${isUS ? "US" : "nonUS"}_${specialist.replace(/\s+/g, "")}`;
  }, []);

  // Sort traits by active status
  const sortTraits = useCallback((traitsList: DisplayTrait[]) => {
    return traitsList.sort((trait1, trait2) =>
      trait1.active === trait2.active ? 0 : trait1.active ? -1 : 1
    );
  }, []);

  // Toggle a selected trait
  const toggleSelectedTrait = useCallback(
    (title: string) => {
      const updatedTraits = displayTraits.map((trait) => {
        if (trait.title.toUpperCase() === title.toUpperCase()) {
          return {
            ...trait,
            active: !trait.active,
          };
        }
        return trait;
      });

      setDisplayTraits(updatedTraits);

      // Save the updated traits for the current specialist
      if (activeSpecialist) {
        const key = getSpecialistKey(activeSpecialist, isUSA);
        setSpecialistTraits({
          ...specialistTraits,
          [key]: updatedTraits,
        });
      }
    },
    [
      displayTraits,
      setDisplayTraits,
      activeSpecialist,
      getSpecialistKey,
      isUSA,
      setSpecialistTraits,
      specialistTraits,
    ]
  );

  // Delete a trait (for custom traits)
  const deleteTrait = useCallback(
    (title: string) => {
      // Filter out the trait with the given title
      const updatedTraits = displayTraits.filter(
        (trait) => trait.title !== title
      );

      // Update the displayTraits state
      setDisplayTraits(updatedTraits);

      // Save the updated traits for the current specialist
      if (activeSpecialist) {
        const key = getSpecialistKey(activeSpecialist, isUSA);
        setSpecialistTraits({
          ...specialistTraits,
          [key]: updatedTraits,
        });
      }
    },
    [
      displayTraits,
      setDisplayTraits,
      activeSpecialist,
      getSpecialistKey,
      isUSA,
      setSpecialistTraits,
      specialistTraits,
    ]
  );

  // Generate default traits for a specialist
  const generateDefaultTraitsForSpecialist = useCallback(
    (specialist: string, traits: DisplayTrait[]) => {
      const specialistTraitsList = isUSA
        ? US_SPECIALIST_TRAITS[
            specialist.replace(/\s+/g, "") as keyof typeof US_SPECIALIST_TRAITS
          ]
        : NON_US_SPECIALIST_TRAITS[
            specialist.replace(
              /\s+/g,
              ""
            ) as keyof typeof NON_US_SPECIALIST_TRAITS
          ];

      if (!specialistTraitsList) {
        console.error(`No traits found for specialist: ${specialist}`);
        return traits; // Return unchanged if no traits found
      }

      // Group traits by category
      const categorizedTraits = traits.reduce(
        (acc, trait) => {
          const category = trait.category || "personalityTraits";
          if (!acc[category]) {
            acc[category] = [];
          }
          acc[category].push(trait);
          return acc;
        },
        {} as Record<string, DisplayTrait[]>
      );

      // For each category, activate at most one trait that matches the specialist's traits
      const updatedTraits = traits.map((trait) => {
        const category = trait.category || "personalityTraits";

        // Skip traits that don't match the current country type
        if (
          (category === "usTraits" && !isUSA) ||
          (category === "nonUsTraits" && isUSA)
        ) {
          return {
            ...trait,
            active: false,
          };
        }

        const traitList = categorizedTraits[category] || [];

        // Find which traits in this category match the specialist's traits
        const matchingTraitsInCategory = traitList.filter((t) =>
          specialistTraitsList.some(
            (st) => st.toUpperCase() === t.title.toUpperCase()
          )
        );

        // If this is the first matching trait in its category, activate it
        const isFirstMatchInCategory =
          matchingTraitsInCategory.length > 0 &&
          matchingTraitsInCategory[0].title.toUpperCase() ===
            trait.title.toUpperCase();

        return {
          ...trait,
          active: isFirstMatchInCategory,
        };
      });

      return sortTraits(updatedTraits);
    },
    [isUSA, sortTraits]
  );

  // Select a specialist and update traits
  const selectSpecialist = useCallback(
    (title: string) => {
      // If we're deselecting the current specialist, don't do anything
      if (activeSpecialist === title) {
        return;
      }

      // Save current trait selection for the current specialist before switching
      if (activeSpecialist) {
        const currentKey = getSpecialistKey(activeSpecialist, isUSA);
        setSpecialistTraits({
          ...specialistTraits,
          [currentKey]: displayTraits,
        });
      }

      // Prepare to switch to the new specialist
      const newSpecialistKey = getSpecialistKey(title, isUSA);
      let newTraits: DisplayTrait[];

      // Check if we already have saved traits for this specialist
      if (specialistTraits[newSpecialistKey]) {
        // Use saved traits
        newTraits = specialistTraits[newSpecialistKey];
      } else {
        // Generate default traits for this specialist
        newTraits = generateDefaultTraitsForSpecialist(title, displayTraits);

        // Save the generated traits
        setSpecialistTraits({
          ...specialistTraits,
          [newSpecialistKey]: newTraits,
        });
      }

      // Update the display traits and active specialist
      setDisplayTraits(newTraits);
      setActiveSpecialist(title);
    },
    [
      activeSpecialist,
      displayTraits,
      generateDefaultTraitsForSpecialist,
      getSpecialistKey,
      isUSA,
      setActiveSpecialist,
      setDisplayTraits,
      setSpecialistTraits,
      specialistTraits,
    ]
  );

  // Get the number of selected traits
  const getNumSelected = useCallback(() => {
    return displayTraits.filter((trait) => trait.active).length;
  }, [displayTraits]);

  // Check if traits have been initialized with a specialist
  const areTraitsInitialized = useCallback(() => {
    // If we have an active specialist, traits have been initialized
    return activeSpecialist !== "";
  }, [activeSpecialist]);

  // Add a new trait
  const addNewTrait = useCallback(
    (title: string, values: string[]) => {
      const newTrait: DisplayTrait = {
        title: title,
        values: values,
        active: true,
        category: "customTraits" as TraitCategory,
      };

      const updatedTraits: DisplayTrait[] = [newTrait, ...displayTraits];

      setDisplayTraits(updatedTraits);

      // Save the updated traits for the current specialist
      if (activeSpecialist) {
        const key = getSpecialistKey(activeSpecialist, isUSA);
        setSpecialistTraits({
          ...specialistTraits,
          [key]: updatedTraits,
        });
      }
    },
    [
      displayTraits,
      setDisplayTraits,
      activeSpecialist,
      getSpecialistKey,
      isUSA,
      setSpecialistTraits,
      specialistTraits,
    ]
  );

  // Activate an existing trait
  const activateNewTrait = useCallback(
    (title: string) => {
      const updatedTraits = displayTraits.map((trait) => {
        if (trait.title === title) {
          return {
            ...trait,
            active: true,
            category: trait.category || ("customTraits" as TraitCategory),
          };
        }
        return trait;
      });

      setDisplayTraits(updatedTraits);

      // Save the updated traits for the current specialist
      if (activeSpecialist) {
        const key = getSpecialistKey(activeSpecialist, isUSA);
        setSpecialistTraits({
          ...specialistTraits,
          [key]: updatedTraits,
        });
      }
    },
    [
      displayTraits,
      setDisplayTraits,
      activeSpecialist,
      getSpecialistKey,
      isUSA,
      setSpecialistTraits,
      specialistTraits,
    ]
  );

  // Group traits by category
  const groupTraitsByCategory = useCallback(
    (traits: DisplayTrait[]) => {
      const grouped = {
        usTraits: [] as DisplayTrait[],
        nonUsTraits: [] as DisplayTrait[],
        lifestyleTraits: [] as DisplayTrait[],
        psychologicalTraits: [] as DisplayTrait[],
        personalityTraits: [] as DisplayTrait[],
        customTraits: [] as DisplayTrait[],
      };

      traits.forEach((trait) => {
        if (trait.category === "customTraits") {
          grouped.customTraits.push(trait);
        } else if (trait.category) {
          // Only include US traits if the country is USA
          if (trait.category === "usTraits" && !isUSA) {
            return;
          }
          // Only include non-US traits if the country is not USA
          if (trait.category === "nonUsTraits" && isUSA) {
            return;
          }
          grouped[trait.category].push(trait);
        } else {
          grouped.personalityTraits.push(trait);
        }
      });

      return grouped;
    },
    [isUSA]
  );

  useEffect(() => {
    if (displayTraits.length > 0 && activeSpecialist === "") {
      // Always default to Economist if no specialist is selected
      selectSpecialist("Economist");
    }
  }, [displayTraits.length, activeSpecialist, selectSpecialist]);

  // Create a ref to track previous country type
  const prevCountryTypeRef = React.useRef<boolean | null>(null);

  useEffect(() => {
    // Only run this effect when isUSA changes, not on every render
    if (displayTraits.length > 0 && activeSpecialist) {
      if (prevCountryTypeRef.current !== isUSA) {
        // Update the ref to the current country type
        prevCountryTypeRef.current = isUSA;

        // Reselect the current specialist to update traits based on new country
        // This will use the saved traits for the specialist in the new country if available
        // or generate default traits if not
        selectSpecialist(activeSpecialist);
      }
    }
  }, [isUSA, displayTraits.length, activeSpecialist, selectSpecialist]);

  return {
    toggleSelectedTrait,
    selectSpecialist,
    getNumSelected,
    addNewTrait,
    activateNewTrait,
    groupTraitsByCategory,
    sortTraits,
    areTraitsInitialized,
    deleteTrait,
  };
}
