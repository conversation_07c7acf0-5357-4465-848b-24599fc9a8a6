import { useEffect, useState, useCallback, useRef } from "react";
import { StateData } from "../../objects";
import { States } from "../../_whenwhere/PopulationTraitsData";

/**
 * Hook for handling country-specific logic in the WhoComponent
 *
 * @param country - The selected country name
 * @param selectedState - The selected state (for USA only)
 * @returns Country-specific state and logic
 */
export function useCountrySpecificLogic(
  country: string | undefined,
  selectedState: string | null
) {
  // Determine if the country is USA
  const [isUSA, setIsUSA] = useState<boolean>(
    country === "United States of America (USA)"
  );

  // Define initializeStateData before using it in useState
  const initializeStateData = useCallback(
    (state: string, isUSA: boolean): StateData => {
      if (!isUSA) {
        return {
          Age: {
            min: 0,
            max: 0,
            "90th_percentile": 0,
            "95th_percentile": 0,
          },
          "Household income": {
            min: 0,
            max: 0,
            "90th_percentile": 0,
            "95th_percentile": 0,
          },
        };
      }

      // If no state is selected or state is 'USA', use the USA aggregate data
      if (!state || state === "USA") {
        return {
          Age: States.USA.Age,
          "Household income": States.USA["Household income"],
        };
      }

      // Get base state data from the States object
      const baseStateData = States[state as keyof typeof States];

      // For non-USA or when there's no state data, return default values
      if (!baseStateData) {
        return {
          Age: {
            min: 18,
            max: 65,
            "90th_percentile": 60,
            "95th_percentile": 65,
          },
          "Household income": {
            min: 0,
            max: 100000,
            "90th_percentile": 90000,
            "95th_percentile": 100000,
          },
        };
      }

      // Return the base state data
      return {
        Age: baseStateData.Age,
        "Household income": baseStateData["Household income"],
      };
    },
    []
  );

  // Initialize state data based on country and selected state
  const [stateData, setStateData] = useState<StateData>(() =>
    initializeStateData(selectedState || "", isUSA)
  );

  // Update isUSA when country changes
  useEffect(() => {
    setIsUSA(country === "United States of America (USA)");
  }, [country]);

  // Store the previous state in a ref to persist between renders
  const previousStateRef = useRef<string | null>(null);

  // Handle state change
  const handleStateChange = useCallback((newState: string | null) => {
    if (previousStateRef.current !== newState) {
      // If state is 'USA' or null, use the USA aggregate data
      const baseStateData =
        newState === "USA" || !newState
          ? States.USA
          : States[newState as keyof typeof States];

      setStateData({
        Age: baseStateData.Age,
        "Household income": baseStateData["Household income"],
      });

      previousStateRef.current = newState;
    }
  }, []);

  // Update state data when selected state changes
  useEffect(() => {
    handleStateChange(selectedState);
  }, [selectedState, handleStateChange]);

  return {
    isUSA,
    stateData,
    setStateData,
    handleStateChange,
  };
}
