import { useCallback, useEffect } from "react";
import { PopulationTraits } from "../../objects";
import { useDemographicTraits as useDemographicTraitsContext } from "../contexts/DemographicTraitsContext";

/**
 * Hook for managing demographic trait selection logic
 *
 * @param isUSA - Whether the selected country is USA
 * @param selectedState - The selected state (for USA only)
 * @returns Trait state and handlers
 */
export function useDemographicTraits(
  isUSA: boolean,
  selectedState: string | null
) {
  // Get values and actions from context
  const {
    ageRange,
    incomeRange,
    educationValues,
    genderValues,
    childrenValues,
    raceValues,
    setAgeRange,
    setIncomeRange,
    updateEducationValues,
    updateGenderValues,
    updateChildrenValues,
    updateRaceValues,
    getCurrentTraits,
    resetTraits,
    haveTraitsChanged,
    markTraitsAsValidated,
  } = useDemographicTraitsContext();

  // Reset traits is handled automatically in the context when selectedState changes

  // Handle age range changes
  const handleGetAgeRange = useCallback(
    (ageRange: number[]) => {
      // Ensure we have exactly two elements for the tuple
      const typedRange: [number, number] =
        ageRange.length === 2 ? [ageRange[0], ageRange[1]] : [18, 65]; // Default values if invalid

      setAgeRange(typedRange);
    },
    [setAgeRange]
  );

  // Handle income range changes
  const handleGetIncomeRange = useCallback(
    (incomeRange: number[]) => {
      // Ensure we have exactly two elements for the tuple
      const typedRange: [number, number] =
        incomeRange.length === 2
          ? [incomeRange[0], incomeRange[1]]
          : [0, 100000]; // Default values if invalid

      setIncomeRange(typedRange);
    },
    [setIncomeRange]
  );

  // Handle education value changes
  const getEduValue = useCallback(
    (value: string, isActive: boolean) => {
      updateEducationValues(value, isActive);
    },
    [updateEducationValues]
  );

  // Handle gender value changes
  const getGenderValue = useCallback(
    (value: string, isActive: boolean) => {
      updateGenderValues(value, isActive);
    },
    [updateGenderValues]
  );

  // Handle children value changes
  const getChildrenValue = useCallback(
    (value: string, isActive: boolean) => {
      updateChildrenValues(value, isActive);
    },
    [updateChildrenValues]
  );

  // Handle race value changes
  const getRaceValue = useCallback(
    (value: string, isActive: boolean) => {
      updateRaceValues(value, isActive);
    },
    [updateRaceValues]
  );

  return {
    // Return the current range values from context
    ageRange,
    incomeRange,
    // Return handlers
    handleGetAgeRange,
    handleGetIncomeRange,
    getEduValue,
    getGenderValue,
    getChildrenValue,
    getRaceValue,
    getCurrentTraits,
    haveTraitsChanged,
    markTraitsAsValidated,
    // Return setters for direct access
    setAgeRange,
    setIncomeRange,
  };
}
