/**
 * Configuration for trait behavior in the demographic traits form
 */

export interface TraitConfig {
  // Whether at least one option must be selected
  requireSelection: boolean;
  // Default values to use when no selection is made
  defaultValues: string[];
}

/**
 * Configuration for all trait types
 * This centralizes the behavior rules for each trait type
 */
export const traitConfigs: Record<string, TraitConfig> = {
  // All traits should have the same behavior - either all require selection or none do
  education: {
    requireSelection: false, // Allow unchecking all checkboxes
    defaultValues: [
      "Less than high school",
      "High School but no diploma",
      "High School Diploma",
      "Some College",
      "Associates",
      "Bachelors",
      "Masters",
      "PhD",
    ],
  },
  gender: {
    requireSelection: false, // Allow unchecking all checkboxes
    defaultValues: ["Male", "Female"],
  },
  children: {
    requireSelection: false, // Allow unchecking all checkboxes
    defaultValues: ["0", "1", "2", "3", "4+"],
  },
  race: {
    requireSelection: false, // Allow unchecking all checkboxes
    defaultValues: [
      "Mixed race",
      "White",
      "African American",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  // Add any additional trait types here with the same configuration
  racial_group: {
    requireSelection: false, // Allow unchecking all checkboxes
    defaultValues: [
      "Mixed race",
      "White",
      "African American",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  number_of_children: {
    requireSelection: false, // Allow unchecking all checkboxes
    defaultValues: ["0", "1", "2", "3", "4+"],
  },
};

/**
 * Get configuration for a trait type
 * @param traitType The trait type to get configuration for
 * @returns The trait configuration
 */
export function getTraitConfig(traitType: string): TraitConfig {
  // Normalize the trait type to handle different formats
  const normalizedType = traitType.toLowerCase().replace(/\s+/g, "_");

  // Return the configuration for the trait type or a default configuration
  return (
    traitConfigs[normalizedType] || {
      requireSelection: false, // Allow unchecking all checkboxes
      defaultValues: [],
    }
  );
}
