// Utility for error message handling in the 'who' flow

export interface GetErrorMessageParams {
  validationLoading: boolean;
  validationResults: any;
  currentStep: "first" | "second" | "third";
  validationError: string | null;
  errorMessage: string | null;
  isLoading: boolean;
}

export function getErrorMessage({
  validationLoading,
  validationResults,
  currentStep,
  validationError,
  errorMessage,
  isLoading,
}: GetErrorMessageParams) {
  // If validation is in progress or we have validation results, don't show errors
  if (validationLoading || validationResults) {
    return null;
  }

  // For the first step, only show validation errors if they exist and we're not loading
  if (
    currentStep === "first" &&
    validationError === "Failed to validate population traits" &&
    !validationLoading
  ) {
    return {
      message: validationError,
      showTryAgain: true,
    };
  }

  // For the second step, only show specific errors if we're not loading
  if (
    currentStep === "second" &&
    validationError === "Select a non-zero population configuration" &&
    !validationLoading
  ) {
    return {
      message: validationError,
      showTryAgain: false,
    };
  }

  // For the third step, show general error messages if we're not loading
  if (errorMessage && currentStep === "third" && !isLoading) {
    return {
      message: errorMessage,
      showTryAgain: false,
    };
  }

  return null;
}
