"use client";

import { CornerRightUp } from "lucide-react";
import React, { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useAutoResizeTextarea } from "@/hooks/use-auto-resize-textarea";

interface TraitInputProps {
  id?: string;
  placeholder?: string;
  minHeight?: number;
  maxHeight?: number;
  onSubmit?: (value: string) => void | Promise<void>;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
}

/**
 * A custom input component for adding traits, without the loading animation
 */
export function TraitInput({
  id = "trait-input",
  placeholder = "Add a trait...",
  minHeight = 56,
  maxHeight = 200,
  onSubmit,
  className,
  disabled = false,
  loading = false,
}: TraitInputProps) {
  const [inputValue, setInputValue] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const { textareaRef, adjustHeight } = useAutoResizeTextarea({
    minHeight,
    maxHeight,
  });

  const handleSubmit = async () => {
    if (!inputValue.trim() || disabled || loading) return;

    await onSubmit?.(inputValue);
    setInputValue("");
    adjustHeight(true);
  };

  return (
    <div className={cn("w-full py-4", className)}>
      <div className="relative w-full flex items-start flex-col gap-2">
        <div className="relative w-full">
          <Textarea
            id={id}
            placeholder={placeholder}
            className={cn(
              "bg-black/5 dark:bg-white/5 w-full rounded-3xl pl-6 pr-10 py-4",
              "placeholder:text-gray-500 dark:placeholder:text-gray-400 placeholder:font-medium",
              "border-none transition-all duration-200",
              isFocused
                ? "ring-2 ring-primary/60 shadow-sm"
                : "ring-black/30 dark:ring-white/30 hover:ring-black/50 dark:hover:ring-white/50",
              "text-black dark:text-white resize-none text-wrap leading-[1.2] font-medium",
              "overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]",
              `min-h-[${minHeight}px]`
            )}
            ref={textareaRef}
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
              adjustHeight();
            }}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSubmit();
              }
            }}
            disabled={disabled}
          />
          {!loading && (
            <button
              onClick={handleSubmit}
              className={cn(
                "absolute right-3 top-1/2 -translate-y-1/2 rounded-xl py-1 px-1",
                disabled ? "bg-none" : "bg-black/5 dark:bg-white/5"
              )}
              type="button"
              disabled={disabled}
            >
              <CornerRightUp
                className={cn(
                  "w-4 h-4 transition-opacity dark:text-white",
                  inputValue ? "opacity-100" : "opacity-30"
                )}
              />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
