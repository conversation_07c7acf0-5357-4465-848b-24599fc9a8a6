import React, { useState, useContext, useEffect } from "react";
import { ChevronDown, ChevronUp, TriangleAlert } from "lucide-react";
import { DisplayTrait } from "../../../objects";
import SpecialistGrid from "./SpecialistGrid";
import TraitCard from "./TraitCard";
import AddNewTraitCard from "./AddNewTraitCard";
import ExperimentCreationContext from "../../../ExperimentCreationContext";
import ErrorDisplay from "../../../../../_components/_ideation/ErrorDisplay";

interface SpecialistSelectionProps {
  onComplete: () => void;
  onBack: () => void;
  isUSA: boolean;
  question: string;
  displayTraits: DisplayTrait[];
  activeSpecialist: string;
  selectSpecialist: (title: string) => void;
  toggleSelectedTrait: (title: string) => void;
  addNewTrait: (title: string, values: string[]) => void;
  activateNewTrait: (title: string) => void;
  groupTraitsByCategory: (
    traits: DisplayTrait[]
  ) => Record<string, DisplayTrait[]>;
  getNumSelected: () => number;
  areTraitsInitialized: () => boolean;
  isLoading: boolean;
  errorMessage: string | null;
  loadingMessage: string;
  deleteTrait: (title: string) => void;
}

/**
 * Component for selecting specialists and traits
 */
const SpecialistSelection: React.FC<SpecialistSelectionProps> = ({
  onComplete,
  onBack,
  isUSA,
  question,
  displayTraits,
  activeSpecialist,
  selectSpecialist,
  toggleSelectedTrait,
  addNewTrait,
  activateNewTrait,
  groupTraitsByCategory,
  getNumSelected,
  areTraitsInitialized,
  isLoading,
  errorMessage,
  loadingMessage,
  deleteTrait,
}) => {
  const { where } = useContext(ExperimentCreationContext);

  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({
    customTraits: false,
    usTraits: false,
    nonUsTraits: false,
    lifestyleTraits: false,
    psychologicalTraits: false,
    personalityTraits: false,
  });

  const [localErrorMessage, setLocalErrorMessage] = useState<string>("");
  const [traitIsLoading, setTraitIsLoading] = useState<boolean>(false);
  const [traitLoadingMessage, setTraitLoadingMessage] = useState<string>("");
  const [showError, setShowError] = useState<boolean>(false);

  // Use effect to handle error message display with debouncing
  useEffect(() => {
    if (localErrorMessage || errorMessage) {
      setShowError(true);
    } else {
      // Small delay before hiding error message to prevent flashing
      const timer = setTimeout(() => {
        setShowError(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [localErrorMessage, errorMessage]);

  const toggleSection = (category: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const categoryDisplayNames = {
    usTraits: "Demographic Traits",
    nonUsTraits: "Demographic Traits",
    lifestyleTraits: "Lifestyle Characteristics",
    psychologicalTraits: "Psychological Traits",
    personalityTraits: "Personality Traits",
    customTraits: "Custom Traits",
  };

  const categoryOrder = [
    "customTraits",
    isUSA ? "usTraits" : "nonUsTraits",
    "lifestyleTraits",
    "psychologicalTraits",
    "personalityTraits",
  ] as const;

  const groupedTraits = groupTraitsByCategory(displayTraits);

  // Helper function to handle error message setting with debounce effect
  const handleSetErrorMessage = (message: string) => {
    setLocalErrorMessage(message);
    if (message) {
      setShowError(true);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col h-full">
        <div className="flex flex-col gap-6 items-start max-w-4xl overflow-y-auto">
          <div className="flex flex-col gap-2 items-stretch w-full">
            <div className="border-[#D0D5DD] border-2 rounded-lg py-3 text-center">
              <h3 className="font-semibold text-primary">Research Question</h3>
              <h3 className="text-text-placeholder font-roboto text-lg font-normal">
                {question}
              </h3>
            </div>
            <h2 className="text-text-dark font-roboto font-semibold text-2xl">
              Select characteristics of the people you want to synthesize
            </h2>
            <div>
              <h3 className="text-text-placeholder font-roboto text-lg font-normal">
                Or choose a specialist to select them for you
              </h3>
              <div id="ideation-who-guide-7" className="mt-4">
                <SpecialistGrid
                  activeSpecialist={activeSpecialist}
                  onSelectSpecialist={selectSpecialist}
                />
              </div>
            </div>
          </div>

          <div className="w-full">
            <AddNewTraitCard
              addnewTrait={addNewTrait}
              setLoadingMessage={setTraitLoadingMessage}
              setErrorMessage={handleSetErrorMessage}
              setIsLoading={setTraitIsLoading}
              isLoading={isLoading}
              activateNewTrait={activateNewTrait}
              expandedSections={expandedSections}
              setExpandedSections={setExpandedSections}
              country={where?.name}
            />
          </div>

          {/* Display global loading message */}
          {isLoading && (
            <div className="flex flex-row gap-1 items-center">
              <span className="sr-only">Loading...</span>
              <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
              <div className="h-4 w-4 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
              <div className="h-4 w-4 bg-primary rounded-full animate-bounce"></div>
              <div className="text-sm text-primary p-2 rounded">
                {loadingMessage ||
                  "We are creating the necessary attributes and levels... Please hold on for a moment."}
              </div>
            </div>
          )}

          {/* Display errors */}
          {showError && !isLoading && !traitIsLoading && (
            <ErrorDisplay
              message={localErrorMessage || errorMessage || ""}
              className="w-full transition-opacity duration-300 ease-in-out"
            />
          )}

          {getNumSelected() === 0 && areTraitsInitialized() && (
            <ErrorDisplay
              message="No traits selected. Please select at least one trait to continue."
              className="w-full mb-4"
            />
          )}

          {categoryOrder.map((category) => {
            const traits = groupedTraits[category];
            const selectedCount = traits
              ? traits.filter((trait) => trait.active).length
              : 0;

            return traits && traits.length > 0 ? (
              <div
                key={category}
                className="w-full bg-white rounded-lg border border-[#E4E7EC] overflow-hidden"
              >
                <div
                  onClick={() => toggleSection(category)}
                  className="flex items-center justify-between cursor-pointer p-4 hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-semibold text-text-dark">
                      {
                        categoryDisplayNames[
                          category as keyof typeof categoryDisplayNames
                        ]
                      }
                    </h3>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {selectedCount} / {traits.length} Selected
                    </span>
                  </div>

                  {expandedSections[category] ? (
                    <ChevronUp className="w-5 h-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500" />
                  )}
                </div>

                <div
                  className={`transition-all duration-300 ease-in-out ${
                    expandedSections[category]
                      ? "max-h-[2000px] opacity-100 p-4"
                      : "max-h-0 opacity-0 overflow-hidden"
                  }`}
                >
                  <div className="grid grid-cols-2 gap-3 items-stretch w-full">
                    {traits.map((trait) => (
                      <TraitCard
                        key={trait.title}
                        title={trait.title}
                        values={trait.values}
                        active={trait.active}
                        setSelected={() => toggleSelectedTrait(trait.title)}
                        onDelete={
                          category === "customTraits"
                            ? () => deleteTrait(trait.title)
                            : undefined
                        }
                        isCustom={category === "customTraits"}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ) : null;
          })}
        </div>
      </div>
    </div>
  );
};

export default SpecialistSelection;
