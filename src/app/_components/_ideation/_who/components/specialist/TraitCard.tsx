import React from "react";
import { useEffect, useState } from "react";
import Checkbox from "../../../../_util/Checkbox";
import { X } from "lucide-react"; // Import X icon from lucide-react

interface TraitCardProps {
  title: string;
  active: boolean;
  values: string[];
  // eslint-disable-next-line no-unused-vars
  setSelected: (selected: boolean) => void;
  onDelete?: () => void; // Optional delete handler for custom traits
  isCustom?: boolean; // Flag to identify if this is a custom trait
}

function formatValues(values: string[], withSpacing = false) {
  const hasColonFormat = values.some((val) => val.includes(":"));

  if (hasColonFormat) {
    return withSpacing ? values.join("\n") : values.join("\n");
  } else {
    return values.map(
      (val, idx) => val + (idx !== values.length - 1 ? ", " : "")
    );
  }
}

const TraitCard = ({
  title,
  active,
  values,
  setSelected,
  onDelete,
  isCustom,
}: TraitCardProps) => {
  // Remove local state and directly use the active prop
  // This prevents the infinite loop between local state and props

  // Handle checkbox click
  const handleCheckboxToggle = () => {
    setSelected(!active);
  };

  // Handle delete button click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card selection when clicking delete
    if (onDelete) {
      onDelete();
    }
  };

  // Check if we need to use the special formatting
  const hasColonFormat = values.some((val) => val.includes(":"));
  const formattedValues = formatValues(values, hasColonFormat);

  return (
    <div
      className={`flex flex-row gap-3 py-4 px-3 rounded-lg bg-white border hover:cursor-pointer relative ${active ? "border-primary" : "border-card-border"}`}
      onMouseDown={handleCheckboxToggle}
    >
      {/* Delete button - only show for custom traits */}
      {isCustom && onDelete && (
        <div
          className="absolute top-2 right-2 p-1 cursor-pointer z-10 text-gray-500 hover:text-gray-700"
          onClick={handleDeleteClick}
          title="Delete custom trait"
        >
          <X size={16} />
        </div>
      )}

      {/* CHECKBOX */}
      <div>
        <Checkbox selected={active} toggleSelected={handleCheckboxToggle} />
      </div>
      {/* TITLE */}
      <div className="flex flex-col justify-start items-start gap-1">
        <p className="text-text-dark text-base font-roboto font-normal">
          {title}
        </p>
        {hasColonFormat ? (
          <div className="text-text-placeholder text-xs font-normal font-roboto whitespace-pre-line leading-relaxed">
            {formattedValues}
          </div>
        ) : (
          <p className="text-text-placeholder text-xs font-normal font-roboto">
            {formattedValues}
          </p>
        )}
      </div>
    </div>
  );
};

export default TraitCard;
