import React, { useContext, useEffect, useRef, useState } from "react";
import * as Sentry from "@sentry/react";
import { AddNewTraitLabelsObject } from "../../../objects";
import ExperimentCreationContext from "../../../ExperimentCreationContext";
import { Loader2 } from "lucide-react";
import { TraitInput } from "../../../_who/components/TraitInput";

interface AddNewTraitCardProps {
  addnewTrait: (title: string, values: string[]) => void;
  setErrorMessage: (message: string) => void;
  setIsLoading: (loading: boolean) => void;
  country?: string;
  isLoading: boolean | undefined;
  activateNewTrait: (trait: string) => void;
  setLoadingMessage: (message: string) => void;
  expandedSections?: Record<string, boolean>;
  setExpandedSections?: (sections: Record<string, boolean>) => void;
}

const fetchNewTraitLabels = async (
  url: string,
  gpt4_body: AddNewTraitLabelsObject
) => {
  const startTime = Date.now();
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(gpt4_body),
    });

    const duration = Date.now() - startTime;
    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return data;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Log error with contextual information
    Sentry.captureException(error, {
      tags: {
        api_endpoint: url,
        status: "error",
      },
      extra: {
        response_time: `${duration}ms`,
        response_status: (error as any).response
          ? (error as any).response.status
          : "unknown",
        error_message: (error as any).message,
      },
    });
    throw error;
  }
};

// React.FC<AddNewTraitCardProps>
const AddNewTraitCard: React.FC<AddNewTraitCardProps> = ({
  setLoadingMessage,
  addnewTrait,
  country,
  setErrorMessage,
  setIsLoading,
  activateNewTrait,
  isLoading,
  expandedSections,
  setExpandedSections,
}) => {
  const { question, displayTraits } = useContext(ExperimentCreationContext);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [traitLoading, setTraitLoading] = useState(false);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleSubmit = async (value: string) => {
    if (value.trim() === "") return;

    if (value.length < 100) {
      await renderNewTrait(value);
      // Open the custom traits drawer after submission
      if (setExpandedSections && expandedSections) {
        setExpandedSections({
          ...expandedSections,
          customTraits: true,
        });
      }
    } else if (value.length > 100) {
      setErrorMessage("Trait name cannot be longer than 100 characters");
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        setErrorMessage("");
      }, 3000);
    }
  };

  const renderNewTrait = async (value: string) => {
    // Clear any previous error messages before starting
    setErrorMessage("");

    let capitalizedTrait = value.slice(0, 1).toUpperCase() + value.slice(1);
    const traitExist = displayTraits.find(
      (trait) => trait.title.toLowerCase() === capitalizedTrait.toLowerCase()
    );

    if (traitExist) {
      setErrorMessage("Trait already exists");
      activateNewTrait(traitExist.title);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        setErrorMessage("");
      }, 3000);
      return;
    }

    try {
      setTraitLoading(true);
      setLoadingMessage("Fetching new trait...");

      const body = {
        levels_count: 3,
        why_prompt: question,
        new_trait: value,
        country: country,
        existing_traits: displayTraits.map(({ title }) => title),
      };

      const response = await fetchNewTraitLabels("/api/traits/newTrait", body);

      let key =
        response?.trait.slice(0, 1).toUpperCase() + response.trait.slice(1);
      addnewTrait(key, response?.levels);
    } catch (error) {
      Sentry.captureException(error);
      setErrorMessage("Failed to add trait. Please try again.");
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        setErrorMessage("");
      }, 3000);
    } finally {
      setTraitLoading(false);
      setLoadingMessage("");
    }
  };

  return (
    <div className="self-stretch w-full px-1 relative">
      <TraitInput
        placeholder="To add a new trait, type here..."
        onSubmit={handleSubmit}
        className="p-0 [&>div]:max-w-none [&>div>div]:max-w-none [&_p]:!text-[#4B5563]"
        minHeight={48}
        disabled={false}
        loading={traitLoading}
      />
      {traitLoading && (
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
          <Loader2 className="h-5 w-5 text-primary animate-spin" />
        </div>
      )}
    </div>
  );
};

export default AddNewTraitCard;
