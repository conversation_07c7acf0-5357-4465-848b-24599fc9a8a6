import React from "react";
import Specialist<PERSON><PERSON> from "./SpecialistCard";
import { SPECIALISTS } from "../../../constants";

interface SpecialistGridProps {
  activeSpecialist: string;
  onSelectSpecialist: (title: string) => void;
}

/**
 * A grid component for displaying and selecting specialists
 */
const SpecialistGrid: React.FC<SpecialistGridProps> = ({
  activeSpecialist,
  onSelectSpecialist,
}) => {
  return (
    <div className="flex flex-row gap-2 w-full justify-between">
      {SPECIALISTS.map((specialist, idx) => (
        <SpecialistCard
          key={idx}
          title={specialist.title}
          icon={specialist.icon}
          active={activeSpecialist === specialist.title}
          onMouseDown={() => onSelectSpecialist(specialist.title)}
        />
      ))}
    </div>
  );
};

export default SpecialistGrid;
