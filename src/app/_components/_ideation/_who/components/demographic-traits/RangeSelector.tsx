import * as Slider from "@radix-ui/react-slider";
import { Input } from "@/components/ui/input";
import React, { useState, useEffect } from "react";

interface RangeSelectorProps {
  title: string;
  range: [number, number];
  min: number;
  max: number;
  step?: number;
  onChange: (range: [number, number]) => void;
  isCurrency?: boolean;
}

const RangeSelector: React.FC<RangeSelectorProps> = ({
  title,
  range,
  min,
  max,
  step = 1,
  onChange,
  isCurrency = false,
}) => {
  // Input field states (as strings for better input control)
  const [inputMin, setInputMin] = useState<string>(range[0].toString());
  const [inputMax, setInputMax] = useState<string>(range[1].toString());
  const [sliderValue, setSliderValue] = useState<[number, number]>(range);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Update internal state when range prop changes
    // Ensure range values are within min/max bounds
    const validMin = Math.max(min, Math.min(range[0], max));
    const validMax = Math.min(max, Math.max(range[1], min));

    // Always update internal state when range prop changes
    // This ensures the component reflects the latest prop values
    setInputMin(validMin.toString());
    setInputMax(validMax.toString());
    setSliderValue([validMin, validMax]);

    // Clear any error when range is updated from props
    setError(null);

    // If the range was adjusted, notify parent
    // But only if the adjustment is significant (prevents infinite loops)
    if (
      Math.abs(validMin - range[0]) > 0.001 ||
      Math.abs(validMax - range[1]) > 0.001
    ) {
      // Use a timeout to break the potential circular dependency
      // This ensures the parent component has finished its current render cycle
      const timeoutId = setTimeout(() => {
        onChange([validMin, validMax]);
      }, 0);

      return () => clearTimeout(timeoutId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [range, min, max]);

  const validateRange = (minVal: number, maxVal: number): string | null => {
    if (minVal < min || maxVal > max) {
      return `Values must be between ${min} and ${max}.`;
    }
    if (minVal > maxVal) {
      return "Min value cannot be greater than max value.";
    }
    return null;
  };

  const formatForDisplay = (val: string): string => {
    if (val === "") return "";
    const numVal = parseFloat(val.replace(/[^0-9.-]+/g, ""));
    if (isNaN(numVal)) return val;

    if (isCurrency) {
      // Format with commas for thousands separators
      return numVal.toLocaleString();
    }
    return numVal.toString();
  };

  // Helper function to parse numeric values from string inputs
  const parseNumericInput = (val: string): number => {
    if (val === "") return 0;
    const parsed = parseFloat(val.replace(/[^0-9.-]+/g, ""));
    return isNaN(parsed) ? 0 : parsed;
  };

  const handleInputMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cursorPosition = e.target.selectionStart || 0;
    const rawValue = e.target.value.replace(/,/g, "");
    setInputMin(rawValue);

    if (rawValue === "") {
      setError(null);
      return;
    }

    const minVal = parseNumericInput(rawValue);
    const maxVal = parseNumericInput(inputMax);

    if (minVal === 0 && rawValue !== "0") {
      setError("Please enter a valid number.");
      return;
    }

    // Validate the new range
    const validationError = validateRange(minVal, maxVal);
    setError(validationError);

    // Always update the slider value to match the input
    // This ensures the slider moves even if there's an error
    const newSliderValue: [number, number] = [
      Math.max(min, Math.min(minVal, max)),
      sliderValue[1],
    ];
    setSliderValue(newSliderValue);

    // Only notify parent if there's no error
    if (!validationError) {
      onChange([minVal, maxVal]);
    }

    if (isCurrency) {
      requestAnimationFrame(() => {
        const input = e.target;
        if (input) {
          input.setSelectionRange(cursorPosition, cursorPosition);
        }
      });
    }
  };

  const handleInputMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cursorPosition = e.target.selectionStart || 0;
    const rawValue = e.target.value.replace(/,/g, "");
    setInputMax(rawValue);

    if (rawValue === "") {
      setError(null);
      return;
    }

    const maxVal = parseNumericInput(rawValue);
    const minVal = parseNumericInput(inputMin);

    if (maxVal === 0 && rawValue !== "0") {
      setError("Please enter a valid number.");
      return;
    }

    // Validate the new range
    const validationError = validateRange(minVal, maxVal);
    setError(validationError);

    // Always update the slider value to match the input
    // This ensures the slider moves even if there's an error
    const newSliderValue: [number, number] = [
      sliderValue[0],
      Math.min(max, Math.max(maxVal, min)),
    ];
    setSliderValue(newSliderValue);

    // Only notify parent if there's no error
    if (!validationError) {
      onChange([minVal, maxVal]);
    }

    if (isCurrency) {
      requestAnimationFrame(() => {
        const input = e.target;
        if (input) {
          input.setSelectionRange(cursorPosition, cursorPosition);
        }
      });
    }
  };

  const handleSliderChange = (val: number[]) => {
    // Only update if values have actually changed
    if (val[0] !== sliderValue[0] || val[1] !== sliderValue[1]) {
      const validationError = validateRange(val[0], val[1]);
      setError(validationError);

      // Always update local state regardless of validation
      // This ensures the inputs reflect the slider position
      setSliderValue([val[0], val[1]]);
      setInputMin(val[0].toString());
      setInputMax(val[1].toString());

      // Notify parent of change if there's no error
      if (!validationError) {
        onChange([val[0], val[1]]);
      }
    }
  };

  return (
    <div>
      <div className="text-md font-semibold">{title}</div>
      <div className="flex justify-between items-end">
        {/* Min Input */}
        <div className="w-24">
          <div className="py-2">Min</div>
          <div className="relative">
            {isCurrency && (
              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500">
                $
              </span>
            )}
            <Input
              type="text"
              value={isCurrency ? `${formatForDisplay(inputMin)}` : inputMin}
              onChange={handleInputMinChange}
              className={isCurrency ? "pl-6" : ""}
            />
          </div>
        </div>

        {/* Slider */}
        <div className="w-100">
          <Slider.Root
            className="relative flex items-center select-none touch-none w-[300px] h-5"
            value={sliderValue}
            onValueChange={handleSliderChange}
            min={min}
            max={max}
            step={step}
          >
            <Slider.Track className="bg-slate-200 relative grow rounded-full h-[8px]">
              <Slider.Range className="absolute bg-[#312E81] rounded-full h-full" />
            </Slider.Track>
            <Slider.Thumb
              className="block w-5 h-5 bg-white border-2 border-[#312E81] rounded-full hover:bg-slate-100 focus:outline-none"
              aria-label="Min"
            />
            <Slider.Thumb
              className="block w-5 h-5 bg-white border-2 border-[#312E81] rounded-full hover:bg-slate-100 focus:outline-none"
              aria-label="Max"
            />
          </Slider.Root>
        </div>

        {/* Max Input */}
        <div className="w-24">
          <div className="py-2">Max</div>
          <div className="relative">
            {isCurrency && (
              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500">
                $
              </span>
            )}
            <Input
              type="text"
              value={isCurrency ? `${formatForDisplay(inputMax)}` : inputMax}
              onChange={handleInputMaxChange}
              className={isCurrency ? "pl-6" : ""}
            />
          </div>
        </div>
      </div>

      {/* Error message */}
      {error && <div className="text-red-500 text-sm mt-3">{error}</div>}
    </div>
  );
};

export default RangeSelector;
