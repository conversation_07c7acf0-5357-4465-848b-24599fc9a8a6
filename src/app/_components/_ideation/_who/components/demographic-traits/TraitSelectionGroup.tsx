import React, { useEffect, useState } from "react";
import PopulationTraitValue from "./PopulationTraitValue";

interface TraitSelectionGroupProps {
  title: string;
  options: string[];
  selectedValues: string[];
  onChange: (value: string, isActive: boolean) => void;
  multiple?: boolean;
}

/**
 * A component for selecting traits from a group of options
 */
const TraitSelectionGroup: React.FC<TraitSelectionGroupProps> = ({
  title,
  options,
  selectedValues,
  onChange,
  multiple = true,
}) => {
  const [selected, setSelected] = useState<string[]>(selectedValues || []);

  // Update selected values when props change
  useEffect(() => {
    if (selectedValues) {
      setSelected(selectedValues);
    }
  }, [selectedValues]);

  const handleChange = (value: string, isActive: boolean) => {
    // Just pass the change to the parent component
    // The actual state management is handled in the context
    onChange(value, isActive);

    // The selected state will be updated via the useEffect when the props change
    // This ensures the UI stays in sync with the actual state in the context
  };

  const traitType = title.toLowerCase().replace(/\s+/g, "_");

  return (
    <div className="w-100 p-4">
      <div className="text-md font-semibold">{title}</div>
      <div className="grid grid-cols-2 gap-4 py-2">
        {options.map((option) => (
          <PopulationTraitValue
            key={option}
            traitValue={option}
            traitType={traitType}
            getTraitValue={(value, isActive) => handleChange(value, isActive)}
          />
        ))}
      </div>
    </div>
  );
};

export default TraitSelectionGroup;
