import { useState, useCallback, useEffect } from "react";
import React from "react";
import { useDemographicTraits } from "../../contexts/DemographicTraitsContext";
import Checkbox from "@/app/_components/_util/Checkbox";

interface PopulationTraitProps {
  traitValue: string;
  getTraitValue: (traitValue: string, activeStatus: boolean) => void;
  traitType: string;
}

const PopulationTraitValue = ({
  traitValue,
  getTraitValue,
  traitType,
}: PopulationTraitProps) => {
  const {
    educationValues,
    genderValues,
    childrenValues,
    raceValues,
    updateEducationValues,
    updateGenderValues,
    updateChildrenValues,
    updateRaceValues,
  } = useDemographicTraits();

  // Determine if this trait is selected based on the trait type
  const isSelected = useCallback(() => {
    // Normalize the trait type to handle different formats
    const normalizedType = traitType.toLowerCase().replace(/\s+/g, "_");

    switch (normalizedType) {
      case "education":
        return educationValues.includes(traitValue);
      case "gender":
        return genderValues.includes(traitValue);
      case "children":
      case "number_of_children":
        return childrenValues.includes(traitValue);
      case "race":
      case "racial_group":
        return raceValues.includes(traitValue);
      default:
        console.warn(`Unknown trait type: ${traitType}`);
        return false;
    }
  }, [
    traitType,
    traitValue,
    educationValues,
    genderValues,
    childrenValues,
    raceValues,
  ]);

  // Initialize state from context
  const [populationTrait, setPopulationTrait] = useState(isSelected);

  // Update local state when context values change
  useEffect(() => {
    setPopulationTrait(isSelected());
  }, [isSelected]);

  const handleOnClick = useCallback(() => {
    // Toggle the current state
    const newValue = !populationTrait;
    let updatedValues: string[] | undefined;

    const normalizedType = traitType.toLowerCase().replace(/\s+/g, "_");

    switch (normalizedType) {
      case "education":
        updatedValues = updateEducationValues(traitValue, newValue);
        break;
      case "gender":
        updatedValues = updateGenderValues(traitValue, newValue);
        break;
      case "children":
      case "number_of_children":
        updatedValues = updateChildrenValues(traitValue, newValue);
        break;
      case "race":
      case "racial_group":
        updatedValues = updateRaceValues(traitValue, newValue);
        break;
      default:
        console.warn(`Unknown trait type in handleOnClick: ${traitType}`);
        break;
    }

    if (updatedValues) {
      const finalState = updatedValues.includes(traitValue);

      // Update component state to match the actual state in the context
      setPopulationTrait(finalState);

      // Call the parent component's callback with the actual final state
      getTraitValue(traitValue, finalState);
    }
  }, [
    populationTrait,
    traitValue,
    traitType,
    getTraitValue,
    educationValues,
    genderValues,
    childrenValues,
    raceValues,
    updateEducationValues,
    updateGenderValues,
    updateChildrenValues,
    updateRaceValues,
  ]);

  return (
    <div className="flex items-center cursor-pointer" onClick={handleOnClick}>
      <div>
        <Checkbox selected={populationTrait} />
      </div>
      <div
        className={`px-2 flex-grow text-base font-roboto font-medium bg-transparent focus:border-b focus:outline-none ${
          populationTrait ? "text-text-dark" : "text-gray-500"
        }`}
      >
        {traitValue}
      </div>
    </div>
  );
};

export default React.memo(PopulationTraitValue);
