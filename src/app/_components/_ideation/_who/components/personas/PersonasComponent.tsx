import React, { useState, useEffect, useContext, useCallback } from "react";
import {
  Edit,
  Plus,
  Trash,
  User,
  Linkedin,
  Search,
  UserPlus,
  X,
  ChevronDown,
} from "lucide-react";
import ExperimentCreationContext from "../../../ExperimentCreationContext";
import { Persona } from "../../../objects";
import { LinkedInProfile } from "../../types";
import { LinkedInSearchParams } from "@/app/api/linkedin/search/route";
import axios from "axios";
import { getManagementToken } from "@/app/actions/auth/getManagementToken";

interface PersonasComponentProps {
  question: string;
}

// Add the new interface for dropdown options
interface Option {
  id: string;
  title: string;
  object: string;
}

const PersonasComponent: React.FC<PersonasComponentProps> = ({ question }) => {
  const { personas, setPersonas } = useContext(ExperimentCreationContext);
  const [token, setToken] = useState<string>("");

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPersona, setEditingPersona] = useState<Persona | null>(null);
  const [inputMethod, setInputMethod] = useState<
    "form" | "description" | "file" | "linkedin"
  >("form");
  const [description, setDescription] = useState("");
  const [descriptionName, setDescriptionName] = useState("");
  const [fileContent, setFileContent] = useState<string>("");
  const [fileName, setFileName] = useState<string>("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<Omit<
    Persona,
    "id"
  > | null>(null);
  const [multiplePersonasResults, setMultiplePersonasResults] = useState<
    Omit<Persona, "id">[]
  >([]);
  const [extractMultiple, setExtractMultiple] = useState(true);
  const [selectedPersonaIndex, setSelectedPersonaIndex] = useState<number>(-1);
  const [formData, setFormData] = useState<Omit<Persona, "id">>({
    name: "",
    age: "",
    gender: "",
    maritalStatus: "",
    income: "",
    education: "",
    racialGroup: "",
    homeOwnership: "",
    vehiclesOwned: "",
    hasDrivingLicense: "",
    location: "",
    occupation: "",
    background: "",
    goals: "",
    painPoints: "",
    personalityTraits: "",
    behaviors: "",
  });

  // Add LinkedIn search states
  const [isSearching, setIsSearching] = useState(false);
  const [linkedinProfiles, setLinkedinProfiles] = useState<LinkedInProfile[]>(
    []
  );
  const [selectedProfiles, setSelectedProfiles] = useState<number[]>([]);
  const [linkedinSearchParams, setLinkedinSearchParams] = useState<{
    industry: { id: string; title: string }[];
    location: { id: string; title: string }[];
    company: { id: string; title: string }[];
    past_company: string[];
    school: { id: string; title: string }[];
    advanced_keywords: {
      first_name: string;
      last_name: string;
      title: string;
      company: string;
      school: string;
    };
    company_headcount?: { min: number; max: number }[];
    company_type?: string[];
    tenure_at_company?: { min: number; max: number }[];
    tenure_at_role?: { min: number; max: number }[];
  }>({
    industry: [],
    location: [],
    company: [],
    past_company: [],
    school: [],
    advanced_keywords: {
      first_name: "",
      last_name: "",
      title: "",
      company: "",
      school: "",
    },
  });

  // Add new states for the select options
  const [industryOptions, setIndustryOptions] = useState<Option[]>([]);
  const [locationOptions, setLocationOptions] = useState<Option[]>([]);
  const [companyOptions, setCompanyOptions] = useState<Option[]>([]);
  const [schoolOptions, setSchoolOptions] = useState<Option[]>([]);
  const [titleOptions, setTitleOptions] = useState<Option[]>([]);
  const [skillOptions, setSkillOptions] = useState<Option[]>([]);

  // Add states for the search keywords
  const [industrySearchTerm, setIndustrySearchTerm] = useState("");
  const [locationSearchTerm, setLocationSearchTerm] = useState("");
  const [companySearchTerm, setCompanySearchTerm] = useState("");
  const [schoolSearchTerm, setSchoolSearchTerm] = useState("");
  const [titleSearchTerm, setTitleSearchTerm] = useState("");
  const [skillSearchTerm, setSkillSearchTerm] = useState("");

  // Add states for dropdown visibility
  const [showIndustryDropdown, setShowIndustryDropdown] = useState(false);
  const [showLocationDropdown, setShowLocationDropdown] = useState(false);
  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);
  const [showSchoolDropdown, setShowSchoolDropdown] = useState(false);
  const [showTitleDropdown, setShowTitleDropdown] = useState(false);
  const [showSkillDropdown, setShowSkillDropdown] = useState(false);
  const [showCompanyTypeDropdown, setShowCompanyTypeDropdown] = useState(false);
  const [showAdvancedKeywords, setShowAdvancedKeywords] = useState(false);

  // Add states for past company search
  const [pastCompanyOptions, setPastCompanyOptions] = useState<Option[]>([]);
  const [pastCompanySearchTerm, setPastCompanySearchTerm] = useState("");
  const [showPastCompanyDropdown, setShowPastCompanyDropdown] = useState(false);

  // Add pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(4);

  const getToken = async () => {
    return await getManagementToken();
  };
  useEffect(() => {
    getToken().then(setToken);
  }, []);
  // Create a function to fetch options from the search-parameter endpoint
  const fetchOptions = useCallback(
    async (
      type: string,
      keywords: string,
      setOptions: React.Dispatch<React.SetStateAction<Option[]>>
    ) => {
      if (!keywords) {
        setOptions([]);
        return;
      }

      try {
        const response = await fetch(
          `/api/linkedin/search-parameter?type=${type}&keywords=${encodeURIComponent(keywords)}`
        );
        if (!response.ok) {
          throw new Error(`Error fetching ${type} options`);
        }

        const data: Option[] = await response.json();
        setOptions(data);
      } catch (error) {
        console.error(`Error fetching ${type} options:`, error);
        setOptions([]);
      }
    },
    []
  );

  // Add useEffects to fetch options when search terms change
  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOptions("INDUSTRY", industrySearchTerm, setIndustryOptions);
    }, 300);

    return () => clearTimeout(handler);
  }, [industrySearchTerm, fetchOptions]);

  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOptions("LOCATION", locationSearchTerm, setLocationOptions);
    }, 300);

    return () => clearTimeout(handler);
  }, [locationSearchTerm, fetchOptions]);

  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOptions("COMPANY", companySearchTerm, setCompanyOptions);
    }, 300);

    return () => clearTimeout(handler);
  }, [companySearchTerm, fetchOptions]);

  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOptions("SCHOOL", schoolSearchTerm, setSchoolOptions);
    }, 300);

    return () => clearTimeout(handler);
  }, [schoolSearchTerm, fetchOptions]);

  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOptions("TITLE", titleSearchTerm, setTitleOptions);
    }, 300);

    return () => clearTimeout(handler);
  }, [titleSearchTerm, fetchOptions]);

  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOptions("SKILL", skillSearchTerm, setSkillOptions);
    }, 300);

    return () => clearTimeout(handler);
  }, [skillSearchTerm, fetchOptions]);

  // Add useEffect for past company search
  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOptions("COMPANY", pastCompanySearchTerm, setPastCompanyOptions);
    }, 300);

    return () => clearTimeout(handler);
  }, [pastCompanySearchTerm, fetchOptions]);

  // Function to add a selected option to the search params
  const addOption = (field: string, option: Option) => {
    setLinkedinSearchParams((prev) => {
      // Check if the option already exists in the array
      const exists =
        prev[field as keyof typeof prev] &&
        Array.isArray(prev[field as keyof typeof prev]) &&
        (prev[field as keyof typeof prev] as any[]).some(
          (item) => item.id === option.id
        );

      if (exists) return prev;

      return {
        ...prev,
        [field]: [
          ...(prev[field as keyof typeof prev] as any[]),
          { id: option.id, title: option.title },
        ],
      };
    });

    // Close the dropdown
    switch (field) {
      case "industry":
        setShowIndustryDropdown(false);
        setIndustrySearchTerm("");
        break;
      case "location":
        setShowLocationDropdown(false);
        setLocationSearchTerm("");
        break;
      case "company":
        setShowCompanyDropdown(false);
        setCompanySearchTerm("");
        break;
      case "school":
        setShowSchoolDropdown(false);
        setSchoolSearchTerm("");
        break;
      default:
        break;
    }
  };

  // Function to remove an option from the search params
  const removeOption = (field: string, optionId: string) => {
    setLinkedinSearchParams((prev) => ({
      ...prev,
      [field]: (prev[field as keyof typeof prev] as any[]).filter(
        (item) => item.id !== optionId
      ),
    }));
  };

  // Replacing useCompletion with a simple fetch function
  const analyzePersona = async (content: string) => {
    try {
      setIsAnalyzing(true);
      const response = await fetch("/api/analyze-persona", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content, extractMultiple }),
      });

      if (!response.ok) {
        throw new Error("Failed to analyze persona");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error analyzing persona:", error);
      return null;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      age: "",
      gender: "",
      maritalStatus: "",
      income: "",
      education: "",
      racialGroup: "",
      homeOwnership: "",
      vehiclesOwned: "",
      hasDrivingLicense: "",
      location: "",
      occupation: "",
      background: "",
      goals: "",
      painPoints: "",
      personalityTraits: "",
      behaviors: "",
    });
    setDescription("");
    setDescriptionName("");
    setFileContent("");
    setFileName("");
    setAnalysisResult(null);
    setEditingPersona(null);
    setMultiplePersonasResults([]);
    setSelectedPersonaIndex(-1);
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setDescription(e.target.value);
  };

  const handleDescriptionNameChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setDescriptionName(e.target.value);
  };

  const parseDescription = () => {
    // Extract basic information from the description
    const persona: Omit<Persona, "id"> = {
      name: descriptionName,
      age: "",
      gender: "",
      maritalStatus: "",
      income: "",
      education: "",
      racialGroup: "",
      homeOwnership: "",
      vehiclesOwned: "",
      hasDrivingLicense: "",
      location: "",
      occupation: "",
      background: "",
      goals: "",
      painPoints: "",
      personalityTraits: "",
      behaviors: "",
      isDescriptionBased: true,
      description: description,
    };

    // Try to extract age
    const ageMatch = description.match(/(\d+)(?:\s*years?\s*old|\s*yo)/i);
    if (ageMatch) {
      persona.age = ageMatch[1];
    }

    // Try to extract gender
    const genderMatch = description.match(
      /(?:is|am|are) ([^,.]+(?:male|female|non-binary|transgender|intersex|agender|genderqueer|genderfluid|two-spirit|third-gender|other)[^,.]*)/i
    );
    if (genderMatch) {
      persona.gender = genderMatch[1].trim();
    }

    // Try to extract marital status
    return persona;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    let newPersonaData: Omit<Persona, "id">;

    if (inputMethod === "description") {
      newPersonaData = parseDescription();
    } else {
      newPersonaData = { ...formData, isDescriptionBased: false };
    }

    if (editingPersona) {
      // Update existing persona
      setPersonas(
        personas.map((p) =>
          p.id === editingPersona.id
            ? { ...newPersonaData, id: editingPersona.id }
            : p
        )
      );
    } else {
      // Add new persona
      const newPersona: Persona = {
        ...newPersonaData,
        id: Date.now().toString(),
      };
      setPersonas([...personas, newPersona]);
    }
    resetForm();
    setIsFormOpen(false);
  };

  const handleEdit = (persona: Persona) => {
    if (persona.isDescriptionBased) {
      setInputMethod("description");
      setDescriptionName(persona.name);
      setDescription(persona.description || "");
    } else {
      setInputMethod("form");
      setFormData(persona);
    }
    setEditingPersona(persona);
    setIsFormOpen(true);
  };

  const handleDelete = (id: string) => {
    setPersonas(personas.filter((p) => p.id !== id));
  };

  const renderPersonaContent = (persona: Persona) => {
    const renderField = (label: string, value: string) => {
      if (!value) return null;

      // Get emoji for specific fields
      let prefix = "";
      if (label === "Gender") {
        const lowerGender = value.toLowerCase();
        if (lowerGender.includes("male") && !lowerGender.includes("female"))
          prefix = "👨 ";
        else if (lowerGender.includes("female")) prefix = "👩 ";
        else if (
          lowerGender.includes("non-binary") ||
          lowerGender.includes("nonbinary")
        )
          prefix = "⚧️ ";
      } else if (label === "Age") {
        prefix = "🗓️ ";
      } else if (label === "Location") {
        prefix = "📍 ";
      } else if (label === "Occupation") {
        prefix = "💼 ";
      } else if (label === "Education") {
        prefix = "🎓 ";
      } else if (label === "Goals") {
        prefix = "🎯 ";
      } else if (label === "Pain Points") {
        prefix = "⚠️ ";
      }

      return (
        <div className="flex items-start gap-2 mb-2 bg-gray-50 p-2 rounded-md hover:bg-gray-100 transition-colors">
          <p className="w-full">
            <span className="font-semibold text-gray-800 block mb-1">
              {label}
            </span>
            <span className="text-gray-700">
              {prefix}
              {value}
            </span>
          </p>
        </div>
      );
    };

    // Helper function to check if a section has any content
    const hasContent = (fields: { label: string; value: string }[]) => {
      return fields.some((field) => field.value);
    };

    // Define sections with their fields
    const basicInfoFields = [
      { label: "Age", value: persona.age },
      { label: "Gender", value: persona.gender },
      { label: "Location", value: persona.location },
      { label: "Occupation", value: persona.occupation },
    ];

    const demographicsFields = [
      { label: "Marital Status", value: persona.maritalStatus },
      { label: "Income", value: persona.income },
      { label: "Education", value: persona.education },
      { label: "Racial Group", value: persona.racialGroup },
    ];

    const lifestyleFields = [
      { label: "Home Ownership", value: persona.homeOwnership },
      { label: "Vehicles Owned", value: persona.vehiclesOwned },
      { label: "Has Driving License", value: persona.hasDrivingLicense },
    ];

    const psychologyFields = [
      { label: "Background", value: persona.background },
      { label: "Goals", value: persona.goals },
      { label: "Pain Points", value: persona.painPoints },
      { label: "Personality Traits", value: persona.personalityTraits },
      { label: "Behaviors", value: persona.behaviors },
    ];

    return (
      <div className="text-sm">
        {/* Basic Information Section - only show if has content */}
        {hasContent(basicInfoFields) && (
          <div className="mb-4">
            <h5 className="text-xs uppercase tracking-wider text-gray-500 mb-2 border-b pb-1">
              Basic Information
            </h5>
            {basicInfoFields.map((field) =>
              renderField(field.label, field.value)
            )}
          </div>
        )}

        {/* Demographics Section - only show if has content */}
        {hasContent(demographicsFields) && (
          <div className="mb-4">
            <h5 className="text-xs uppercase tracking-wider text-gray-500 mb-2 border-b pb-1">
              Demographics
            </h5>
            {demographicsFields.map((field) =>
              renderField(field.label, field.value)
            )}
          </div>
        )}

        {/* Lifestyle Section - only show if has content */}
        {hasContent(lifestyleFields) && (
          <div className="mb-4">
            <h5 className="text-xs uppercase tracking-wider text-gray-500 mb-2 border-b pb-1">
              Lifestyle
            </h5>
            {lifestyleFields.map((field) =>
              renderField(field.label, field.value)
            )}
          </div>
        )}

        {/* Psychology Section - only show if has content */}
        {hasContent(psychologyFields) && (
          <div className="mb-4">
            <h5 className="text-xs uppercase tracking-wider text-gray-500 mb-2 border-b pb-1">
              Psychology & Behavior
            </h5>
            {psychologyFields.map((field) =>
              renderField(field.label, field.value)
            )}
          </div>
        )}

        {/* Show a message if no information is available */}
        {!hasContent(basicInfoFields) &&
          !hasContent(demographicsFields) &&
          !hasContent(lifestyleFields) &&
          !hasContent(psychologyFields) && (
            <div className="text-center py-4 text-gray-500 italic">
              No detailed information available for this persona.
            </div>
          )}
      </div>
    );
  };

  const pdfToText = async (file: File): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const { data, status, statusText } = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/personas/extract-text-from-pdf`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
            Accept: "application/json",
          },
          // Include withCredentials if required by the target API
          // withCredentials: true,
        }
      );

      if (status !== 200) {
        const errorData = data;
        console.error("PDF extraction failed:", errorData);
        throw new Error(
          `Failed to extract text from PDF: ${errorData.error || statusText}`
        );
      }

      console.log("PDF extraction response:", data);
      return data;
    } catch (error) {
      console.error("Error extracting text from PDF:", error);
      throw error;
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const file = e.target.files[0];

    // Check if file size is within 20 MB limit
    const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20 MB in bytes
    if (file.size > MAX_FILE_SIZE) {
      alert("File size exceeds 20 MB limit. Please upload a smaller file.");
      return;
    }

    console.log("File selected:", file.name, file.type);
    setFileName(file.name);
    setMultiplePersonasResults([]);
    setSelectedPersonaIndex(-1);
    setAnalysisResult(null);

    const reader = new FileReader();

    reader.onerror = (error) => {
      console.error("FileReader error:", error);
    };

    reader.onloadstart = () => {
      console.log("File reading started");
    };

    reader.onload = async (event) => {
      console.log("File loaded successfully");
      let content;

      if (file.type === "application/pdf") {
        content = await pdfToText(file);
      } else {
        content = event.target?.result as string;
      }

      console.log("Content length:", content?.length);
      setFileContent(content);
      console.log("File content:", file.name);
      // Use the content directly instead of relying on state
      if (content && file.name) {
        try {
          console.log("Analyzing content...");
          const result = await analyzePersona(content);
          console.log("Analysis result:", result);

          console.log("Result:", result, extractMultiple, result.personas);

          if (extractMultiple && result && result.personas) {
            // Handle multiple personas
            setMultiplePersonasResults(
              result.personas.map((persona: any) => ({
                name: persona.name || "",
                age: persona.age || "",
                gender: persona.gender || "",
                maritalStatus: persona.maritalStatus || "",
                income: persona.income || "",
                education: persona.education || "",
                racialGroup: persona.racialGroup || "",
                homeOwnership: persona.homeOwnership || "",
                vehiclesOwned: persona.vehiclesOwned || "",
                hasDrivingLicense: persona.hasDrivingLicense || "",
                location: persona.location || "",
                occupation: persona.occupation || "",
                background: persona.background || "",
                goals: persona.goals || "",
                painPoints: persona.painPoints || "",
                personalityTraits: persona.personalityTraits || "",
                behaviors: persona.behaviors || "",
                isDescriptionBased: true,
                description: content,
              }))
            );

            if (result.personas.length > 0) {
              setSelectedPersonaIndex(0);
              const firstPersona = result.personas[0];
              setFormData({
                name: firstPersona.name || fileName.replace(/\.[^/.]+$/, ""),
                age: firstPersona.age || "",
                gender: firstPersona.gender || "",
                maritalStatus: firstPersona.maritalStatus || "",
                income: firstPersona.income || "",
                education: firstPersona.education || "",
                racialGroup: firstPersona.racialGroup || "",
                homeOwnership: firstPersona.homeOwnership || "",
                vehiclesOwned: firstPersona.vehiclesOwned || "",
                hasDrivingLicense: firstPersona.hasDrivingLicense || "",
                location: firstPersona.location || "",
                occupation: firstPersona.occupation || "",
                background: firstPersona.background || "",
                goals: firstPersona.goals || "",
                painPoints: firstPersona.painPoints || "",
                personalityTraits: firstPersona.personalityTraits || "",
                behaviors: firstPersona.behaviors || "",
              });
            }
          } else if (result) {
            // Handle single persona
            setAnalysisResult({
              name: result.name || fileName.replace(/\.[^/.]+$/, ""),
              age: result.age || "",
              gender: result.gender || "",
              maritalStatus: result.maritalStatus || "",
              income: result.income || "",
              education: result.education || "",
              racialGroup: result.racialGroup || "",
              homeOwnership: result.homeOwnership || "",
              vehiclesOwned: result.vehiclesOwned || "",
              hasDrivingLicense: result.hasDrivingLicense || "",
              location: result.location || "",
              occupation: result.occupation || "",
              background: result.background || "",
              goals: result.goals || "",
              painPoints: result.painPoints || "",
              personalityTraits: result.personalityTraits || "",
              behaviors: result.behaviors || "",
              isDescriptionBased: true,
              description: content,
            });

            setFormData({
              name: result.name || fileName.replace(/\.[^/.]+$/, ""),
              age: result.age || "",
              gender: result.gender || "",
              maritalStatus: result.maritalStatus || "",
              income: result.income || "",
              education: result.education || "",
              racialGroup: result.racialGroup || "",
              homeOwnership: result.homeOwnership || "",
              vehiclesOwned: result.vehiclesOwned || "",
              hasDrivingLicense: result.hasDrivingLicense || "",
              location: result.location || "",
              occupation: result.occupation || "",
              background: result.background || "",
              goals: result.goals || "",
              painPoints: result.painPoints || "",
              personalityTraits: result.personalityTraits || "",
              behaviors: result.behaviors || "",
            });
          }
        } catch (error) {
          console.error("Error analyzing file:", error);
        }
      }
    };

    try {
      console.log("Starting to read file as text");
      reader.readAsText(file);
    } catch (error) {
      console.error("Error calling readAsText:", error);
    }
  };

  const selectPersona = (index: number) => {
    if (index >= 0 && index < multiplePersonasResults.length) {
      setSelectedPersonaIndex(index);
      const selectedPersona = multiplePersonasResults[index];
      setFormData({
        name: selectedPersona.name,
        age: selectedPersona.age,
        gender: selectedPersona.gender,
        maritalStatus: selectedPersona.maritalStatus,
        income: selectedPersona.income,
        education: selectedPersona.education,
        racialGroup: selectedPersona.racialGroup,
        homeOwnership: selectedPersona.homeOwnership,
        vehiclesOwned: selectedPersona.vehiclesOwned,
        hasDrivingLicense: selectedPersona.hasDrivingLicense,
        location: selectedPersona.location,
        occupation: selectedPersona.occupation,
        background: selectedPersona.background,
        goals: selectedPersona.goals,
        painPoints: selectedPersona.painPoints,
        personalityTraits: selectedPersona.personalityTraits,
        behaviors: selectedPersona.behaviors,
      });
    }
  };

  const addSelectedPersona = () => {
    if (
      selectedPersonaIndex >= 0 &&
      selectedPersonaIndex < multiplePersonasResults.length
    ) {
      const newPersona: Persona = {
        ...formData,
        id: Date.now().toString(),
        isDescriptionBased: false,
      };
      setPersonas([...personas, newPersona]);

      // Move to the next persona if available
      if (selectedPersonaIndex < multiplePersonasResults.length - 1) {
        selectPersona(selectedPersonaIndex + 1);
      } else {
        // Reset if we've added all personas
        resetForm();
        setIsFormOpen(false);
      }
    }
  };

  const addAllPersonas = () => {
    if (multiplePersonasResults.length > 0) {
      const newPersonas = multiplePersonasResults.map((persona) => ({
        ...persona,
        id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
        isDescriptionBased: false,
      }));
      setPersonas([...personas, ...newPersonas]);
      resetForm();
      setIsFormOpen(false);
    }
  };

  // Handle LinkedIn input changes
  const handleLinkedinInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: string,
    isArray: boolean = false
  ) => {
    const value = e.target.value;

    if (isArray) {
      setLinkedinSearchParams((prev) => ({
        ...prev,
        [field]: value ? value.split(",").map((item) => item.trim()) : [],
      }));
    } else if (field.includes(".")) {
      // Handle nested fields (advanced_keywords)
      const [parent, child] = field.split(".");
      setLinkedinSearchParams((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value,
        },
      }));
    } else {
      setLinkedinSearchParams((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Function to search LinkedIn profiles
  const searchLinkedinProfiles = async () => {
    try {
      setIsSearching(true);
      setLinkedinProfiles([]);

      // Filter out empty values from advanced_keywords
      const advanced_keywords: { [key: string]: string } = {};
      Object.entries(linkedinSearchParams.advanced_keywords).forEach(
        ([key, value]) => {
          if (value) {
            advanced_keywords[key] = value;
          }
        }
      );

      // Create a base payload object
      const payload: LinkedInSearchParams = {};

      // Only add properties if they have values
      if (linkedinSearchParams.industry.length > 0) {
        payload.industry = {
          include: linkedinSearchParams.industry.map((item) => item.id),
        };
      }

      if (linkedinSearchParams.company.length > 0) {
        payload.company = {
          include: linkedinSearchParams.company.map((item) => item.id),
        };
      }

      if (linkedinSearchParams.past_company.length > 0) {
        payload.past_company = {
          include: linkedinSearchParams.past_company,
        };
      }

      if (linkedinSearchParams.school.length > 0) {
        payload.school = {
          include: linkedinSearchParams.school.map((item) => item.id),
        };
      }

      if (linkedinSearchParams.location.length > 0) {
        payload.company_location = {
          include: linkedinSearchParams.location.map((item) => item.id),
        };
      }

      if (advanced_keywords.first_name) {
        payload.first_name = advanced_keywords.first_name;
      }

      if (advanced_keywords.last_name) {
        payload.last_name = advanced_keywords.last_name;
      }

      if (advanced_keywords.title) {
        payload.keywords = advanced_keywords.title;
      }

      if (
        linkedinSearchParams.company_headcount &&
        linkedinSearchParams.company_headcount.length > 0
      ) {
        // Create a new range object with only the properties that have values
        const range: { min?: number; max?: number } = {};

        // Min company headcount dropdown values: 1, 11, 51, 201, 501, 1001, 5001, 10001
        const minValue = linkedinSearchParams.company_headcount[0].min;
        if (
          minValue > 0 &&
          [1, 11, 51, 201, 501, 1001, 5001, 10001].includes(minValue)
        ) {
          range.min = minValue;
        }

        // Max company headcount dropdown values: 10, 50, 200, 500, 1000, 5000, 10000
        const maxValue = linkedinSearchParams.company_headcount[0].max;
        if (
          maxValue > 0 &&
          [10, 50, 200, 500, 1000, 5000, 10000].includes(maxValue)
        ) {
          range.max = maxValue;
        }

        // Only add the range if it has at least one property
        if (Object.keys(range).length > 0) {
          payload.company_headcount = [range];
        }
      }

      if (
        linkedinSearchParams.company_type &&
        linkedinSearchParams.company_type.length > 0
      ) {
        payload.company_type = linkedinSearchParams.company_type;
      }

      if (
        linkedinSearchParams.tenure_at_company &&
        linkedinSearchParams.tenure_at_company.length > 0
      ) {
        // Create a new range object with only the properties that have values
        const range: { min?: number; max?: number } = {};

        // Min tenure at company dropdown values: 0,1,3,6,10
        const minValue = linkedinSearchParams.tenure_at_company[0].min;
        if (minValue >= 0 && [0, 1, 3, 6, 10].includes(minValue)) {
          range.min = minValue;
        }

        // Max tenure at company dropdown values: 1,2,5,10
        const maxValue = linkedinSearchParams.tenure_at_company[0].max;
        if (maxValue > 0 && [1, 2, 5, 10].includes(maxValue)) {
          range.max = maxValue;
        }

        // Only add the range if it has at least one property
        if (Object.keys(range).length > 0) {
          payload.tenure_at_company = [range];
        }
      }

      if (
        linkedinSearchParams.tenure_at_role &&
        linkedinSearchParams.tenure_at_role.length > 0
      ) {
        // Create a new range object with only the properties that have values
        const range: { min?: number; max?: number } = {};

        // Min tenure at role dropdown values: 0,1,3,6,10
        const minValue = linkedinSearchParams.tenure_at_role[0].min;
        if (minValue >= 0 && [0, 1, 3, 6, 10].includes(minValue)) {
          range.min = minValue;
        }

        // Max tenure at role dropdown values: 1,2,5,10
        const maxValue = linkedinSearchParams.tenure_at_role[0].max;
        if (maxValue > 0 && [1, 2, 5, 10].includes(maxValue)) {
          range.max = maxValue;
        }

        // Only add the range if it has at least one property
        if (Object.keys(range).length > 0) {
          payload.tenure_at_role = [range];
        }
      }

      const options = {
        method: "POST",
        headers: {
          accept: "application/json",
          "content-type": "application/json",
        },
        body: JSON.stringify(payload),
      };

      const response = await fetch("/api/linkedin/search", options);

      if (!response.ok) {
        throw new Error("Failed to search LinkedIn profiles");
      }

      const data = await response.json();

      if (data && Array.isArray(data)) {
        setLinkedinProfiles(data);
      } else {
        setLinkedinProfiles([]);
      }
    } catch (error) {
      console.error("Error searching LinkedIn profiles:", error);
    } finally {
      setIsSearching(false);
    }
  };

  // Toggle profile selection
  const toggleProfileSelection = (index: number) => {
    setSelectedProfiles((prev) => {
      if (prev.includes(index)) {
        return prev.filter((i) => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // Pagination functions
  const totalPages = Math.ceil(personas.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getPaginatedPersonas = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return personas.slice(startIndex, endIndex);
  };

  // Generate personas from selected LinkedIn profiles
  const generatePersonasFromProfiles = async () => {
    if (selectedProfiles.length === 0) return;

    setIsAnalyzing(true);

    try {
      const selectedProfilesData = selectedProfiles.map(
        (index) => linkedinProfiles[index]
      );
      const newPersonas: Persona[] = [...personas]; // Create a copy of the current personas

      // Process profiles in parallel using Promise.all
      const personaPromises = selectedProfilesData.map(async (profile) => {
        // Extract the profile data and format it for analysis
        const profileContent = `
          Name: ${profile.first_name || ""} ${profile.last_name || ""}
          Title: ${profile.headline || ""}
          Location: ${profile.location || ""}
          Industry: ${profile.industry || ""}
          Summary: ${profile.summary || ""}
          Experience: ${
            profile.work_experience
              ?.map(
                (exp) =>
                  `${exp.position} at ${exp.company} (${exp.start} - ${exp.end})`
              )
              .join("; ") || ""
          }
          Education: ${
            profile.education
              ?.map(
                (edu) =>
                  `${edu.degree} at ${edu.school} (${edu.start} - ${edu.end})`
              )
              .join("; ") || ""
          }
          Skills: ${profile.skills.map((skill) => skill.name).join(", ") || ""}
          Certifications: ${profile.certifications?.join(", ") || ""}
          Languages: ${profile.languages?.join(", ") || ""}
          Volunteering Experience: ${profile.volunteering_experience?.join(", ") || ""}
          Projects: ${profile.projects?.join(", ") || ""}
          Posts: ${
            profile.posts
              ?.map((post) =>
                post.text
                  ? `${post.date}: "${post.text}" (${post.reaction_counter} reactions)`
                  : ""
              )
              .filter(Boolean)
              .join(", ") || ""
          }
        `;

        // Analyze the profile to create a persona
        const {
          personas: [result],
        } = await analyzePersona(profileContent);

        if (result) {
          // Create a new persona from the analysis result
          const newPersona: Persona = {
            id:
              Date.now().toString() +
              Math.random().toString(36).substring(2, 9),
            name:
              result.name || `${profile.first_name} ${profile.last_name}` || "",
            age: result.age || "",
            gender: result.gender || "",
            maritalStatus: result.maritalStatus || "",
            income: result.income || "",
            education: result.education || profile.education?.[0]?.degree || "",
            racialGroup: result.racialGroup || "",
            homeOwnership: result.homeOwnership || "",
            vehiclesOwned: result.vehiclesOwned || "",
            hasDrivingLicense: result.hasDrivingLicense || "",
            location: result.location || profile.location || "",
            occupation: result.occupation || profile.headline || "",
            background:
              result.background ||
              `Worked at ${profile.work_experience?.map((exp) => exp.company).join(", ")}` ||
              "",
            goals: result.goals || "",
            painPoints: result.painPoints || "",
            personalityTraits: result.personalityTraits || "",
            behaviors: result.behaviors || "",
            isDescriptionBased: false,
            linkedInSource: profile.public_identifier || "",
          };
          return newPersona;
        }
        return null;
      });

      // Wait for all persona analyses to complete
      const results = await Promise.all(personaPromises);

      // Filter out null results and add valid personas to the array
      const validPersonas = results.filter(
        (persona): persona is Persona => persona !== null
      );
      newPersonas.push(...validPersonas);

      // Update all personas at once with the new array
      setPersonas(newPersonas);

      // Reset states after processing
      setSelectedProfiles([]);
      resetForm();
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error generating personas from LinkedIn profiles:", error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col h-full">
        <div className="flex flex-col gap-6 items-start max-w-4xl overflow-y-auto">
          <div className="flex flex-col gap-2 items-stretch w-full">
            <h2 className="text-text-dark font-roboto font-semibold text-2xl">
              Create your personas
            </h2>
            <p className="text-gray-600">
              This section will allow you to create detailed personas for your
              research.
            </p>
          </div>

          {/* Personas List */}
          <div className="w-full">
            {personas.length > 0 ? (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">
                  Your Personas
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getPaginatedPersonas().map((persona) => (
                    <div
                      key={persona.id}
                      className="bg-white rounded-lg border border-[#E4E7EC] p-4 shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center gap-2">
                          <User size={18} className="text-gray-700" />
                          <h4 className="text-lg font-semibold text-gray-800">
                            {persona.name}
                          </h4>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleEdit(persona)}
                            className="p-1.5 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                            aria-label="Edit persona"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(persona.id)}
                            className="p-1.5 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                            aria-label="Delete persona"
                          >
                            <Trash size={16} />
                          </button>
                        </div>
                      </div>
                      {renderPersonaContent(persona)}
                    </div>
                  ))}
                </div>
                {personas.length > itemsPerPage && (
                  <React.Fragment>
                    <div className="flex justify-center mt-6">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className={`p-2 rounded-md ${
                            currentPage === 1
                              ? "text-gray-400 cursor-not-allowed"
                              : "text-blue-600 hover:bg-blue-50"
                          }`}
                        >
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M12.5 15L7.5 10L12.5 5"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </button>
                        {Array.from(
                          { length: totalPages },
                          (_, i) => i + 1
                        ).map((page) => (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`p-2 rounded-md w-8 h-8 text-sm flex items-center justify-center ${
                              currentPage === page
                                ? "bg-blue-600 text-white"
                                : "text-gray-700 hover:bg-blue-50"
                            }`}
                          >
                            {page}
                          </button>
                        ))}
                        <button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className={`p-2 rounded-md ${
                            currentPage === totalPages
                              ? "text-gray-400 cursor-not-allowed"
                              : "text-blue-600 hover:bg-blue-50"
                          }`}
                        >
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.5 5L12.5 10L7.5 15"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </React.Fragment>
                )}
              </div>
            ) : null}
          </div>

          {/* Add Persona Button */}
          {!isFormOpen && (
            <button
              onClick={() => setIsFormOpen(true)}
              className="flex items-center gap-2 bg-gray-100 border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors font-medium mx-auto"
            >
              <Plus size={18} />
              {personas.length === 0
                ? "Create Your First Persona"
                : "Add Another Persona"}
            </button>
          )}

          {/* Persona Form */}
          {isFormOpen && (
            <div className="w-full bg-white rounded-lg border border-[#E4E7EC] p-6">
              <h3 className="text-lg font-medium mb-4">
                {editingPersona ? "Edit Persona" : "Create New Persona"}
              </h3>

              {/* Input Method Toggle */}
              {!editingPersona && (
                <div className="mb-6">
                  <div className="flex gap-4 p-2 bg-gray-50 rounded-lg">
                    <button
                      onClick={() => setInputMethod("form")}
                      className={`flex-1 py-2 px-4 rounded-md text-center ${
                        inputMethod === "form"
                          ? "bg-white shadow-sm"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      Detailed Fields
                    </button>
                    {/* <button
                      onClick={() => setInputMethod("description")}
                      className={`flex-1 py-2 px-4 rounded-md text-center ${
                        inputMethod === "description"
                          ? "bg-white shadow-sm"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      Quick Description
                    </button> */}
                    <button
                      onClick={() => setInputMethod("file")}
                      className={`flex-1 py-2 px-4 rounded-md text-center ${
                        inputMethod === "file"
                          ? "bg-white shadow-sm"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      Upload File
                    </button>
                    <button
                      onClick={() => setInputMethod("linkedin")}
                      className={`flex-1 py-2 px-4 rounded-md text-center ${
                        inputMethod === "linkedin"
                          ? "bg-white shadow-sm"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      <div className="flex items-center justify-center gap-2">
                        <span>Search Web</span>
                      </div>
                    </button>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                {inputMethod === "description" ? (
                  <>
                    <div>
                      <label
                        htmlFor="descriptionName"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Name
                      </label>
                      <input
                        type="text"
                        id="descriptionName"
                        value={descriptionName}
                        onChange={handleDescriptionNameChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        placeholder="Enter persona name"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="description"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Describe your persona
                      </label>
                      <textarea
                        id="description"
                        value={description}
                        onChange={handleDescriptionChange}
                        rows={6}
                        placeholder="Describe your persona in natural language. For example: 'A 32-year-old software engineer working in a tech startup. He is passionate about creating user-friendly applications...'"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        required
                      />
                    </div>
                  </>
                ) : inputMethod === "file" ? (
                  <>
                    <div>
                      <label
                        htmlFor="fileUpload"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Upload Persona Document
                      </label>
                      <div className="mt-1 flex items-center">
                        <label
                          htmlFor="fileUpload"
                          className="cursor-pointer px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                          Choose File
                          <input
                            id="fileUpload"
                            type="file"
                            accept=".txt,.md,.doc,.docx,.pdf"
                            onChange={handleFileUpload}
                            className="sr-only"
                          />
                        </label>
                        <span className="ml-3 text-sm text-gray-500">
                          {fileName || "No file chosen"}
                        </span>
                      </div>
                      <div className="flex items-center mt-2">
                        <input
                          type="checkbox"
                          id="extractMultiple"
                          checked={extractMultiple}
                          onChange={(e) => setExtractMultiple(e.target.checked)}
                          className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded"
                        />
                        <label
                          htmlFor="extractMultiple"
                          className="ml-2 block text-sm text-gray-700"
                        >
                          Extract multiple personas from this document
                        </label>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Upload a document containing persona information. Our AI
                        will analyze it to extract relevant details.
                      </p>
                    </div>

                    {fileContent && (
                      <div className="mt-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="text-sm font-medium text-gray-700">
                            File Content
                          </h4>
                        </div>
                      </div>
                    )}

                    {isAnalyzing && (
                      <div className="mt-4 p-4 border border-blue-200 bg-blue-50 rounded-md">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
                          <span className="ml-2 text-sm text-gray-600">
                            Analyzing document...
                          </span>
                        </div>
                      </div>
                    )}

                    {multiplePersonasResults.length > 0 && (
                      <div className="mt-4 p-4 border border-green-200 bg-green-50 rounded-md">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">
                          Multiple Personas Found (
                          {multiplePersonasResults.length})
                        </h4>

                        <div className="mb-4">
                          <div className="flex flex-wrap gap-2 mb-3">
                            {multiplePersonasResults.map((persona, index) => (
                              <button
                                key={index}
                                type="button"
                                onClick={() => selectPersona(index)}
                                className={`px-3 py-1 text-sm rounded-full ${
                                  selectedPersonaIndex === index
                                    ? "bg-gray-600 text-white"
                                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                }`}
                              >
                                {persona.name || `Persona ${index + 1}`}
                              </button>
                            ))}
                          </div>

                          <div className="flex gap-2 mb-3">
                            <button
                              type="button"
                              onClick={addSelectedPersona}
                              disabled={selectedPersonaIndex < 0}
                              className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:text-gray-500"
                            >
                              Add Selected Persona
                            </button>
                            <button
                              type="button"
                              onClick={addAllPersonas}
                              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                              Add All Personas
                            </button>
                          </div>
                        </div>

                        {selectedPersonaIndex >= 0 && (
                          <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                              <div>
                                <label
                                  htmlFor="gender"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Gender
                                </label>
                                <input
                                  type="text"
                                  id="gender"
                                  name="gender"
                                  value={formData.gender}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                              <div>
                                <label
                                  htmlFor="maritalStatus"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Marital Status
                                </label>
                                <input
                                  type="text"
                                  id="maritalStatus"
                                  name="maritalStatus"
                                  value={formData.maritalStatus}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                              <div>
                                <label
                                  htmlFor="income"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Income
                                </label>
                                <input
                                  type="text"
                                  id="income"
                                  name="income"
                                  value={formData.income}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                              <div>
                                <label
                                  htmlFor="education"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Education
                                </label>
                                <input
                                  type="text"
                                  id="education"
                                  name="education"
                                  value={formData.education}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                              <div>
                                <label
                                  htmlFor="racialGroup"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Racial Group
                                </label>
                                <input
                                  type="text"
                                  id="racialGroup"
                                  name="racialGroup"
                                  value={formData.racialGroup}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                              <div>
                                <label
                                  htmlFor="location"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Location
                                </label>
                                <input
                                  type="text"
                                  id="location"
                                  name="location"
                                  value={formData.location}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                              <div>
                                <label
                                  htmlFor="homeOwnership"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Home Ownership
                                </label>
                                <input
                                  type="text"
                                  id="homeOwnership"
                                  name="homeOwnership"
                                  value={formData.homeOwnership}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                              <div>
                                <label
                                  htmlFor="vehiclesOwned"
                                  className="block text-xs font-medium text-gray-700"
                                >
                                  Vehicles Owned
                                </label>
                                <input
                                  type="text"
                                  id="vehiclesOwned"
                                  name="vehiclesOwned"
                                  value={formData.vehiclesOwned}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                />
                              </div>
                            </div>

                            <div className="mt-2">
                              <label
                                htmlFor="hasDrivingLicense"
                                className="block text-xs font-medium text-gray-700"
                              >
                                Has Driving License
                              </label>
                              <select
                                id="hasDrivingLicense"
                                name="hasDrivingLicense"
                                value={formData.hasDrivingLicense}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              >
                                <option value="">Select...</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </select>
                            </div>

                            <div className="mt-2">
                              <label
                                htmlFor="occupation"
                                className="block text-xs font-medium text-gray-700"
                              >
                                Occupation
                              </label>
                              <input
                                type="text"
                                id="occupation"
                                name="occupation"
                                value={formData.occupation}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>

                            <div className="mt-2">
                              <label
                                htmlFor="background"
                                className="block text-xs font-medium text-gray-700"
                              >
                                Background
                              </label>
                              <textarea
                                id="background"
                                name="background"
                                value={formData.background}
                                onChange={handleInputChange}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>

                            <div className="mt-2">
                              <label
                                htmlFor="goals"
                                className="block text-xs font-medium text-gray-700"
                              >
                                Goals
                              </label>
                              <textarea
                                id="goals"
                                name="goals"
                                value={formData.goals}
                                onChange={handleInputChange}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>

                            <div className="mt-2">
                              <label
                                htmlFor="painPoints"
                                className="block text-xs font-medium text-gray-700"
                              >
                                Pain Points
                              </label>
                              <textarea
                                id="painPoints"
                                name="painPoints"
                                value={formData.painPoints}
                                onChange={handleInputChange}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>

                            <div className="mt-2">
                              <label
                                htmlFor="personalityTraits"
                                className="block text-xs font-medium text-gray-700"
                              >
                                Personality Traits
                              </label>
                              <textarea
                                id="personalityTraits"
                                name="personalityTraits"
                                value={formData.personalityTraits}
                                onChange={handleInputChange}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>

                            <div className="mt-2">
                              <label
                                htmlFor="behaviors"
                                className="block text-xs font-medium text-gray-700"
                              >
                                Behaviors
                              </label>
                              <textarea
                                id="behaviors"
                                name="behaviors"
                                value={formData.behaviors}
                                onChange={handleInputChange}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>
                          </>
                        )}
                        <p className="text-xs text-gray-500 mt-3">
                          Review and edit the extracted information before
                          adding personas.
                        </p>
                      </div>
                    )}
                  </>
                ) : inputMethod === "linkedin" ? (
                  <>
                    <h4 className="font-medium">Profile Search</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Industry multiselect */}
                      <div className="relative">
                        <label
                          htmlFor="linkedin-industry"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Industry
                        </label>
                        <div className="relative">
                          {/* Selected options */}
                          <div className="flex flex-wrap gap-2 mb-2">
                            {linkedinSearchParams.industry.map((option) => (
                              <div
                                key={option.id}
                                className="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded-full flex items-center gap-1"
                              >
                                <span>{option.title}</span>
                                <button
                                  type="button"
                                  onClick={() =>
                                    removeOption("industry", option.id)
                                  }
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <X size={14} />
                                </button>
                              </div>
                            ))}
                          </div>
                          {/* Search input */}
                          <div className="relative">
                            <input
                              type="text"
                              id="linkedin-industry"
                              value={industrySearchTerm}
                              onChange={(e) =>
                                setIndustrySearchTerm(e.target.value)
                              }
                              onFocus={() => setShowIndustryDropdown(true)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              placeholder="Search for industries..."
                            />
                            <button
                              type="button"
                              className="absolute right-2 top-1/2 transform -translate-y-1/2"
                              onClick={() =>
                                setShowIndustryDropdown(!showIndustryDropdown)
                              }
                            >
                              <ChevronDown
                                size={18}
                                className="text-gray-500"
                              />
                            </button>
                          </div>
                          {/* Dropdown */}
                          {showIndustryDropdown &&
                            industryOptions.length > 0 && (
                              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                {industryOptions.map((option) => (
                                  <div
                                    key={option.id}
                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() =>
                                      addOption("industry", option)
                                    }
                                  >
                                    {option.title}
                                  </div>
                                ))}
                              </div>
                            )}
                        </div>
                      </div>

                      {/* Location multiselect */}
                      <div className="relative">
                        <label
                          htmlFor="linkedin-location"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Location
                        </label>
                        <div className="relative">
                          {/* Selected options */}
                          <div className="flex flex-wrap gap-2 mb-2">
                            {linkedinSearchParams.location.map((option) => (
                              <div
                                key={option.id}
                                className="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded-full flex items-center gap-1"
                              >
                                <span>{option.title}</span>
                                <button
                                  type="button"
                                  onClick={() =>
                                    removeOption("location", option.id)
                                  }
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <X size={14} />
                                </button>
                              </div>
                            ))}
                          </div>
                          {/* Search input */}
                          <div className="relative">
                            <input
                              type="text"
                              id="linkedin-location"
                              value={locationSearchTerm}
                              onChange={(e) =>
                                setLocationSearchTerm(e.target.value)
                              }
                              onFocus={() => setShowLocationDropdown(true)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              placeholder="Search for locations..."
                            />
                            <button
                              type="button"
                              className="absolute right-2 top-1/2 transform -translate-y-1/2"
                              onClick={() =>
                                setShowLocationDropdown(!showLocationDropdown)
                              }
                            >
                              <ChevronDown
                                size={18}
                                className="text-gray-500"
                              />
                            </button>
                          </div>
                          {/* Dropdown */}
                          {showLocationDropdown &&
                            locationOptions.length > 0 && (
                              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                {locationOptions.map((option) => (
                                  <div
                                    key={option.id}
                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() =>
                                      addOption("location", option)
                                    }
                                  >
                                    {option.title}
                                  </div>
                                ))}
                              </div>
                            )}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Company multiselect */}
                      <div className="relative">
                        <label
                          htmlFor="linkedin-company"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Current Company
                        </label>
                        <div className="relative">
                          {/* Selected options */}
                          <div className="flex flex-wrap gap-2 mb-2">
                            {linkedinSearchParams.company.map((option) => (
                              <div
                                key={option.id}
                                className="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded-full flex items-center gap-1"
                              >
                                <span>{option.title}</span>
                                <button
                                  type="button"
                                  onClick={() =>
                                    removeOption("company", option.id)
                                  }
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <X size={14} />
                                </button>
                              </div>
                            ))}
                          </div>
                          {/* Search input */}
                          <div className="relative">
                            <input
                              type="text"
                              id="linkedin-company"
                              value={companySearchTerm}
                              onChange={(e) =>
                                setCompanySearchTerm(e.target.value)
                              }
                              onFocus={() => setShowCompanyDropdown(true)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              placeholder="Search for companies..."
                            />
                            <button
                              type="button"
                              className="absolute right-2 top-1/2 transform -translate-y-1/2"
                              onClick={() =>
                                setShowCompanyDropdown(!showCompanyDropdown)
                              }
                            >
                              <ChevronDown
                                size={18}
                                className="text-gray-500"
                              />
                            </button>
                          </div>
                          {/* Dropdown */}
                          {showCompanyDropdown && companyOptions.length > 0 && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                              {companyOptions.map((option) => (
                                <div
                                  key={option.id}
                                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                  onClick={() => addOption("company", option)}
                                >
                                  {option.title}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Past Company - Updated to use search filter */}
                      <div className="relative">
                        <label
                          htmlFor="linkedin-past-company"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Past Company
                        </label>
                        <div className="relative">
                          {/* Selected options */}
                          <div className="flex flex-wrap gap-2 mb-2">
                            {linkedinSearchParams.past_company.map(
                              (company) => (
                                <div
                                  key={company}
                                  className="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded-full flex items-center gap-1"
                                >
                                  <span>{company}</span>
                                  <button
                                    type="button"
                                    onClick={() => {
                                      setLinkedinSearchParams((prev) => ({
                                        ...prev,
                                        past_company: prev.past_company.filter(
                                          (c) => c !== company
                                        ),
                                      }));
                                    }}
                                    className="text-gray-500 hover:text-gray-700"
                                  >
                                    <X size={14} />
                                  </button>
                                </div>
                              )
                            )}
                          </div>
                          {/* Search input */}
                          <div className="relative">
                            <input
                              type="text"
                              id="linkedin-past-company"
                              value={pastCompanySearchTerm}
                              onChange={(e) =>
                                setPastCompanySearchTerm(e.target.value)
                              }
                              onFocus={() => setShowPastCompanyDropdown(true)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              placeholder="Search for past companies..."
                            />
                            <button
                              type="button"
                              className="absolute right-2 top-1/2 transform -translate-y-1/2"
                              onClick={() =>
                                setShowPastCompanyDropdown(
                                  !showPastCompanyDropdown
                                )
                              }
                            >
                              <ChevronDown
                                size={18}
                                className="text-gray-500"
                              />
                            </button>
                          </div>
                          {/* Dropdown */}
                          {showPastCompanyDropdown &&
                            pastCompanyOptions.length > 0 && (
                              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                {pastCompanyOptions.map((option) => (
                                  <div
                                    key={option.id}
                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() => {
                                      setLinkedinSearchParams((prev) => ({
                                        ...prev,
                                        past_company: [
                                          ...prev.past_company,
                                          option.title,
                                        ],
                                      }));
                                      setPastCompanySearchTerm("");
                                      setShowPastCompanyDropdown(false);
                                    }}
                                  >
                                    {option.title}
                                  </div>
                                ))}
                              </div>
                            )}
                        </div>
                      </div>
                    </div>

                    {/* School multiselect */}
                    <div className="relative">
                      <label
                        htmlFor="linkedin-school"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        School
                      </label>
                      <div className="relative">
                        {/* Selected options */}
                        <div className="flex flex-wrap gap-2 mb-2">
                          {linkedinSearchParams.school.map((option) => (
                            <div
                              key={option.id}
                              className="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded-full flex items-center gap-1"
                            >
                              <span>{option.title}</span>
                              <button
                                type="button"
                                onClick={() =>
                                  removeOption("school", option.id)
                                }
                                className="text-gray-500 hover:text-gray-700"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          ))}
                        </div>
                        {/* Search input */}
                        <div className="relative">
                          <input
                            type="text"
                            id="linkedin-school"
                            value={schoolSearchTerm}
                            onChange={(e) =>
                              setSchoolSearchTerm(e.target.value)
                            }
                            onFocus={() => setShowSchoolDropdown(true)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            placeholder="Search for schools..."
                          />
                          <button
                            type="button"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2"
                            onClick={() =>
                              setShowSchoolDropdown(!showSchoolDropdown)
                            }
                          >
                            <ChevronDown size={18} className="text-gray-500" />
                          </button>
                        </div>
                        {/* Dropdown */}
                        {showSchoolDropdown && schoolOptions.length > 0 && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                            {schoolOptions.map((option) => (
                              <div
                                key={option.id}
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => addOption("school", option)}
                              >
                                {option.title}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Company Characteristics Section */}
                    <div className="mt-6">
                      <h5 className="text-sm font-medium text-gray-700 mb-3">
                        Company Characteristics
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label
                            htmlFor="company-headcount"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Company Size
                          </label>
                          <div className="flex gap-2">
                            <select
                              id="company-headcount-min"
                              value={
                                linkedinSearchParams.company_headcount?.[0]
                                  ?.min || ""
                              }
                              onChange={(e) => {
                                const min = parseInt(e.target.value);
                                setLinkedinSearchParams((prev) => ({
                                  ...prev,
                                  company_headcount: [
                                    {
                                      min: min || 0,
                                      max:
                                        prev.company_headcount?.[0]?.max || 0,
                                    },
                                  ],
                                }));
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                              <option value="">Min</option>
                              <option value="1">1</option>
                              <option value="11">11</option>
                              <option value="51">51</option>
                              <option value="201">201</option>
                              <option value="501">501</option>
                              <option value="1001">1001</option>
                              <option value="5001">5001</option>
                              <option value="10001">10001</option>
                            </select>
                            <select
                              id="company-headcount-max"
                              value={
                                linkedinSearchParams.company_headcount?.[0]
                                  ?.max || ""
                              }
                              onChange={(e) => {
                                const max = parseInt(e.target.value);
                                setLinkedinSearchParams((prev) => ({
                                  ...prev,
                                  company_headcount: [
                                    {
                                      min:
                                        prev.company_headcount?.[0]?.min || 0,
                                      max: max || 0,
                                    },
                                  ],
                                }));
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                              <option value="">Max</option>
                              <option value="10">10</option>
                              <option value="50">50</option>
                              <option value="200">200</option>
                              <option value="500">500</option>
                              <option value="1000">1000</option>
                              <option value="5000">5000</option>
                              <option value="10000">10000</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label
                            htmlFor="company-type"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Company Type
                          </label>
                          <div className="relative">
                            {/* Selected options display */}
                            <div className="flex flex-wrap gap-2 mb-2">
                              {(linkedinSearchParams.company_type || []).map(
                                (type) => (
                                  <div
                                    key={type}
                                    className="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded-full flex items-center gap-1"
                                  >
                                    <span>
                                      {type
                                        .split("_")
                                        .map(
                                          (word) =>
                                            word.charAt(0).toUpperCase() +
                                            word.slice(1)
                                        )
                                        .join(" ")}
                                    </span>
                                    <button
                                      type="button"
                                      onClick={() => {
                                        setLinkedinSearchParams((prev) => ({
                                          ...prev,
                                          company_type:
                                            prev.company_type?.filter(
                                              (t) => t !== type
                                            ) || [],
                                        }));
                                      }}
                                      className="text-gray-500 hover:text-gray-700"
                                    >
                                      <X size={14} />
                                    </button>
                                  </div>
                                )
                              )}
                            </div>

                            {/* Dropdown button */}
                            <button
                              type="button"
                              onClick={() =>
                                setShowCompanyTypeDropdown(
                                  !showCompanyTypeDropdown
                                )
                              }
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 bg-white text-left flex justify-between items-center"
                            >
                              <span className="text-gray-700">
                                {linkedinSearchParams.company_type?.length
                                  ? `${linkedinSearchParams.company_type.length} selected`
                                  : "Select company types..."}
                              </span>
                              <ChevronDown
                                size={18}
                                className="text-gray-500"
                              />
                            </button>

                            {/* Dropdown menu */}
                            {showCompanyTypeDropdown && (
                              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                {[
                                  {
                                    value: "public_company",
                                    label: "Public Company",
                                  },
                                  {
                                    value: "privately_held",
                                    label: "Privately Held",
                                  },
                                  { value: "non_profit", label: "Non-Profit" },
                                  {
                                    value: "educational_institution",
                                    label: "Educational Institution",
                                  },
                                  {
                                    value: "partnership",
                                    label: "Partnership",
                                  },
                                  {
                                    value: "self_employed",
                                    label: "Self-Employed",
                                  },
                                  { value: "self_owned", label: "Self-Owned" },
                                  {
                                    value: "government_agency",
                                    label: "Government Agency",
                                  },
                                ].map((option) => (
                                  <div
                                    key={option.value}
                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center gap-2"
                                    onClick={() => {
                                      setLinkedinSearchParams((prev) => ({
                                        ...prev,
                                        company_type:
                                          prev.company_type?.includes(
                                            option.value
                                          )
                                            ? prev.company_type.filter(
                                                (type) => type !== option.value
                                              )
                                            : [
                                                ...(prev.company_type || []),
                                                option.value,
                                              ],
                                      }));
                                    }}
                                  >
                                    <input
                                      type="checkbox"
                                      checked={
                                        linkedinSearchParams.company_type?.includes(
                                          option.value
                                        ) || false
                                      }
                                      onChange={() => {}}
                                      className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded"
                                    />
                                    <span>{option.label}</span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <label
                            htmlFor="tenure-company"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Years at Company
                          </label>
                          <div className="flex gap-2">
                            <select
                              id="tenure-company-min"
                              value={
                                linkedinSearchParams.tenure_at_company?.[0]
                                  ?.min || ""
                              }
                              onChange={(e) => {
                                const min = parseInt(e.target.value);
                                setLinkedinSearchParams((prev) => ({
                                  ...prev,
                                  tenure_at_company: [
                                    {
                                      min: min || 0,
                                      max:
                                        prev.tenure_at_company?.[0]?.max || 0,
                                    },
                                  ],
                                }));
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                              <option value="">Min years</option>
                              <option value="0">0</option>
                              <option value="1">1</option>
                              <option value="3">3</option>
                              <option value="6">6</option>
                              <option value="10">10</option>
                            </select>
                            <select
                              id="tenure-company-max"
                              value={
                                linkedinSearchParams.tenure_at_company?.[0]
                                  ?.max || ""
                              }
                              onChange={(e) => {
                                const max = parseInt(e.target.value);
                                setLinkedinSearchParams((prev) => ({
                                  ...prev,
                                  tenure_at_company: [
                                    {
                                      min:
                                        prev.tenure_at_company?.[0]?.min || 0,
                                      max: max || 0,
                                    },
                                  ],
                                }));
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                              <option value="">Max years</option>
                              <option value="1">1</option>
                              <option value="2">2</option>
                              <option value="5">5</option>
                              <option value="10">10</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label
                            htmlFor="tenure-role"
                            className="block text-sm font-medium text-gray-700 mb-1"
                          >
                            Years in Role
                          </label>
                          <div className="flex gap-2">
                            <select
                              id="tenure-role-min"
                              value={
                                linkedinSearchParams.tenure_at_role?.[0]?.min ||
                                ""
                              }
                              onChange={(e) => {
                                const min = parseInt(e.target.value);
                                setLinkedinSearchParams((prev) => ({
                                  ...prev,
                                  tenure_at_role: [
                                    {
                                      min: min || 0,
                                      max: prev.tenure_at_role?.[0]?.max || 0,
                                    },
                                  ],
                                }));
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                              <option value="">Min years</option>
                              <option value="0">0</option>
                              <option value="1">1</option>
                              <option value="3">3</option>
                              <option value="6">6</option>
                              <option value="10">10</option>
                            </select>
                            <select
                              id="tenure-role-max"
                              value={
                                linkedinSearchParams.tenure_at_role?.[0]?.max ||
                                ""
                              }
                              onChange={(e) => {
                                const max = parseInt(e.target.value);
                                setLinkedinSearchParams((prev) => ({
                                  ...prev,
                                  tenure_at_role: [
                                    {
                                      min: prev.tenure_at_role?.[0]?.min || 0,
                                      max: max || 0,
                                    },
                                  ],
                                }));
                              }}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                              <option value="">Max years</option>
                              <option value="1">1</option>
                              <option value="2">2</option>
                              <option value="5">5</option>
                              <option value="10">10</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Advanced Keywords Section - Moved to end and made collapsible */}
                    {/* <div className="mt-6 border-t pt-6">
                      <button
                        type="button"
                        onClick={() =>
                          setShowAdvancedKeywords(!showAdvancedKeywords)
                        }
                        className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
                      >
                        <ChevronDown
                          size={16}
                          className={`transform transition-transform ${
                            showAdvancedKeywords ? "rotate-180" : ""
                          }`}
                        />
                        <span>Advanced Keywords</span>
                      </button>

                      {showAdvancedKeywords && (
                        <div className="mt-4 space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label
                                htmlFor="linkedin-first-name"
                                className="block text-sm font-medium text-gray-700 mb-1"
                              >
                                First Name
                              </label>
                              <input
                                type="text"
                                id="linkedin-first-name"
                                value={
                                  linkedinSearchParams.advanced_keywords
                                    .first_name
                                }
                                onChange={(e) =>
                                  handleLinkedinInputChange(
                                    e,
                                    "advanced_keywords.first_name"
                                  )
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>
                            <div>
                              <label
                                htmlFor="linkedin-last-name"
                                className="block text-sm font-medium text-gray-700 mb-1"
                              >
                                Last Name
                              </label>
                              <input
                                type="text"
                                id="linkedin-last-name"
                                value={
                                  linkedinSearchParams.advanced_keywords
                                    .last_name
                                }
                                onChange={(e) =>
                                  handleLinkedinInputChange(
                                    e,
                                    "advanced_keywords.last_name"
                                  )
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label
                                htmlFor="linkedin-title"
                                className="block text-sm font-medium text-gray-700 mb-1"
                              >
                                Title
                              </label>
                              <input
                                type="text"
                                id="linkedin-title"
                                value={
                                  linkedinSearchParams.advanced_keywords.title
                                }
                                onChange={(e) =>
                                  handleLinkedinInputChange(
                                    e,
                                    "advanced_keywords.title"
                                  )
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                                placeholder="e.g. CEO, Software Engineer"
                              />
                            </div>
                            <div>
                              <label
                                htmlFor="linkedin-keyword-company"
                                className="block text-sm font-medium text-gray-700 mb-1"
                              >
                                Company Keyword
                              </label>
                              <input
                                type="text"
                                id="linkedin-keyword-company"
                                value={
                                  linkedinSearchParams.advanced_keywords.company
                                }
                                onChange={(e) =>
                                  handleLinkedinInputChange(
                                    e,
                                    "advanced_keywords.company"
                                  )
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                              />
                            </div>
                          </div>

                          <div>
                            <label
                              htmlFor="linkedin-keyword-school"
                              className="block text-sm font-medium text-gray-700 mb-1"
                            >
                              School Keyword
                            </label>
                            <input
                              type="text"
                              id="linkedin-keyword-school"
                              value={
                                linkedinSearchParams.advanced_keywords.school
                              }
                              onChange={(e) =>
                                handleLinkedinInputChange(
                                  e,
                                  "advanced_keywords.school"
                                )
                              }
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                            />
                          </div>
                        </div>
                      )}
                    </div> */}

                    <div className="mt-4">
                      <button
                        type="button"
                        onClick={searchLinkedinProfiles}
                        disabled={isSearching}
                        className="ml-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 flex items-center gap-2"
                      >
                        {isSearching ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Searching...</span>
                          </>
                        ) : (
                          <>
                            <Search size={16} />
                            <span>Search</span>
                          </>
                        )}
                      </button>
                    </div>

                    {/* Search Results */}
                    {linkedinProfiles.length > 0 && (
                      <div className="mt-6">
                        <h5 className="text-sm font-medium text-gray-700 mb-3">
                          Search Results ({linkedinProfiles.length})
                        </h5>

                        <div className="border border-gray-200 rounded-md divide-y divide-gray-200 max-h-96 overflow-y-auto">
                          {linkedinProfiles.map((profile, index) => (
                            <div
                              key={index}
                              className={`p-3 flex items-start gap-3 ${
                                selectedProfiles.includes(index)
                                  ? "bg-blue-50"
                                  : "hover:bg-gray-50"
                              }`}
                            >
                              <div className="flex-shrink-0">
                                <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                                  {profile.profile_picture_url ? (
                                    <img
                                      src={profile.profile_picture_url}
                                      alt={profile.first_name || "Profile"}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <User size={20} className="text-gray-500" />
                                  )}
                                </div>
                              </div>

                              <div className="flex-grow min-w-0">
                                <h6 className="font-medium text-gray-800 truncate">
                                  {profile.first_name || "Unknown Name"}
                                </h6>
                                {profile.headline && (
                                  <p className="text-sm text-gray-600 truncate">
                                    {profile.headline}
                                  </p>
                                )}
                                {profile.location && (
                                  <p className="text-xs text-gray-500">
                                    {profile.location}
                                  </p>
                                )}
                              </div>

                              <div className="flex-shrink-0">
                                <button
                                  type="button"
                                  onClick={() => toggleProfileSelection(index)}
                                  className={`p-1.5 rounded-full ${
                                    selectedProfiles.includes(index)
                                      ? "bg-blue-600 text-white"
                                      : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                                  }`}
                                >
                                  {selectedProfiles.includes(index) ? (
                                    <UserPlus size={16} />
                                  ) : (
                                    <UserPlus size={16} />
                                  )}
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="mt-4 flex items-center justify-between">
                          <p className="text-sm text-gray-600">
                            {selectedProfiles.length} profile(s) selected
                          </p>

                          <button
                            type="button"
                            onClick={generatePersonasFromProfiles}
                            disabled={
                              selectedProfiles.length === 0 || isAnalyzing
                            }
                            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:bg-gray-300 flex items-center gap-2"
                          >
                            {isAnalyzing ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                <span>Generating...</span>
                              </>
                            ) : (
                              <>
                                <UserPlus size={16} />
                                <span>Generate Personas from Selected</span>
                              </>
                            )}
                          </button>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="name"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Name
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Helps identify and reference your persona throughout
                          your research.
                        </p>
                      </div>
                      <div>
                        <label
                          htmlFor="age"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Age
                        </label>
                        <input
                          type="text"
                          id="age"
                          name="age"
                          value={formData.age}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Provides demographic context and helps understand
                          generational perspectives.
                        </p>
                      </div>
                    </div>
                    <div>
                      <label
                        htmlFor="occupation"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Occupation
                      </label>
                      <input
                        type="text"
                        id="occupation"
                        name="occupation"
                        value={formData.occupation}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Establishes professional context and potential technical
                        familiarity.
                      </p>
                    </div>
                    <div>
                      <label
                        htmlFor="background"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Background
                      </label>
                      <textarea
                        id="background"
                        name="background"
                        value={formData.background}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Provides insight into experiences, education, and life
                        circumstances that shape their needs.
                      </p>
                    </div>
                    <div>
                      <label
                        htmlFor="goals"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Goals
                      </label>
                      <textarea
                        id="goals"
                        name="goals"
                        value={formData.goals}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Identifies motivations and what the persona is trying to
                        achieve, helping align your solution with their needs.
                      </p>
                    </div>
                    <div>
                      <label
                        htmlFor="painPoints"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Pain Points
                      </label>
                      <textarea
                        id="painPoints"
                        name="painPoints"
                        value={formData.painPoints}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Reveals challenges and frustrations that your solution
                        can address, highlighting opportunities for improvement.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="gender"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Gender
                        </label>
                        <input
                          type="text"
                          id="gender"
                          name="gender"
                          value={formData.gender}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="maritalStatus"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Marital Status
                        </label>
                        <input
                          type="text"
                          id="maritalStatus"
                          name="maritalStatus"
                          value={formData.maritalStatus}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="income"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Income
                        </label>
                        <input
                          type="text"
                          id="income"
                          name="income"
                          value={formData.income}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="education"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Education
                        </label>
                        <input
                          type="text"
                          id="education"
                          name="education"
                          value={formData.education}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="racialGroup"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Racial Group
                        </label>
                        <input
                          type="text"
                          id="racialGroup"
                          name="racialGroup"
                          value={formData.racialGroup}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="location"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Location
                        </label>
                        <input
                          type="text"
                          id="location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="homeOwnership"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Home Ownership
                        </label>
                        <input
                          type="text"
                          id="homeOwnership"
                          name="homeOwnership"
                          value={formData.homeOwnership}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="vehiclesOwned"
                          className="block text-sm font-medium text-gray-700 mb-1"
                        >
                          Vehicles Owned
                        </label>
                        <input
                          type="text"
                          id="vehiclesOwned"
                          name="vehiclesOwned"
                          value={formData.vehiclesOwned}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="hasDrivingLicense"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Has Driving License
                      </label>
                      <select
                        id="hasDrivingLicense"
                        name="hasDrivingLicense"
                        value={formData.hasDrivingLicense}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                      >
                        <option value="">Select...</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                      </select>
                    </div>

                    <div className="mt-2">
                      <label
                        htmlFor="personalityTraits"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Personality Traits
                      </label>
                      <textarea
                        id="personalityTraits"
                        name="personalityTraits"
                        value={formData.personalityTraits}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                      />
                    </div>

                    <div className="mt-2">
                      <label
                        htmlFor="behaviors"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Behaviors
                      </label>
                      <textarea
                        id="behaviors"
                        name="behaviors"
                        value={formData.behaviors}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                      />
                    </div>
                  </>
                )}

                <div className="flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => {
                      resetForm();
                      setIsFormOpen(false);
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  {inputMethod === "file" &&
                  extractMultiple &&
                  multiplePersonasResults.length > 0 ? (
                    <button
                      type="button"
                      onClick={() => {
                        resetForm();
                        setIsFormOpen(false);
                      }}
                      className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                    >
                      Done
                    </button>
                  ) : (
                    <button
                      type="submit"
                      className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                      disabled={
                        (inputMethod === "file" &&
                          !analysisResult &&
                          multiplePersonasResults.length === 0) ||
                        isAnalyzing ||
                        isSearching ||
                        formData.name === ""
                      }
                    >
                      {editingPersona ? "Update Persona" : "Create Persona"}
                    </button>
                  )}
                </div>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PersonasComponent;
