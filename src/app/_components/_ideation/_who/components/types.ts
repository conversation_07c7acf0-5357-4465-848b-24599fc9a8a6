import {
  DisplayTrait,
  StateData,
  ValidationResponse,
  FinalSelectedPopulationTraits,
} from "@/lib/schemas";

export interface DemographicTraitsFormProps {
  onContinue: () => void;
  onBack: () => void;
  stateData: StateData;
  isUSA: boolean;
  selectedState: string | null;
  validationError: string | null;
  handleValidation: () => Promise<void>;
  handleGetAgeRange: (ageRange: number[]) => void;
  handleGetIncomeRange: (incomeRange: number[]) => void;
  getEduValue: (value: string, isActive: boolean) => void;
  getGenderValue: (value: string, isActive: boolean) => void;
  getChildrenValue: (value: string, isActive: boolean) => void;
  getRaceValue: (value: string, isActive: boolean) => void;
}

export interface DemographicTraitsValidationProps {
  onContinue: () => void;
  onBack: () => void;
  traits: any;
  validationResults: ValidationResponse | null;
  validationError: string | null;
  isLoading: boolean;
  question: string;
  handleFinalTraits: (finalData: FinalSelectedPopulationTraits) => void;
}

export interface SpecialistSelectionProps {
  onComplete: () => void;
  onBack: () => void;
  isUSA: boolean;
  question: string;
  displayTraits: DisplayTrait[];
  activeSpecialist: string;
  selectSpecialist: (title: string) => void;
  toggleSelectedTrait: (title: string) => void;
  addNewTrait: (title: string, values: string[]) => void;
  activateNewTrait: (title: string) => void;
  groupTraitsByCategory: (
    traits: DisplayTrait[]
  ) => Record<string, DisplayTrait[]>;
  getNumSelected: () => number;
  isLoading: boolean;
  errorMessage: string | null;
  loadingMessage: string;
}

export interface ValidationResultCardProps {
  result: {
    population_size: number;
    population_traits: Record<string, any>;
  };
  status: "original" | "alternative";
  populationSize: number;
  onAdjust: (data: FinalSelectedPopulationTraits) => void;
  isSelected?: boolean;
}

export interface PopulationSizeIndicatorProps {
  size: number;
  maxSize?: number;
  threshold?: number;
}

export interface RangeSelectorProps {
  title: string;
  range: number[];
  min: number;
  max: number;
  onChange: (range: number[]) => void;
  formatValue?: (value: number) => string;
}

export interface TraitSelectionGroupProps {
  title: string;
  options: string[];
  selectedValues: string[];
  onChange: (value: string, isActive: boolean) => void;
  multiple?: boolean;
}

export interface SpecialistGridProps {
  activeSpecialist: string;
  onSelectSpecialist: (title: string) => void;
}
