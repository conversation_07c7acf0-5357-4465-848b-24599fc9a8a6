import { useMemo } from "react";
import {
  ValidationResponse,
  FinalSelectedPopulationTraits,
} from "../../../../objects";

/**
 * Custom hook to compute the displayed data based on user selection
 *
 * @param data The validation response data
 * @param selectedOption The selected option (suggestion, original, or one_trait_change)
 * @param selectedTrait The selected trait (only used when selectedOption is one_trait_change)
 * @returns The computed population traits data to display
 */
export const useDisplayedData = (
  data: ValidationResponse,
  selectedOption: "suggestion" | "original" | "one_trait_change",
  selectedTrait: string | null
): FinalSelectedPopulationTraits => {
  return useMemo<FinalSelectedPopulationTraits>(() => {
    // Handle one_trait_change option
    if (
      selectedOption === "one_trait_change" &&
      selectedTrait &&
      data.one_trait_change
    ) {
      const traitData = data.one_trait_change[selectedTrait];

      if (!traitData) {
        return data.original; // Fallback to original if trait data is missing
      }

      // Create a copy of the original data as the base
      const baseData = { ...data.original };

      // Handle the specific structure of one_trait_change data
      return {
        population_size: traitData.population_size,
        population_traits: {
          ...baseData.population_traits,
          [selectedTrait]: traitData.population_traits,
        },
      };
    }

    // Return the selected dataset (suggestion or original)
    if (selectedOption === "suggestion") {
      return data.suggestion;
    } else if (selectedOption === "original") {
      return data.original;
    } else {
      // Default to original if no valid option is selected
      return data.original;
    }
  }, [data, selectedOption, selectedTrait]);
};
