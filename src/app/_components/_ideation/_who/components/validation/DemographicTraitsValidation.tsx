import React, { useEffect } from "react";
import { TriangleAlert } from "lucide-react";
import {
  ValidationResponse,
  FinalSelectedPopulationTraits,
} from "../../../objects";
import ValidationResultsView from "./ValidationResultsView";
import ErrorDisplay from "../../../../../_components/_ideation/ErrorDisplay";

interface DemographicTraitsValidationProps {
  onContinue: () => void;
  onBack: () => void;
  traits: any;
  validationResults: ValidationResponse | null;
  validationError: string | null;
  isLoading: boolean;
  question: string;
  handleFinalTraits: (finalData: FinalSelectedPopulationTraits) => void;
}

/**
 * Component for validating demographic traits and showing results
 * This is a wrapper around ValidationResultsView
 * Uses ValidationContext for state management
 */
const DemographicTraitsValidation: React.FC<
  DemographicTraitsValidationProps
> = ({
  validationResults,
  validationError,
  question,
  handleFinalTraits,
  isLoading,
}) => {
  // Debug info using useEffect
  useEffect(() => {}, [validationError, validationResults, isLoading]);

  return (
    <div>
      {/* Research question and title */}
      <div className="flex flex-col gap-2 items-stretch w-full mb-5">
        <div className="border-[#D0D5DD] border-2 rounded-lg py-3 text-center">
          <h3 className="font-semibold text-primary">Research Question</h3>
          <h3 className="text-text-placeholder font-roboto text-lg font-normal">
            {question}
          </h3>
        </div>
        <h2 className="text-text-dark font-roboto font-semibold text-2xl">
          Populations Validation Results
        </h2>
      </div>

      {/* Validation error message - only show if there are no validation results and not loading */}
      {validationError && !validationResults && !isLoading && (
        <ErrorDisplay message={validationError} className="w-full mb-4" />
      )}

      {/* Debug info - using useEffect to avoid React node issues */}

      {/* Validation results view - using the ValidationContext from parent */}
      <div className="w-full pb-4 bg-white border rounded-lg p-4 items-stretch border-primary">
        {validationResults && (
          <ValidationResultsView
            data={validationResults}
            onDataChange={handleFinalTraits}
          />
        )}
        {!validationResults && (
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <p className="text-gray-600">No validation results available</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DemographicTraitsValidation;
