import React, { useEffect } from "react";
import {
  ValidationResponse,
  FinalSelectedPopulationTraits,
} from "../../../objects";
import { useDisplayedData } from "./hooks/useDisplayedData";
import {
  OptionSelector,
  TraitSelector,
  PopulationDataDisplay,
  OneTraitChangeWarning,
} from "./components";
import { useValidation } from "../../contexts/ValidationContext";
import ErrorDisplay from "../../../../../_components/_ideation/ErrorDisplay";

interface ValidationResultsViewProps {
  data: ValidationResponse;
  onDataChange?: (finalTraits: FinalSelectedPopulationTraits) => void;
}

/**
 * Component for displaying validation results and allowing user to select population data
 */
export default function ValidationResultsView({
  data,
  onDataChange,
}: ValidationResultsViewProps): React.JSX.Element {
  // Get state and actions from ValidationContext
  const {
    selectedOption,
    selectedTrait,
    setSelectedOption,
    setSelectedTrait,
    handleFinalTraits,
  } = useValidation();

  // Use custom hook to compute displayed data
  const displayedData = useDisplayedData(data, selectedOption, selectedTrait);

  // Update parent component when displayed data changes
  useEffect(() => {
    if (displayedData) {
      // Use context handler first
      handleFinalTraits(displayedData);

      // Also call the prop handler if provided (for backward compatibility)
      if (onDataChange) {
        onDataChange(displayedData);
      }
    }
  }, [displayedData, handleFinalTraits, onDataChange]);

  // Force a trait selection when "one_trait_change" is selected but no trait is chosen
  useEffect(() => {
    if (
      selectedOption === "one_trait_change" &&
      !selectedTrait &&
      data.one_trait_change
    ) {
      const traitKeys = Object.keys(data.one_trait_change);
      if (traitKeys.length > 0) {
        const defaultTrait = traitKeys[0];
        setSelectedTrait(defaultTrait);
      }
    }
  }, [selectedOption, selectedTrait, data.one_trait_change, setSelectedTrait]);

  // If data is missing required properties, show an error
  if (!data.suggestion || !data.original) {
    return (
      <div className="flex flex-col gap-6 max-w-6xl mx-auto w-full">
        <ErrorDisplay
          message="The validation data is missing required properties. Please try again or contact support."
          className="w-full"
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 max-w-6xl mx-auto">
      {/* Option Selection Controls */}
      <OptionSelector
        data={data}
        selectedOption={selectedOption}
        onOptionChange={setSelectedOption}
      />

      {/* Trait Selector - Only show when one_trait_change is selected and has data */}
      {selectedOption === "one_trait_change" &&
        data.one_trait_change &&
        Object.keys(data.one_trait_change).length > 0 && (
          <TraitSelector
            data={data}
            selectedTrait={selectedTrait}
            onTraitChange={setSelectedTrait}
          />
        )}

      {/* Show message when one_trait_change is selected but has no data */}
      {selectedOption === "one_trait_change" &&
        (!data.one_trait_change ||
          Object.keys(data.one_trait_change).length === 0) && (
          <OneTraitChangeWarning />
        )}

      {/* Display Data */}
      <PopulationDataDisplay
        data={displayedData}
        selectedOption={selectedOption}
        highlightedTrait={selectedTrait}
      />
    </div>
  );
}
