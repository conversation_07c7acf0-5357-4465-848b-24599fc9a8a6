import React from "react";
import { Listbox } from "@headlessui/react";
import { ChevronDown } from "lucide-react";
import { ValidationResponse } from "../../../../objects";

interface TraitSelectorProps {
  data: ValidationResponse;
  selectedTrait: string | null;
  onTraitChange: (trait: string) => void;
}

/**
 * Component for selecting a trait to change
 */
export const TraitSelector: React.FC<TraitSelectorProps> = ({
  data,
  selectedTrait,
  onTraitChange,
}) => {
  // Format trait name for display
  const formatTraitName = (trait: string): string => {
    return trait.replace(/_/g, " ").replace(/^./, (m) => m.toUpperCase());
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium mb-1">
        Select One Trait to Change:
      </label>
      <Listbox value={selectedTrait} onChange={onTraitChange}>
        <div className="relative">
          <Listbox.Button className="w-full p-2 border border-gray-300 rounded focus:border-[#232353] focus:ring-2 focus:ring-[#4c4f99] flex justify-between items-center relative">
            <span>
              {selectedTrait
                ? formatTraitName(selectedTrait)
                : "-- Select a trait --"}
            </span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </Listbox.Button>
          <Listbox.Options className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded shadow-lg max-h-60 overflow-auto">
            <Listbox.Option key="" value="">
              {({ active }) => (
                <div
                  className={`cursor-pointer select-none p-2 text-center ${active ? "bg-gray-200" : ""}`}
                >
                  -- Select a trait --
                </div>
              )}
            </Listbox.Option>
            {Object.keys(data.one_trait_change).map((trait) => {
              const traitData = data.one_trait_change[trait];
              return (
                <Listbox.Option key={trait} value={trait}>
                  {({ active }) => (
                    <div
                      className={`cursor-pointer select-none p-2 ${active ? "bg-gray-200" : ""}`}
                    >
                      {formatTraitName(trait)}

                      <span className="text-xs text-gray-500 ml-2">
                        (Size: {traitData.population_size})
                      </span>
                    </div>
                  )}
                </Listbox.Option>
              );
            })}
          </Listbox.Options>
        </div>
      </Listbox>
    </div>
  );
};
