import React from "react";

interface ValidationErrorProps {
  error: string;
  data?: any;
}

/**
 * Component for displaying validation errors
 */
export const ValidationError: React.FC<ValidationErrorProps> = ({
  error,
  data,
}) => {
  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <h3 className="text-lg font-semibold text-red-800 mb-2">Data Error</h3>
      <p className="text-sm text-red-700">{error}</p>
      {data && (
        <pre className="mt-4 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
          {JSON.stringify(data, null, 2)}
        </pre>
      )}
    </div>
  );
};
