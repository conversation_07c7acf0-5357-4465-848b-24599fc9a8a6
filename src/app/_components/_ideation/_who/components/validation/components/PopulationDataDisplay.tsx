import React from "react";
import { FinalSelectedPopulationTraits } from "@/lib/schemas";

interface PopulationDataDisplayProps {
  data: FinalSelectedPopulationTraits;
  selectedOption: "suggestion" | "original" | "one_trait_change";
  highlightedTrait: string | null;
}

/**
 * Component for displaying population data
 */
export const PopulationDataDisplay: React.FC<PopulationDataDisplayProps> = ({
  data,
  selectedOption,
  highlightedTrait,
}) => {
  // Format trait name for display
  const formatTraitName = (key: string): string => {
    return key.replace(/_/g, " ").replace(/^./, (m) => m.toUpperCase());
  };

  return (
    <div className="p-4 border rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-3 flex items-center">
        <span>Population Size: {data.population_size}</span>
        {selectedOption === "suggestion" && (
          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
            Recommended
          </span>
        )}
      </h3>
      <ul className="mt-2 divide-y divide-gray-200">
        {Object.entries(data.population_traits).map(([key, value]) => (
          <li
            key={key}
            className={`py-3 ${
              highlightedTrait === key && selectedOption === "one_trait_change"
                ? "bg-yellow-50 -mx-4 px-4"
                : ""
            }`}
          >
            <div className="flex justify-between items-start">
              <strong>{formatTraitName(key)}:</strong>
              <div className="text-right max-w-[60%]">
                {Array.isArray(value) ? (
                  value.length === 2 &&
                  (key === "age" || key === "household_income") ? (
                    // Handle range values
                    <span>
                      {key === "household_income"
                        ? `$${value[0].toLocaleString()} - $${value[1].toLocaleString()}`
                        : `${value[0]} - ${value[1]}`}
                    </span>
                  ) : (
                    // Handle list values
                    <span className="flex flex-wrap justify-end gap-1">
                      {value.map((item, index) => (
                        <span
                          key={index}
                          className="bg-gray-100 px-2 py-1 rounded text-xs"
                        >
                          {item}
                        </span>
                      ))}
                    </span>
                  )
                ) : (
                  value
                )}
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};
