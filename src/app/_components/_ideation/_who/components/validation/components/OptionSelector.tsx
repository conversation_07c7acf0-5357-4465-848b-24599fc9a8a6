import React from "react";
import { ValidationResponse } from "../../../../objects";

interface OptionSelectorProps {
  data: ValidationResponse;
  selectedOption: "suggestion" | "original" | "one_trait_change";
  onOptionChange: (
    option: "suggestion" | "original" | "one_trait_change"
  ) => void;
}

/**
 * Component for selecting the population data option
 */
export const OptionSelector: React.FC<OptionSelectorProps> = ({
  data,
  selectedOption,
  onOptionChange,
}) => {
  const handleOptionChange = (
    option: "suggestion" | "original" | "one_trait_change"
  ) => {
    onOptionChange(option);
  };

  return (
    <div className="mb-4 flex flex-col gap-3">
      <h3 className="text-sm font-medium">Select population data to use:</h3>

      <div className="flex gap-4 flex-wrap">
        <label className="flex items-center cursor-pointer gap-2">
          <input
            type="checkbox"
            checked={selectedOption === "suggestion"}
            onChange={() => handleOptionChange("suggestion")}
            className="w-4 h-4 accent-[#232353]"
          />
          <span className="text-sm">
            Recommended (Population size: {data.suggestion.population_size})
          </span>
        </label>

        <label className="flex items-center cursor-pointer gap-2">
          <input
            type="checkbox"
            checked={selectedOption === "original"}
            onChange={() => handleOptionChange("original")}
            className="w-4 h-4 accent-[#232353]"
          />
          <span className="text-sm">
            Original (Population size: {data.original.population_size})
          </span>
        </label>

        {data.one_trait_change &&
          Object.keys(data.one_trait_change).length > 0 && (
            <label className="flex items-center cursor-pointer gap-2">
              <input
                type="checkbox"
                checked={selectedOption === "one_trait_change"}
                onChange={() => handleOptionChange("one_trait_change")}
                className="w-4 h-4 accent-[#232353]"
              />
              <span className="text-sm">One Trait Change</span>
            </label>
          )}
      </div>
    </div>
  );
};
