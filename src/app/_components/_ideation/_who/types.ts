export interface LinkedInProfile {
  object: string;
  provider: string;
  provider_id: string;
  public_identifier: string;
  member_urn: string;
  first_name: string;
  last_name: string;
  headline: string;
  summary?: string;
  primary_locale: Primarylocale;
  is_open_profile: boolean;
  is_premium: boolean;
  is_influencer: boolean;
  is_creator: boolean;
  is_relationship: boolean;
  network_distance: string;
  is_self: boolean;
  websites: any[];
  follower_count: number;
  connections_count: number;
  location: string;
  profile_picture_url: string;
  profile_picture_url_large: string;
  background_picture_url: string;
  education: Education[];
  work_experience: Workexperience[];
  skills: Skill[];
  languages: any[];
  certifications: any[];
  volunteering_experience: any[];
  projects: any[];
  recommendations: Recommendations;
  shared_connections_count: number;
  type: string;
  industry?: any;
  id: string;
  name: string;
  profile_url: string;
  public_profile_url: string;
  posts: Post[];
}

interface Recommendations {
  received: any[];
  given: any[];
}

interface Skill {
  name: string;
  endorsement_count: number;
  insights: any[];
  endorsement_id?: any;
  endorsed: boolean;
}

interface Workexperience {
  company: string;
  position: string;
  skills: any[];
  start: string;
  end?: string;
  company_id?: string;
  location?: string;
  description?: string;
}

interface Education {
  degree: string;
  school: string;
  start: string;
  end: string;
}

interface Primarylocale {
  country: string;
  language: string;
}

export interface Post {
  object: string;
  provider: string;
  social_id: string;
  share_url: string;
  date: string;
  parsed_datetime: string;
  comment_counter: number;
  impressions_counter: number;
  reaction_counter: number;
  repost_counter: number;
  permissions: Permissions;
  text: string;
  attachments: any[];
  author: Author;
  is_repost: boolean;
  id: string;
  repost_id: string;
  reposted_by: Repostedby;
  repost_content: Repostcontent;
}

export interface Repostcontent {
  id: string;
  date: string;
  parsed_datetime: string;
  text: string;
  author: Author2;
}

export interface Author2 {
  public_identifier: string;
  id?: any;
  name: string;
  is_company: boolean;
}

export interface Repostedby {
  public_identifier?: any;
  id?: any;
  name: string;
  is_company: boolean;
}

export interface Author {
  public_identifier: string;
  id: string;
  name: string;
  is_company: boolean;
  headline: string;
}

export interface Permissions {
  can_post_comments: boolean;
  can_react: boolean;
  can_share: boolean;
}
