"use client";
import React from "react";
import { ValidationProvider } from "./_who/contexts/ValidationContext";
import { DemographicTraitsProvider } from "./_who/contexts/DemographicTraitsContext";

interface IdeationContextWrapperProps {
  children: React.ReactNode;
  selectedState?: string | null;
}

/**
 * Context wrapper for the ideation page
 * Provides all the necessary context providers for the ideation flow
 */
export default function IdeationContextWrapper({
  children,
  selectedState = null,
}: IdeationContextWrapperProps) {
  return (
    <ValidationProvider>
      <DemographicTraitsProvider selectedState={selectedState}>
        {children}
      </DemographicTraitsProvider>
    </ValidationProvider>
  );
}
