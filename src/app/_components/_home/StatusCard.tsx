import Image from "next/image";

interface StatusCardProps {
  icon: string;
  iconAlt: string;
  statTitle: string;
  count: number;
}

const StatusCard = ({ icon, iconAlt, statTitle, count }: StatusCardProps) => {
  return (
    <div className="flex flex-row w-full gap-4 rounded-xl bg-white border border-card-border border-opacity-70 p-6">
      <Image src={icon} height={80} width={80} alt={iconAlt} />
      <div className="flex flex-col font-roboto text-text-dark">
        <p className="text-4xl font-medium">{count.toLocaleString("en-US")}</p>
        <p className="text-xl font-normal">{statTitle}</p>
      </div>
    </div>
  );
};

export default StatusCard;
