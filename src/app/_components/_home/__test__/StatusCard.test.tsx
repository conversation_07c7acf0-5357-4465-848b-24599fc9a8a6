import React from "react";
import { render } from "@testing-library/react";
import StatusCard from "../StatusCard";

describe("StatusCard", () => {
  const props = {
    icon: "/path/to/icon.png",
    iconAlt: "Icon Alt Text",
    statTitle: "Total Users",
    count: 1000,
  };

  it("renders with correct props", () => {
    const { getByText, getByAltText } = render(
      <StatusCard
        icon={props.icon}
        iconAlt={props.iconAlt}
        statTitle={props.statTitle}
        count={props.count}
      />
    );

    // Check if the stat title and count are rendered correctly
    expect(getByText("Total Users")).toBeInTheDocument();
    expect(getByText("1,000")).toBeInTheDocument(); // Updated to match formatted number

    // Check if the image alt text is correct
    const imageElement = getByAltText("Icon Alt Text") as HTMLImageElement;
    expect(imageElement).toBeInTheDocument();

    // Adjusted assertion to check for a substring in the image src
    expect(imageElement.src).toContain(
      "http://localhost/_next/image?url=%2Fpath%2Fto%2Ficon.png&w=256&q=75"
    );
  });
});
