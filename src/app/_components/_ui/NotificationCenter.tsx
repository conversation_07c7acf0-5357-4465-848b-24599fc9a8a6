import React from "react";
import {
  NovuProvider,
  PopoverNotificationCenter,
  NotificationBell,
} from "@novu/notification-center";
import { useUser } from "@auth0/nextjs-auth0/client";

export const Header = () => {
  const { user } = useUser();

  const email = user?.email;

  if (!email) {
    console.error("Email not available for Novu notifications.");
  }
  const appId = process.env.NEXT_PUBLIC_NOVU_APP_IDENTIFIER;
  if (!appId) {
    console.warn(
      "NEXT_PUBLIC_NOVU_APP_IDENTIFIER environment variable is not set."
    );
  }

  return (
    <NovuProvider
      subscriberId={email || ""}
      applicationIdentifier={appId || ""}
    >
      <PopoverNotificationCenter colorScheme={"light"}>
        {({ unseenCount }) => <NotificationBell unseenCount={unseenCount} />}
      </PopoverNotificationCenter>
    </NovuProvider>
  );
};
