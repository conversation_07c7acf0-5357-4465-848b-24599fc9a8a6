import { Skeleton } from "./Skeleton";

export function ChartSkeleton() {
  return (
    <div className="w-full p-4 flex flex-col items-center">
      <div className="flex justify-between items-center mb-4 w-full">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-6 w-20" />
      </div>

      <div className="h-60 w-full flex items-center justify-center">
        <Skeleton className="h-full w-full rounded-md" />
      </div>

      <div className="mt-4 space-y-2 w-full">
        <div className="flex justify-between items-center">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex justify-between items-center">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex justify-between items-center">
          <Skeleton className="h-4 w-28" />
          <Skeleton className="h-4 w-16" />
        </div>
      </div>
    </div>
  );
}
