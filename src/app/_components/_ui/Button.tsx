import React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "../_util/tailwindUtils";

// TODO: will need to set all characteristics of the button with designer
export const buttonVariants = cva(
  "rounded-lg gap-x-2 flex items-center justify-center text-base-300 font-semibold border-[1px] border-transparent transition-colors child:transition-colors capitalize disabled:cursor-not-allowed",
  {
    variants: {
      variant: {
        primary: [
          "bg-primary",
          "text-white",
          // "hover:bg-primary-focus",
          "active:bg-primary-content",
          "disabled:text-neutral-content",
          "disabled:bg-[#ECEDFB]",
        ],
        secondary: [
          "bg-secondary",
          "hover:bg-secondary-focus",
          // "active:bg-secondary-content",
          // "disabled:text-neutral-focus",
          // "disabled:bg-secondary-focus",
        ],
        // accent: [
        //   "bg-accent",
        //   "hover:bg-accent-focus",
        //   "active:bg-accent-content",
        //   "disabled:text-neutral-focus",
        //   "disabled:bg-accent-focus",
        // ],
        // neutral: [
        //   "bg-base-200",
        //   "hover:bg-base-100",
        //   "active:bg-neutral-content",
        //   "disabled:text-neutral-focus",
        //   "disabled:bg-neutral-content",
        // ],
        // error: [
        //   "bg-error-content",
        //   "hover:bg-error",
        //   "active:bg-error",
        //   "disabled:text-neutral-focus",
        //   "disabled:bg-error",
        // ],
        // link: [
        //   "text-neutral-focus",
        //   "hover:text-base-300",
        //   "disabled:text-neutral-content",
        //   "child:text-base-300",
        //   "hover:child:text-neutral-focus",
        // ],
        outline: [
          "bg-base-200",
          "text-dark",
          "border-card-border",
          // "hover:bg-primary-content",
          // "active:bg-primary",
          // "disabled:bg-base-100",
          // "disabled:text-neutral",
        ],
        ghost: ["hover:bg-accent", "hover:text-accent-foreground"],
      },
      // for now, we only have one size but if we dont have the size you need for the button,
      // add the appropriate attributes here
      size: {
        xs: ["text-[10px]", "py-[8px]", "px-[8px]"],
        sm: ["text-[14px]", "py-[8px]", "px-[16px]"],
        icon: "h-10 w-10",
        // md: ["text-[13px]", "py-3", "px-5"],
        // lg: ["text-base", "py-[14px]", "px-[22px]"],
        // xl: ["text-base", "py-4", "px-6"],
        // xxl: ["text-xl", "py-[18px]", "px-[26px]"],
      },
    },
    compoundVariants: [{ variant: "primary", size: "sm" }],
    defaultVariants: {
      variant: "primary",
      size: "sm",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

export default function Button({
  className,
  variant,
  size,
  children,
  ...props
}: ButtonProps) {
  return (
    <button
      type="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    >
      {children}
    </button>
  );
}
