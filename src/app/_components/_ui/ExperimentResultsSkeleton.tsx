import { Skeleton } from "./Skeleton";

export function ExperimentResultsSkeleton() {
  return (
    <div className="w-full">
      {/* Filter and Search */}
      <div className="mt-5 mb-4">
        <Skeleton className="h-10 w-full  rounded-md" />
      </div>

      {/* Experiment Cards */}
      {Array.from({ length: 5 }).map((_, index) => (
        <div
          key={index}
          className="flex relative p-6 flex-col gap-4 w-full text-dark bg-white border border-card-border rounded-xl font-roboto overflow-hidden mb-4"
        >
          <div className="flex justify-between items-center mb-4">
            <Skeleton className="h-6 w-64" />
            <Skeleton className="h-6 w-6 rounded-md" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="flex items-center gap-2">
              <Skeleton className="h-6 w-10 rounded-full" />
              <Skeleton className="h-5 w-16" />
            </div>

            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-5 w-32" />
            </div>

            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-5 w-32" />
            </div>

            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-5 w-32" />
            </div>

            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-5 w-32" />
            </div>

            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-5 w-32" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
