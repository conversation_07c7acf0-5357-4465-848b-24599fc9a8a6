import React, { useEffect, useState } from "react";

export type PaginationProps = {
  page: number;
  // eslint-disable-next-line no-unused-vars
  setPage: (page: number) => void;
  totalPage: number;
};

const Paginator = ({ page, setPage, totalPage }: PaginationProps) => {
  const [visiblePages, setVisiblePages] = useState<(number | string)[]>([]);

  useEffect(() => {
    calculateVisiblePages();
  }, [page, totalPage]);

  const calculateVisiblePages = () => {
    // If we have 7 or fewer pages, show all page numbers
    if (totalPage <= 7) {
      setVisiblePages(Array.from({ length: totalPage }, (_, i) => i + 1));
      return;
    }

    // For more than 7 pages, we need to show a compact version
    let newVisiblePages: (number | string)[] = [];

    // Always show first page
    newVisiblePages.push(1);

    // If current page is close to the start
    if (page <= 4) {
      newVisiblePages.push(2, 3, 4, 5);
      newVisiblePages.push("ellipsis");
      newVisiblePages.push(totalPage);
    }
    // If current page is close to the end
    else if (page >= totalPage - 3) {
      newVisiblePages.push("ellipsis");
      newVisiblePages.push(
        totalPage - 4,
        totalPage - 3,
        totalPage - 2,
        totalPage - 1,
        totalPage
      );
    }
    // If current page is in the middle
    else {
      newVisiblePages.push("ellipsis");
      newVisiblePages.push(page - 1, page, page + 1);
      newVisiblePages.push("ellipsis");
      newVisiblePages.push(totalPage);
    }

    setVisiblePages(newVisiblePages);
  };

  return (
    <div className="flex justify-center items-center gap-2">
      {/* First Button */}
      <button
        onMouseDown={() => setPage(1)}
        disabled={page === 1}
        className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white disabled:text-slate-400 disabled:hover:bg-white disabled:hover:text-slate-400 disabled:cursor-not-allowed"
      >
        First
      </button>

      {/* Previous Button */}
      <button
        onMouseDown={() => setPage(page - 1)}
        disabled={page === 1}
        className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white disabled:text-slate-400 disabled:hover:bg-white disabled:hover:text-slate-400 disabled:cursor-not-allowed"
      >
        Previous
      </button>

      {/* Page Numbers */}
      {visiblePages.map((pageNum, idx) => {
        if (pageNum === "ellipsis") {
          return (
            <span key={`ellipsis-${idx}`} className="px-3 py-1">
              ...
            </span>
          );
        }

        return (
          <button
            key={`page-${pageNum}`}
            onMouseDown={() => setPage(pageNum as number)}
            className={`px-3 py-1 border rounded ${
              pageNum === page
                ? "bg-primary-dark text-white"
                : "bg-white text-primary-dark hover:bg-primary hover:text-white"
            }`}
          >
            {pageNum}
          </button>
        );
      })}

      {/* Next Button */}
      <button
        onMouseDown={() => setPage(page + 1)}
        disabled={page === totalPage}
        className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white disabled:text-slate-400 disabled:hover:bg-white disabled:hover:text-slate-400 disabled:cursor-not-allowed"
      >
        Next
      </button>

      {/* Last Button */}
      <button
        onMouseDown={() => setPage(totalPage)}
        disabled={page === totalPage}
        className="px-3 py-1 border rounded bg-white text-primary-dark hover:bg-primary hover:text-white disabled:text-slate-400 disabled:hover:bg-white disabled:hover:text-slate-400 disabled:cursor-not-allowed"
      >
        Last
      </button>
    </div>
  );
};

export default Paginator;
