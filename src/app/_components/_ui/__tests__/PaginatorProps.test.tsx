import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../PaginatorProps";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";

describe("Pagination Components", () => {
  test("Pagination renders correctly", () => {
    render(<Pagination />);
    const pagination = screen.getByRole("navigation");
    expect(pagination).toBeInTheDocument();
  });

  test("PaginationContent renders correctly", () => {
    render(<PaginationContent />);
    const paginationContent = screen.getByRole("list");
    expect(paginationContent).toBeInTheDocument();
  });

  test("PaginationItem renders correctly", () => {
    render(<PaginationItem />);
    const paginationItem = screen.getByRole("listitem");
    expect(paginationItem).toBeInTheDocument();
  });
});
