import React from "react";
import { render, screen } from "@testing-library/react";
import Loading from "../Loading";

describe("Loading Component", () => {
  test("renders correctly", () => {
    render(<Loading />);

    const loadingElement = screen.getByText(/loading.../i);
    expect(loadingElement).toBeInTheDocument();

    const bouncingBalls = screen.getAllByRole("status");
    expect(bouncingBalls.length).toBe(3);
    bouncingBalls.forEach((ball) => {
      expect(ball).toHaveClass(
        "h-4",
        "w-4",
        "bg-primary",
        "rounded-full",
        "animate-bounce"
      );
    });
  });
});
