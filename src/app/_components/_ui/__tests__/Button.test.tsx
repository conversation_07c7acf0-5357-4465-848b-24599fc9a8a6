import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Button, { buttonVariants, ButtonProps } from "../Button";
import { cn } from "../../_util/tailwindUtils";

describe("Button Component", () => {
  const defaultText = "Click me";

  const renderButton = (props?: Partial<ButtonProps>) => {
    render(<Button {...props}>{defaultText}</Button>);
  };

  test("renders correctly with default props", () => {
    renderButton();
    const button = screen.getByRole("button", { name: defaultText });
    expect(button).toBeInTheDocument();
    // Get the generated class list
    const expectedClassList = cn(
      buttonVariants({ variant: "primary", size: "sm" })
    )
      .split(" ")
      .sort();
    const actualClassList = button.className.split(" ").sort();
    expect(actualClassList).toEqual(expectedClassList);
  });

  test("applies the correct styles based on variant", () => {
    renderButton({ variant: "secondary" });
    const button = screen.getByRole("button", { name: defaultText });
    expect(button).toHaveClass(
      buttonVariants({ variant: "secondary", size: "sm" })
    );
  });

  test("handles disabled state correctly", () => {
    renderButton({ disabled: true });
    const button = screen.getByRole("button", { name: defaultText });
    expect(button).toBeDisabled();
    expect(button).toHaveClass("disabled:cursor-not-allowed");
  });

  test("calls onClick handler when clicked", () => {
    const handleClick = jest.fn();
    renderButton({ onClick: handleClick });
    const button = screen.getByRole("button", { name: defaultText });
    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
