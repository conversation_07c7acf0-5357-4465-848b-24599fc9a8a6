import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Tab from "../Tab";

describe("Tab Component", () => {
  const tabButtons = [
    { name: "Text_Info", label: "Text Info" },
    { name: "Experiment_Design", label: "Experiment Design" },
    { name: "Attributes_Levels", label: "Attributes Levels" },
  ];

  const data = {
    causal_question: "What is the causal relationship?",
    characteristics: ["Characteristic 1", "Characteristic 2"],
    when: "Now",
    where: "Here",
  };

  const textInfo = {
    DOI: "123456",
    Title: "Sample Title",
    Author: "Sample Author",
  };

  const attributesLevels = [
    {
      attribute: "Attribute 1",
      levels: [["Level 1", "Level 2"]],
    },
    {
      attribute: "Attribute 2",
      levels: [["Level 1", "Level 2"]],
    },
  ];

  test("switches tab content correctly", () => {
    render(
      <Tab
        tabButtons={tabButtons}
        data={data}
        textInfo={textInfo}
        attributesLevels={attributesLevels}
      />
    );

    // Switch to the Experiment Design tab
    fireEvent.mouseDown(screen.getByText("Experiment Design"));

    // Ensure Experiment Design content is rendered
    expect(
      screen.getByRole("heading", { name: /causal question/i })
    ).toBeInTheDocument();

    // Switch to the Attributes Levels tab
    fireEvent.mouseDown(screen.getByText("Attributes Levels"));

    // Ensure Attributes Levels content is rendered
    expect(
      screen.getByRole("heading", { name: /attribute 1/i })
    ).toBeInTheDocument();
  });
});
