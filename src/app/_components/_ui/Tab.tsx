import React, { useState, useEffect } from "react";
import <PERSON> from "next/link";
import { LinkIcon } from "@heroicons/react/24/outline";

type TabProps = {
  tabButtons: any;
  data: any;
  textInfo: any;
  attributesLevels: any;
};

const Tab = ({ tabButtons, data, textInfo, attributesLevels }: TabProps) => {
  const [activeTab, setActiveTab] = useState<string>(tabButtons[2].name);
  const [expDes, setExpDes] = useState<any>([]);

  useEffect(() => {
    if (Object.keys(data).length > 0) {
      const experimentDesign = [
        {
          name: "Causal Question",
          value: data.causal_question ? data.causal_question : "",
        },
        {
          name: "Characteristics",
          value: data.characteristics.join(", ")
            ? data.characteristics.join(", ")
            : "",
        },
        {
          name: "When",
          value: data.when ? data.when : "",
        },
        {
          name: "Where",
          value: data.where ? data.where : "",
        },
      ];

      setExpDes(experimentDesign);
    }
  }, [data]);

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <div className="w-full p-6 text-dark bg-white border border-card-border rounded-xl font-roboto overflow-y-auto">
      <div className="flex gap-8 border-b-2 pb-3 mb-6">
        {tabButtons.map((tab: any) => (
          <button
            key={tab.name}
            className={activeTab === tab.name ? "text-dark" : "text-subtitle"}
            onMouseDown={() => handleTabClick(tab.name)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div className="relative overflow-y-auto h-96">
        {activeTab === "Text_Info" && (
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              {textInfo.DOI !== "" && (
                <>
                  <h2 className="text-subtitle">DOI: </h2>
                  <p>
                    {textInfo.DOI !== "" ? (
                      <Link
                        href={`http://dx.doi.org/${textInfo.DOI}`}
                        legacyBehavior
                      >
                        <a
                          target="_blank"
                          className="flex gap-2 items-center border-b border-transparent hover:border-primary-dark w-fit"
                        >
                          <span>{textInfo.DOI}</span>
                          <LinkIcon className="h-4 w-4" />
                        </a>
                      </Link>
                    ) : (
                      "None Available"
                    )}
                  </p>
                </>
              )}
              {Object.keys(textInfo).map((key) => {
                if (key !== "DOI" && textInfo[key] !== "") {
                  return (
                    <div className="flex flex-col gap-2" key={key}>
                      <h2 className="text-subtitle">{key} </h2>
                      <p>{textInfo[key]}</p>
                    </div>
                  );
                }
              })}
            </div>
          </div>
        )}
        {activeTab === "Experiment_Design" && (
          <div className="flex flex-col gap-4">
            {expDes.map(
              (item: any) =>
                item.value !== "" && (
                  <div className="flex flex-col gap-2" key={item.name}>
                    <h2 className="text-subtitle">{item.name} </h2>
                    <p>{item.value}</p>
                  </div>
                )
            )}
          </div>
        )}
        {activeTab === "Attributes_Levels" && (
          <div className="flex flex-col gap-4">
            {attributesLevels.map((item: any) => (
              <ul className="flex flex-col gap-2" key={item.attribute}>
                <h2 className="text-subtitle">{item.attribute} </h2>
                <ul>
                  {item.levels[0].map((level: string, idx: number) => {
                    return <p key={idx}>{level}</p>;
                  })}
                </ul>
              </ul>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Tab;
