import React from "react";

interface LoadingProps {
  fullScreen?: boolean;
}

const Loading: React.FC<LoadingProps> = ({ fullScreen = false }) => {
  return (
    <div
      className={`relative ${fullScreen ? "min-h-screen" : "min-h-[200px]"}`}
    >
      <div className="flex flex-row gap-4 items-center justify-center w-full h-full absolute inset-0">
        <div className="flex space-x-2 justify-center items-center">
          <span className="sr-only">Loading...</span>
          <div
            className="h-4 w-4 bg-primary rounded-full animate-bounce"
            style={{ animationDelay: "-0.3s" }}
            data-testid="ball-1"
            role="status"
          ></div>
          <div
            className="h-4 w-4 bg-primary rounded-full animate-bounce"
            style={{ animationDelay: "-0.15s" }}
            data-testid="ball-2"
            role="status"
          ></div>
          <div
            className="h-4 w-4 bg-primary rounded-full animate-bounce"
            data-testid="ball-3"
            role="status"
          ></div>
        </div>
      </div>
    </div>
  );
};

export default Loading;
