import { Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import Image from "next/image";

interface StatusModalProps {
  showModal: boolean;
  // eslint-disable-next-line no-unused-vars
  setShowModal: (showModal: boolean) => void;
}

const StatusModal = ({ showModal, setShowModal }: StatusModalProps) => {
  return (
    <Transition.Root show={showModal} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={setShowModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform w-[620px] overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all sm:my-8 sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-0"
                    onMouseDown={() => setShowModal(false)}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 flex flex-col gap-4 text-center sm:mt-0 sm:text-left">
                    <Dialog.Title
                      as="h3"
                      className="text-2xl font-semibold leading-9 text-[#101828] font-roboto"
                    >
                      Do you want to keep your experiment public?
                    </Dialog.Title>
                    <p className="text-lg text-[#101828] font-normal -mt-2">
                      Please upgrade your plan to run the experiment.
                    </p>
                    <div className="flex mt-2 justify-start items-end">
                      <Image
                        src="/world.svg"
                        height={16}
                        width={16}
                        layout="responsive"
                        alt="Country Marker"
                      />
                    </div>
                    <div className="flex flex-col gap-3 text-lg text-text-dark font-roboto font-normal">
                      <div className="flex flex-row gap-2 items-center">
                        By making your experiment public, you contribute to a
                        global understanding of human behavior. Every shared
                        insight is a step towards collective advancement.
                      </div>
                      <div className="flex flex-row gap-2 items-center">
                        You will be able to edit the privacy of your experiments
                        later in the Experiments page.
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6 w-full gap-4 flex flex-row items-center">
                  <button
                    type="button"
                    className="flex w-full border-[1px] justify-center rounded-md border-[#101828] text-[#101828] px-3 py-3 text-sm font-semibold"
                    onMouseDown={() => setShowModal(false)}
                  >
                    Make Private
                  </button>
                  <button
                    type="button"
                    className="flex w-full justify-center rounded-md bg-[#312E81] px-3 py-3 text-sm font-semibold text-white shadow-sm"
                    onMouseDown={() => setShowModal(false)}
                  >
                    Keep Public
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default StatusModal;
