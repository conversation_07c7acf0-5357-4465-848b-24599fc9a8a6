import { useEffect, useState } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./JupyterLab";

const Visualization = ({ runId }: { runId: string }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [htmlContent, setHtmlContent] = useState("");

  useEffect(() => {
    const getRunDetails = async (run_id: string) => {
      try {
        const response = await fetch(`/api/runs/${run_id}`, {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        });
        const data = await response.json();

        return data;
      } catch (error: any) {
        return { error: error.message };
      }
    };

    const getSurveyResultVisualisation = async (run_name: string) => {
      try {
        const file_Name = `experiment_survey_results_${run_name}`;
        const response = await fetch(
          `/api/survey-result-visualization?run_name=${file_Name}`,
          {
            method: "GET",
            headers: { "Content-Type": "text/html" },
          }
        );
        const data = await response.json();
        return data;
      } catch (error: any) {
        return { error: error.message };
      }
    };

    const fetchSurveyResult = async () => {
      const data = await getRunDetails(runId);
      if (data.run_details.run_name) {
        const artifactResponse = await getSurveyResultVisualisation(
          data.run_details.run_name
        );
        setHtmlContent(artifactResponse);
      }
    };
    fetchSurveyResult();
  }, [runId]);

  const openModalHandle = () => {
    setModalOpen(true);
  };
  const closeModalHandle = () => {
    setModalOpen(false);
  };
  return (
    <div>
      <div className="pb-2">Experiment Id: {runId}</div>
      <div className="flex gap-5">
        <button
          className="flex bg-primary font-medium hover:bg-[#504D9A] text-white font-inter text-sm px-4 py-2 rounded-md items-center justify-center"
          onClick={openModalHandle}
        >
          Visualize Dataset
        </button>
        <JupyterLab runId={runId}></JupyterLab>
      </div>
      <div className={`visualization-modal ${modalOpen ? "block" : "hidden"}`}>
        <div className="modal-container">
          <span className="close-modal" onClick={closeModalHandle}>
            Go back to experiments page
          </span>
          {!htmlContent && (
            <div className="loading-dataset">Loading Dataset..</div>
          )}
          {htmlContent && (
            <div
              className="visualization-wrapper"
              dangerouslySetInnerHTML={{ __html: htmlContent }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Visualization;
