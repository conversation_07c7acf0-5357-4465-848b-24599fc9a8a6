import React from "react";

interface AttributeImportance {
  attribute_text: string;
  importance: number;
}

interface ProgressBarProps {
  value: number;
  color?: string;
}

interface ProgressGroupProps {
  text: string;
  value: number;
  color?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value = 0,
  color = "aqua",
}) => {
  if (value < 0 || value > 100) {
    throw new Error("'value' should be in the range from 0 to 100.");
  }

  const barStyles = {
    backgroundColor: color,
    width: `${value}%`,
    height: "10px",
    transition: "width 0.6s ease",
  };

  return (
    <div
      style={{
        backgroundColor: "#F5F5F5",
        borderRadius: "2px",
        width: "100%",
        height: "10px",
      }}
    >
      <div style={barStyles}></div>
    </div>
  );
};

const ProgressGroup: React.FC<ProgressGroupProps> = ({
  text,
  value,
  color = "aqua",
}) => {
  return (
    <div style={{ marginBottom: "20px", width: "100%" }}>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr auto",
          alignItems: "center",
          marginBottom: "5px",
        }}
      >
        <span
          style={{
            justifySelf: "center",
            fontWeight: "bold",
          }}
        >
          {text}
        </span>
        <span
          style={{
            justifySelf: "right",
            fontWeight: "bold",
          }}
        >{`${Math.round(value * 100)}%`}</span>
      </div>

      <ProgressBar value={Math.round(value * 100)} color={color} />
    </div>
  );
};

const colors = ["#3C8DBC", "#F39C12", "#2FC0EF", "#DD4B39", "#1EA65A"];

const AttributeImportanceList: React.FC<{
  attributeImportance: AttributeImportance[];
}> = ({ attributeImportance }) => {
  return (
    <div style={{ maxWidth: "50%", padding: "20px", margin: "0 auto" }}>
      <h2
        className="text-base font-roboto font-semibold text-blue-600 mb-4"
        style={{ textAlign: "center" }}
      >
        Feature Importance
      </h2>
      {attributeImportance?.map((item, index) => (
        <ProgressGroup
          key={index}
          text={item.attribute_text}
          value={item.importance}
          color={colors[index % colors.length]}
        />
      ))}
    </div>
  );
};

export default AttributeImportanceList;
