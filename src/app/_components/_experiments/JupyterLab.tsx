import { useUser } from "@auth0/nextjs-auth0/client";
import { useEffect, useState } from "react";

const JupyterLab = ({ runId }: { runId: string }) => {
  const { user } = useUser();
  const email = user?.email || "";
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [runName, setRunName] = useState("");

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const response = await fetch("/api/token");
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setAccessToken(data.accessToken);
      } catch (error) {
        console.error("Failed to fetch access token:", error);
      }
    };

    fetchToken();
  }, []);

  useEffect(() => {
    const getRunDetails = async (run_id: string) => {
      try {
        const response = await fetch(`/api/runs/${run_id}`, {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        });
        const data = await response.json();
        return data;
      } catch (error: any) {
        return { error: error.message };
      }
    };

    const fetchSurveyResult = async () => {
      const data = await getRunDetails(runId);
      if (data.run_details.run_name) {
        setRunName(data.run_details.run_name);
      }
    };

    fetchSurveyResult();
  }, [runId]);

  const openJupyterLab = async () => {
    const environment = process.env.NEXT_PUBLIC_ENVIRONMENT || "development";
    const file_Name = `experiment_survey_results_${runName}`;

    try {
      const response = await fetch(
        `https://ipynb.dev.subconscious.ai:8443?file_name=${file_Name}&environment=${environment}&email=${email}`,
        {
          headers: {
            accept: "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (!response.ok) {
        console.error("Failed to open JupyterLab:", response.statusText);
      } else if (response.status === 200) {
        response.json().then((data) => {
          data.redirect_uri && window.open(data.redirect_uri, "_blank");
        });
      }
    } catch (error) {
      console.error("Error opening JupyterLab:", error);
    }
  };

  return (
    <div>
      <button
        className="flex bg-primary font-medium hover:bg-[#504D9A] text-white font-inter text-sm px-4 py-2 rounded-md items-center justify-center w-28"
        onClick={openJupyterLab}
      >
        JupyterLab
      </button>
    </div>
  );
};

export default JupyterLab;
