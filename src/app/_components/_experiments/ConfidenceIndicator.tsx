interface ConfidenceIndicatorProps {
  confidence: string;
}

interface ColorScheme {
  text: string;
  bg: string;
}

const COLOR_SCHEMES = {
  HIGH: {
    text: "#078B04",
    bg: "#E9FCE3",
  },
  REASONABLE: {
    text: "#EC9E07",
    bg: "#FFF9EF",
  },
  LOW: {
    text: "#BB0F04",
    bg: "#FFF5F1",
  },
};

const ConfidenceIndicator = ({ confidence }: ConfidenceIndicatorProps) => {
  let colors: ColorScheme = COLOR_SCHEMES.REASONABLE;
  if (confidence !== undefined) {
    if (confidence.toLowerCase() === "high") colors = COLOR_SCHEMES.HIGH;
    if (confidence.toLowerCase() === "low") colors = COLOR_SCHEMES.LOW;
  } else {
    confidence = "Reasonable";
  }

  return (
    <div
      className="flex flex-row items-start w-fit gap-2.5 py-0.5 px-2 rounded-md"
      style={{ backgroundColor: `${colors.bg}` }}
    >
      <p
        className="font-roboto font-medium text-xs"
        style={{ color: `${colors.text}` }}
      >
        {confidence}
      </p>
    </div>
  );
};

export default ConfidenceIndicator;
