/* eslint-disable react/jsx-key */
import { useState, useEffect } from "react";
import {
  IllusionBlank,
  Illusion1a,
  Illusion1b,
  Illusion1c,
  Illusion1d,
  Illusion2a,
  <PERSON>lusion2b,
  <PERSON>lusion2c,
  <PERSON>lusion2d,
  <PERSON>lusion3a,
  <PERSON>lusion3b,
  <PERSON>lusion3c,
  <PERSON>lusion3d,
  <PERSON><PERSON>4a,
  <PERSON>lusion4b,
  <PERSON>lusion4c,
  <PERSON>lusion5a,
  <PERSON>lusion5b,
  <PERSON>lusion5c,
  <PERSON>lusion5d,
  <PERSON>lusion6a,
  <PERSON>lusion6b,
  Illusion6c,
  Illusion6d,
} from "../../../../public/icons/IllusionOfLabor";

const illusions = [
  <IllusionBlank />,
  <Illusion1a />,
  <Illusion1b />,
  <Illusion1c />,
  <Illusion1d />,
  <IllusionBlank />,
  <Illusion2a />,
  <Illusion2b />,
  <Illusion2c />,
  <Illusion2d />,
  <IllusionBlank />,
  <Illusion3a />,
  <Illusion3b />,
  <Illusion3c />,
  <Illusion3d />,
  <IllusionBlank />,
  <Illusion4a />,
  <Illusion4b />,
  <Illusion4c />,
  <IllusionBlank />,
  <Illusion5a />,
  <Illusion5b />,
  <Illusion5c />,
  <Illusion5d />,
  <IllusionBlank />,
  <Illusion6a />,
  <Illusion6b />,
  <Illusion6c />,
  <Illusion6d />,
];

const illusionTexts = [
  "Crafting a friendly survey...",
  "Recruiting respondents for a chat...",
  "Determining market demand...",
  "Simulating market impact...",
  "Tailoring causal messaging...",
  "Calculating next best action...",
];

const IllusionLaborCard = () => {
  const [currentIllusion, setCurrentIllusion] = useState(0);
  const [currentText, setCurrentText] = useState(illusionTexts[0]);
  const [textIndex, setTextIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIllusion((prevIllusion) => {
        const nextIllusion = (prevIllusion + 1) % illusions.length;
        if (illusions[nextIllusion].type === IllusionBlank) {
          const nextTextIndex = (textIndex + 1) % illusionTexts.length;
          setCurrentText(illusionTexts[nextTextIndex]);
          setTextIndex(nextTextIndex);
        }
        return nextIllusion;
      });
    }, 1500);

    return () => clearInterval(interval);
  }, [textIndex]);

  return (
    <div className="w-[520px] h-[310px] rounded-xl bg-white flex flex-col justify-center items-center space-y-6">
      <div>{illusions[currentIllusion]}</div>
      <div>
        <div className="font-roboto font-semibold text-[24px] text-center">
          {" "}
          Running your experiment{" "}
        </div>
        <div>
          <p className="text-center">{currentText}</p>
        </div>
      </div>
    </div>
  );
};

export default IllusionLaborCard;
