// AmceChart.test.tsx
import React from "react";
import { render } from "@testing-library/react";
import { VegaLite } from "react-vega";
import <PERSON>ce<PERSON><PERSON> from "../AmceChart"; // Adjust the import path as necessary

jest.mock("react-vega", () => ({
  VegaLite: jest.fn(() => <div data-testid="vega-lite" />),
}));

describe("AmceChart", () => {
  it("renders VegaLite with the provided spec", () => {
    const spec = {
      description: "A simple bar chart with embedded data.",
      mark: "bar",
      encoding: {
        x: { field: "a", type: "ordinal" },
        y: { field: "b", type: "quantitative" },
      },
      data: { name: "table" }, // Note: data is embedded here as an example
    };
    const data = {
      table: [
        { a: "A", b: 28 },
        { a: "B", b: 55 },
        { a: "C", b: 43 },
        { a: "D", b: 91 },
        { a: "E", b: 81 },
        { a: "F", b: 53 },
        { a: "G", b: 19 },
        { a: "H", b: 87 },
      ],
    };

    const { getByTestId } = render(<AmceChart spec={spec} data={data} />);

    expect(getByTestId("vega-lite")).toBeInTheDocument();
    expect(VegaLite).toHaveBeenCalledWith(
      expect.objectContaining({ spec }),
      expect.any(Object)
    );
  });
});
