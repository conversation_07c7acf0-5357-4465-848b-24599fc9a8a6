import { render, screen, act } from "@testing-library/react";
import ShowcaseOverviewCard from "../ShowcaseOverviewCard";

const mockData = {
  title: "Test Title",
  hbID: "hb123",
  authors: "Author Name",
  DOI: "10.1234/example.doi",
  repID: "rep123",
  journal: "Test Journal",
  runID: "run123",
};

describe("ShowcaseOverviewCard", () => {
  it("renders card with title and authors", async () => {
    await act(async () => {
      render(<ShowcaseOverviewCard {...mockData} hb_folder={mockData.hbID} />);
    });

    await screen.findByText("Test Title");

    expect(screen.getByText("Test Title")).toBeInTheDocument();
    expect(screen.getByText("Author Name")).toBeInTheDocument();
    expect(screen.getByText("DOI 10.1234/example.doi")).toBeInTheDocument();
  });
});
