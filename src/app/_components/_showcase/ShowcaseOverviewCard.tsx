"use client";
import { useEffect, useMemo, useRef, useState } from "react";
import Link from "next/link";
import {
  ChevronUp,
  Newspaper,
  Link2,
  Book,
  Building2,
  Info,
  Sparkles,
} from "lucide-react";
import Loading from "../_ui/Loading";

import OverviewCard from "../_util/OverviewCard";
import * as Sentry from "@sentry/react";
import <PERSON>ce<PERSON><PERSON> from "../_util/AmceChart";
import CausalGraph from "../_graph/CausalGraph";

type amceHBObj = {
  attribute_text: string;
  transformed_coef: string;
  level_text: string;
  error: number;
  name: string;
  color: string;
  base: boolean;
  lowerBound: number;
  upperBound: number;
  level: string;
  coef: number;
  attribute: string;
};

interface ShowcaseOverviewCardProps {
  title: string;
  hb_folder: string;
  authors: string;
  DOI: string;
  repID: string;
  journal: string;
  runID: string;
}

type HBDataProps = {
  experiment_id: string;
  causal_question: string;
  when: string;
  where: string;
  characteristics: string[];
};

interface AMCEResult {
  attribute_id: string;
  attribute_text: string;
  level_ids: string;
  level_text: string;
  base: boolean;
  "Calculated significance": string;
  "Calculated AMCE": string | number;
  "Calculated std_error": string | number;
}

interface MeasureOfEffectivenessType {
  Coverage_Probability_for_Betas: number;
  Frobenius_similarity: number;
  Manhattan_similarity: number;
  Model: string;
  Number_of_Alternatives: number;
  Number_of_Parameters_Estimated: number;
  Opt_out_Included: boolean;
  Percentage_of_Parameters_with_same_Calculated_Reported_Sign: number;
  Spearman_Correlation_Beta_Values: number;
  Spearman_Correlation_Feature_Importance: number;
  Spectral_similarity: number;
  Standard_Deviation_Normalized_Mean_Absolute_Error_Beta_Values: number;
  Standard_Deviation_Normalized_Mean_Absolute_Error_Feature_Importance: number;
  sample_size?: number;
  tasks_per_respondent?: number;
  total_number_of_task?: number;
}
interface Replication {
  replication_name: string;
  llm_model: string;
  replication_id: string;
  amce_results: AMCEResult[];
  spearman_r: number;
  Measure_of_Effectiveness: MeasureOfEffectivenessType;
}

interface HBExperimentAMCE {
  level_text: string;
  group: string;
  varname: string;
  attribute_text: string;
  base: boolean;
  coef: number;
  err: number | null;
  p_value: number | null;
  "Calculated AMCE": string | number;
  "Calculated std_error": string | number;
  "Reported AMCE": string | number;
  "Reported std_error": string | number;
}

interface ProcessedDataPoint {
  name: string;
  level: string;
  coef: number;
  error: number;
  base: boolean;
  attribute: string;
  color: string;
}

type textInfoProps = [
  { "First Author": string | "" },
  { "Regression Model": string | "" },
  { "Type of task": string | "" },
  { "Type of experiment design": string | "" },
];

const buttonStyles =
  "flex bg-primary w-50 hover:bg-[#504D9A] text-white font-inter font-medium text-lg px-4 py-2 mx-2 rounded-xl items-center justify-center";

const ShowcaseOverviewCard = ({
  title,
  hb_folder,
  authors,
  DOI,
  repID,
  journal,
  runID,
}: ShowcaseOverviewCardProps) => {
  const [collapsed, setCollapsed] = useState<boolean>(true);
  const [data, setData] = useState<any>([]);
  const [textInfo, setTextInfo] = useState<textInfoProps>([
    { "First Author": "" },
    { "Regression Model": "" },
    { "Type of task": "" },
    { "Type of experiment design": "" },
  ]);
  const [replicateData, setReplicateData] = useState<any>({});
  const [paperData, setPaperData] = useState<any>({});
  const [specs, setSpecs] = useState<any>({});
  const [expDetail, setExpDetail] = useState<any>([]);
  const [causalData, setCausalData] = useState({});
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [llmModels, setLlmModels] = useState<{ id: string; name: string }[]>(
    []
  );

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const response = await fetch("/api/token");
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setAccessToken(data.accessToken);
      } catch (error) {
        console.error("Failed to fetch access token:", error);
        Sentry.captureException(error);
      }
    };

    fetchToken();
  }, []);

  const fetcher = async (uri: string) => {
    const startTime = Date.now();
    try {
      const response = await fetch(uri, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log error with contextual information
      Sentry.captureException(error, {
        tags: {
          api_endpoint: uri,
          status: "error",
        },
        extra: {
          response_time: `${duration}ms`,
          response_status: (error as any).response
            ? (error as any).response.status
            : "unknown",
          error_message: (error as any).message,
        },
      });

      throw error;
    }
  };

  // Fetching human baseline data and replication data from the API
  // so that we can configure them for Vega Chart.
  useEffect(() => {
    if (!collapsed) {
      fetcher(
        `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/human-baselines/${hb_folder}/replications-amces`
      )
        .then((data) => {
          if (data) {
            setData(data);
            const paper_Data =
              data.hb_experiment_definition.paper_data.slice(1);
            const configurePaperData: { [key: string]: string } = {};

            paper_Data.map((item: any) => {
              configurePaperData[item[0]] = item[1];
            });
            setPaperData(configurePaperData);

            const {
              experimentor_why_question_prompt,
              survey_year,
              survey_country,
              why_prompt,
              year,
              country,
              pre_cooked_attributes_and_levels_lookup,
              title,
            } = data.hb_experiment_definition;

            const whyPrompt = why_prompt ?? experimentor_why_question_prompt;
            const surveyYear = survey_year ?? year;
            const surveyCountry = survey_country ?? country;

            const population_traits = ["age", "education", "gender", "income"];
            // Ideation page has an object configured specifically for attributes and levels
            // so we need to configure the same object for the experiment when users want to
            // replicate the experiment as if it's built from the ideation page
            const configuredAttrLvls: any[] = [];

            if (pre_cooked_attributes_and_levels_lookup) {
              for (
                let i = 0;
                i < pre_cooked_attributes_and_levels_lookup.length;
                i++
              ) {
                const attrLevels: { [key: string]: string } = {};
                const currentAttribute: string =
                  pre_cooked_attributes_and_levels_lookup[i][0];
                const currentLevels =
                  pre_cooked_attributes_and_levels_lookup[i].slice(1);

                attrLevels["attribute"] = currentAttribute;
                attrLevels["levels"] = currentLevels;

                configuredAttrLvls.push(attrLevels);
              }
            }

            const configureLocation: { [key: string]: string } = {
              UK: "United Kingdom",
              "United States": "United States of America (USA)",
              "Montevideo, Uruguay": "Uruguay",
              "Tirana, Albania": "Albania",
            };

            const Repdata: HBDataProps = {
              experiment_id: title,
              causal_question: whyPrompt,
              when: surveyYear,
              // eslint-disable-next-line no-prototype-builtins
              where: configureLocation.hasOwnProperty(surveyCountry)
                ? configureLocation[surveyCountry]
                : surveyCountry,
              characteristics: population_traits.map(
                (trait: string) => trait[0].toUpperCase() + trait.slice(1)
              ),
            };

            setReplicateData(Repdata);

            // causal graph
            const responseData = data.hb_experiment_amce;

            const transformed = {
              id: "center_node",
              type: "center",
              attributes: [] as { name: string; levels: any }[],
            };
            const causalAttributes: { [key: string]: any[] } = {};
            responseData.forEach((item: any) => {
              if (item.attribute_text === "Intercept") return;
              const configuredItem = item.attribute_text;
              if (!causalAttributes[configuredItem]) {
                causalAttributes[configuredItem] = [];
              }

              causalAttributes[configuredItem].push({
                id: item.base ? `${item.level_text} (Base)` : item.level_text,
                value: parseFloat(item["Reported AMCE"]),
                p_value:
                  item["Reported std_error"] != 0
                    ? (
                        parseFloat(item["Reported AMCE"]) /
                        parseFloat(item["Reported std_error"])
                      ).toFixed(2)
                    : 0.0,
                std_error: item["Reported std_error"],
              });
            });

            Object.keys(causalAttributes).forEach((attrName) => {
              transformed.attributes.push({
                name: attrName,
                levels: causalAttributes[attrName],
              });
            });
            setCausalData(transformed);
          }
        })
        .catch((error) => {
          console.error("Error fetching data:", error);
        });
    }
  }, [collapsed, hb_folder, runID]);

  const COLORS: string[] = [
    "#81A0C2",
    "#AD7C59",
    "#B1F3B1",
    "#E7BCE7",
    "#62C9C3",
    "#DF9661",
    "#7DA7CA",
    "#e377c2",
    "#7f7f7f",
    "#bcbd22",
    "#17becf",
  ];

  const configureAmceData = (data: any) => {
    let configuredData: amceHBObj[] = [];

    data.map((item: any) => {
      const { attribute, coef, error, level, name, color, base } = item;

      configuredData.push({
        name: name,
        attribute_text: attribute,
        transformed_coef: parseFloat(coef).toFixed(2),
        level_text: base === true ? `${level}  (Base)` : `${level}`,
        error: error,
        color: color,
        lowerBound: parseFloat(
          Math.max(
            -1,
            parseFloat(coef) - 1.96 * Math.abs(parseFloat(error))
          ).toFixed(4)
        ),
        upperBound: parseFloat(
          Math.min(
            1,
            parseFloat(coef) + 1.96 * Math.abs(parseFloat(error))
          ).toFixed(4)
        ),
        base: base,
        level: level,
        coef: coef,
        attribute: attribute,
      });
    });

    return configuredData;
  };

  const processedData: ProcessedDataPoint[] = useMemo(() => {
    if (!data?.replications_amces || !data?.hb_experiment_amce) {
      return [];
    }

    const hbData: ProcessedDataPoint[] = data.hb_experiment_amce
      .filter((d: HBExperimentAMCE) => d.level_text !== "Intercept")
      .map(
        (d: HBExperimentAMCE): ProcessedDataPoint => ({
          name: "Human Baseline",
          level: d.level_text,
          coef:
            typeof d["Reported AMCE"] === "string"
              ? parseFloat(d["Reported AMCE"])
              : d["Reported AMCE"] || 0,
          error:
            typeof d["Reported std_error"] === "string"
              ? parseFloat(d["Reported std_error"])
              : d["Reported std_error"] || 0,
          base: d.base,
          attribute: d.attribute_text,
          color: COLORS[0],
        })
      );

    // Sort LLMs by Manhattan similarity descending
    const sortedReplications = [...data.replications_amces].sort(
      (a: Replication, b: Replication) =>
        b.Measure_of_Effectiveness.Manhattan_similarity -
        a.Measure_of_Effectiveness.Manhattan_similarity
    );

    const llmData: ProcessedDataPoint[] = [];
    sortedReplications.forEach((replication: Replication, index: number) => {
      const llmModel = replication.llm_model;
      const MeasureOfEffectiveness =
        replication.Measure_of_Effectiveness.Manhattan_similarity;
      const spearman_r =
        replication.Measure_of_Effectiveness.Spearman_Correlation_Beta_Values ??
        0.0;
      const models = data.replications_amces.map(
        (replication: Replication) => ({
          id: replication.replication_id,
          name: replication.llm_model.replace(/_/g, " "),
        })
      );
      setLlmModels(models);
      if (Array.isArray(replication.amce_results)) {
        replication.amce_results.forEach((result: AMCEResult) => {
          if (result.level_text !== "Intercept") {
            const coef =
              typeof result["Calculated AMCE"] === "string"
                ? parseFloat(result["Calculated AMCE"])
                : result["Calculated AMCE"] || 0;

            const error =
              typeof result["Calculated std_error"] === "string"
                ? parseFloat(result["Calculated std_error"])
                : result["Calculated std_error"] || 0;

            llmData.push({
              name: `${llmModel.replace(/_/g, " ")} (SpearmanR: ${spearman_r.toFixed(2)}, Human_AI Agreement Score: ${MeasureOfEffectiveness.toFixed(3)})`,
              level: result.level_text,
              coef: isNaN(coef) ? 0 : coef,
              error: isNaN(error) ? 0 : error,
              base: result.base,
              attribute: result.attribute_text,
              color: COLORS[(index + 1) % COLORS.length],
            });
          }
        });
      }
    });

    const combinedData = [...llmData, ...hbData];
    const configuredData = configureAmceData(combinedData);
    return configuredData;
  }, [data]);

  // Configure the text information for the tabs
  useEffect(() => {
    const text_information: textInfoProps = [
      { "First Author": paperData["first_author"] },
      { "Regression Model": paperData["regression_model"] },
      { "Type of task": paperData["type_of_task"] },
      {
        "Type of experiment design": paperData["type_of_experiment_design"],
      },
    ];

    setTextInfo(text_information);
  }, [paperData]);

  // we're having the Vega specs outside of component for dynamic uses
  useEffect(() => {
    if (data && processedData.length > 0) {
      const modelNames = Array.from(new Set(processedData.map((d) => d.name)));
      const configureSpec = {
        $schema: "https://vega.github.io/schema/vega-lite/v5.json",
        background: "white",
        width: 500,
        autosize: { type: "fit", contains: "padding" },
        title: {
          anchor: "middle",
          baseline: "top",
          offset: 20,
          orient: "top",
          subtitlePadding: 15,
          text: "Human LLMs Equivalence",
        },
        spacing: 10,
        facet: {
          row: {
            field: "attribute_text",
            type: "nominal",
            header: {
              labelAlign: "right",
              labelAnchor: "start",
              labelOrient: "top",
              labelPadding: 0,
              title: null,
            },
          },
        },
        transform: [
          {
            calculate:
              "if(indexof(datum.level_text, '(Base)') > -1, split(datum.level_text, ' (Base)')[0], datum.level_text)",
            as: "level_main",
          },
          {
            calculate:
              "if(indexof(datum.level_text, '(Base)') > -1, '(Base)', '')",
            as: "base_text",
          },
          {
            calculate:
              "length(datum.level_main) > 50 ? substring(datum.level_main, 0, 50) + '…' : datum.level_main",
            as: "level_main_truncated",
          },
          {
            filter:
              "datum.attribute_text != null && datum.attribute_text != ''",
          },
        ],
        data: { values: processedData },
        spec: {
          width: 350,
          layer: [
            {
              mark: {
                type: "text",
                fontWeight: "bold",
                baseline: "middle",
                dx: -205,
                fontSize: 14,
                color: "black",
              },
              encoding: {
                text: { field: "base_text" },
                y: { field: "level_main", type: "ordinal" },
                opacity: {
                  condition: {
                    test: "datum.name === 'Human Baseline'",
                    value: 1,
                  },
                  value: 0,
                },
              },
            },
            {
              mark: { type: "point" },
              encoding: {
                x: {
                  field: "transformed_coef",
                  type: "quantitative",
                  title:
                    "Left of red line weakens preference, right strengthens. Zero-crossing errors show no significant effect.",
                  axis: {
                    labelFontSize: 15,
                    titleFontSize: 15,
                    titlePadding: 20,
                  },
                  scale: { domain: [-1, 1] },
                },
                y: {
                  field: "level_main",
                  type: "ordinal",
                  axis: {
                    title: null,
                    labels: false,
                    domain: false,
                    labelFontSize: 15,
                  },
                },
                color: {
                  condition: {
                    test: "datum.base_text === '(Base)'",
                    value: "grey",
                  },
                  type: "nominal",
                  field: "name",
                  scale: { domain: modelNames },
                  legend: {
                    title: null,
                    sort: null,
                  },
                },
                tooltip: [
                  { field: "name", title: "Model" },
                  { field: "level_text", title: "Level" },
                  {
                    field: "transformed_coef",
                    title: "Coefficient",
                    format: ".2f",
                  },
                  { field: "lowerBound", title: "Lower Bound", format: ".2f" },
                  { field: "upperBound", title: "Upper Bound", format: ".2f" },
                  { field: "error", title: "Std Error", format: ".2f" },
                ],
              },
            },
            {
              mark: { type: "rule" },
              encoding: {
                x: { field: "lowerBound", type: "quantitative" },
                x2: { field: "upperBound" },
                y: { field: "level_main", type: "ordinal" },
                color: { field: "name", type: "nominal" },
              },
            },
            {
              mark: { type: "rule", color: "red", strokeDash: [2, 2] },
              encoding: { x: { datum: 0, type: "quantitative" } },
            },
            {
              mark: {
                type: "text",
                align: "right",
                dx: -60,
              },
              encoding: {
                y: { field: "level_main", type: "ordinal" },
                x: { value: 0 },
                text: { field: "level_main_truncated" },
                tooltip: { field: "level_main" },
                color: { value: "black" },
                size: { value: 15 },
                opacity: {
                  condition: {
                    test: "datum.name === 'Human Baseline'",
                    value: 1,
                  },
                  value: 0,
                },
              },
            },
          ],
        },
        resolve: { scale: { y: "independent" } },
        config: {
          font: "Arial",
          point: { size: 70 },
          legend: {
            orient: "right",
            offset: -160,
            padding: 0,
            labelLimit: 1000,
            sort: null,
          },
          title: {
            fontSize: 16,
            fontWeight: "bold",
            subtitleFont: "sans-serif",
            subtitleFontSize: 14,
            subtitlePadding: 15,
          },
          header: {
            labelFont: "sans-serif",
            labelFontSize: 15,
            labelFontWeight: "bold",
          },
        },
      };

      setSpecs(configureSpec);
    }
  }, [data, processedData]);

  // const handleDropdownClick = () => {
  //   if (dropdownVisible) {
  //     setDropdownVisible(false);
  //   } else {
  //     setDropdownVisible(true);
  //   }
  // };

  // const handleModelSelect = (id: string) => {
  //   setDropdownVisible(false);
  //   window.location.href = `/insights/${id}`;
  // };

  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     if (
  //       dropdownRef.current &&
  //       !dropdownRef.current.contains(event.target as Node)
  //     ) {
  //       setDropdownVisible(false);
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, []);

  useEffect(() => {
    if (Object.keys(replicateData).length > 0) {
      const experimentDesign = [
        {
          name: "Experiment ID",
          value: replicateData.experiment_id ? replicateData.experiment_id : "",
        },
        {
          name: "Question Prompt",
          value: replicateData.causal_question
            ? replicateData.causal_question
            : "",
        },
        {
          name: "Location and time",
          value:
            replicateData.when && replicateData.where
              ? `${replicateData.where} ${replicateData.when}`
              : "",
        },
      ];

      setExpDetail(experimentDesign);
    }
  }, [replicateData]);

  const tabDetails = [
    {
      tabContentName: "amce_graph",
      tabTitle: "Feature Level Importance",
      contentDetails: (
        <div className="flex justify-center items-center overflow-x-auto ">
          <AmceChart spec={specs} />
        </div>
      ),
    },
    {
      tabContentName: "causal_graph",
      tabTitle: "Causal Graph",
      contentDetails: causalData ? (
        <div className="relative border-4 border-white-500 w-full">
          <CausalGraph
            graphWidth="100%"
            graphHeight="100vh"
            data={causalData}
          />
        </div>
      ) : (
        <Loading />
      ),
    },
    {
      tabContentName: "experiment_details",
      tabTitle: "Experiment Details",
      contentDetails: (
        <div className="flex flex-col gap-2 pl-2">
          {expDetail.map(
            (item: any) =>
              item.value !== "" && (
                <div className="flex flex-col gap-2" key={item.name}>
                  <h2 className="text-subtitle">{item.name} </h2>
                  <p>{item.value}</p>
                </div>
              )
          )}
          {
            <div className="flex flex-col gap-4">
              <div className="flex flex-col gap-2">
                {textInfo.map((item: any, index: number) => {
                  const key = Object.keys(item)[0];
                  const value = item[key];

                  return (
                    <div key={key || index}>
                      {key !== "DOI" ? (
                        <div className="flex flex-col gap-2" key={key}>
                          <h2 className="text-subtitle">{key} </h2>
                          <p>{value}</p>
                        </div>
                      ) : (
                        value !== "" && (
                          <>
                            <h2 className="text-subtitle">DOI: </h2>
                            <p>
                              {value !== "" ? (
                                <Link
                                  href={`http://dx.doi.org/${value}`}
                                  legacyBehavior
                                >
                                  <a
                                    target="_blank"
                                    className="flex gap-2 items-center border-b border-transparent hover:border-primary-dark w-fit"
                                  >
                                    <span>{value}</span>
                                    <Link2 className="h-4 w-4" />
                                  </a>
                                </Link>
                              ) : (
                                "None Available"
                              )}
                            </p>
                          </>
                        )
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          }
        </div>
      ),
    },
  ];

  const subHeadingsDetail = (
    <>
      <div className="flex gap-2">
        <Newspaper className="h-5 w-5" />
        <span className="">{authors}</span>
      </div>
      <div className="flex gap-4">
        <div className="flex gap-2">
          <Link href={`http:dx.doi.org/${DOI}`} legacyBehavior>
            <a
              target="_blank"
              className="flex gap-2 items-center border-b border-transparent hover:border-primary-dark w-fit"
            >
              <Link2 className="h-4 w-4" />
              <span>DOI {DOI}</span>
            </a>
          </Link>
        </div>
        <div className="flex gap-2">
          <div className="flex items-center">
            <Book className="h-5 w-5" />
          </div>
          <p>{journal}</p>
        </div>
        {paperData && (
          <>
            <div className="flex gap-2">
              <div className="flex items-center">
                <Building2 className="h-5 w-5" />
              </div>
              <p>{paperData["research_domain"]}</p>
            </div>
            <div className="flex gap-2">
              <div className="flex items-center">
                <Info className="h-5 w-5" />
              </div>
              <p>{paperData["research_sub_domain"]}</p>
            </div>
          </>
        )}
      </div>
    </>
  );

  const cardDetailTop = (
    <div className="flex justify-between">
      <div
        className="flex flex-col gap-3 text-subtitle"
        onMouseDown={() => setCollapsed(!collapsed)}
      >
        <h1 className="font-medium text-xl text-primary-dark hover:cursor-pointer">
          {title}
        </h1>
        <>{subHeadingsDetail}</>
      </div>
      <div
        className="hover:cursor-pointer"
        onMouseDown={() => setCollapsed(!collapsed)}
      >
        <ChevronUp
          className={`h-6 w-6 text-chevron transition-all duration-300 ${
            collapsed ? "rotate-180" : "rotate-0"
          }`}
        />
      </div>
    </div>
  );

  const customActionDetails = [
    {
      customButton: (
        <div></div>
        // <a
        //   href={`/ideation/${hb_folder}`}
        //   onMouseDown={(e) => {
        //     // if (runID === "running") {
        //     e.preventDefault();
        //     // }
        //   }}
        // >
        // <div
        //   className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-xl border-card-border bg-white hover:bg-secondary-grey shadow-sm"
        //   style={{
        //     opacity: 0.5, // Make it visually appear disabled
        //     pointerEvents: "none", // Prevent pointer events completely
        //     // opacity: runID === "running" ? 0.5 : 1,
        //     // pointerEvents: runID === "running" ? "none" : "auto",
        //   }}
        // >
        //   <div className="flex flex-row gap-2 items-center w-fit">
        //     <p className="w-fit">Replicate Experiment</p>
        //   </div>
        // </div>
        // </a>
      ),
    },
    {
      customButton: (
        <div></div>
        // <div className="relative cursor-pointer " ref={dropdownRef}>
        //   <div
        //     className={buttonStyles}
        //     onClick={handleDropdownClick}
        //     style={{
        //       opacity: runID === "running" ? 0.5 : 1,
        //       pointerEvents: runID === "running" ? "none" : "auto",
        //     }}
        //   >
        //     <div className="flex flex-row gap-2 items-center w-fit">
        //       <Sparkles className="h-4 w-4" />
        //       <p className="w-fit">Market Simulator</p>
        //     </div>
        //   </div>
        //   {dropdownVisible && (
        //     <div className="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 ml-[14px] w-48">
        //       <ul className="max-h-60 overflow-y-auto p-2">
        //         {llmModels.map((model) => (
        //           <li
        //             key={model.id}
        //             className="cursor-pointer hover:bg-gray-100 p-2 rounded transition duration-150"
        //             onClick={() => handleModelSelect(model.id)}
        //           >
        //             {model.name}
        //           </li>
        //         ))}
        //       </ul>
        //     </div>
        //   )}
        // </div>
      ),
    },
  ];

  return (
    <OverviewCard
      tabDetails={tabDetails}
      loadingCondition={data.length === 0}
      collapsed={collapsed}
      cardDetailTop={cardDetailTop}
      customActionDetails={customActionDetails}
    />
  );
};

export default ShowcaseOverviewCard;
