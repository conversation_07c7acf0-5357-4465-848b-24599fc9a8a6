import React, { useState } from "react";
import Link from "next/link";
import {
  <PERSON><PERSON>,
  Eye,
  EyeOff,
  Key,
  Shield,
  ExternalLink,
  AlertCircle,
} from "lucide-react";

import * as Sentry from "@sentry/nextjs";

const ApiAccessComponent: React.FC = () => {
  const [apiToken, setApiToken] = useState<string>(""); // Store the API token
  const [isTokenVisible, setIsTokenVisible] = useState(false); // Toggle token visibility
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState("swagger");

  // Function to generate an API token
  const generateToken = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/token");
      if (!response.ok) {
        throw new Error(response.statusText);
      }
      const data = await response.json();
      setApiToken(data.accessToken);
    } catch (error) {
      console.error("Failed to generate token:", error);
      Sentry.captureException(error, {
        tags: {
          component: "ApiAccessComponent",
          action: "generateToken",
        },
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to regenerate the token
  const refreshToken = async () => {
    setIsTokenVisible(false);
    setApiToken("");
  };

  // Toggle token visibility
  const toggleTokenVisibility = () => {
    setIsTokenVisible((prev) => !prev);
  };

  // Copy the token to clipboard
  const copyToClipboard = () => {
    if (apiToken) {
      navigator.clipboard.writeText(apiToken);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000); // Clear success message after 2 seconds
    }
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-1 border-[#D0D5DD] border-2 bg-white rounded-lg ">
        <div className="flex flex-col p-3">
          <h2 className="font-semibold text-xl my-3">
            <div className="flex items-center gap-2">
              Get Your Subconscious.ai Access Token
              <Key className="w-5 h-5" />
            </div>
          </h2>
          <p className="font-normal text-base mx-2 my-1 flex items-center gap-1 flex-wrap">
            Use this token to securely interact with our API directly through
            <Link
              href="https://api.subconscious.ai/docs"
              target="_blank"
              className="text-blue-500 hover:text-blue-700 cursor-pointer inline-flex items-center gap-1"
            >
              Swagger UI
              <ExternalLink className="w-4 h-4" />
            </Link>
            or
            <Link
              href="https://docs.subconscious.ai/dashboard-api/"
              target="_blank"
              className="text-blue-500 hover:text-blue-700 cursor-pointer inline-flex items-center gap-1"
            >
              Fern
              <ExternalLink className="w-4 h-4" />
            </Link>
            .
          </p>

          <button
            onClick={generateToken}
            className="mx-2 my-2 px-3 py-2 bg-[#312E81] rounded-md text-white w-fit"
          >
            {loading ? "Getting Token..." : "Get Access Token"}
          </button>
        </div>

        {apiToken.length ? (
          <div className="space-y-2 w-[70%] mx-4">
            <h2 className="font-semibold text-lg my-3">Your Access Token</h2>
            <div className="flex items-center gap-2 p-3 bg-gray-100 rounded-lg">
              <input
                type={isTokenVisible ? "text" : "password"}
                value={apiToken}
                readOnly
                className="flex-1 bg-transparent border-none focus:outline-none font-mono"
              />
              <button
                onClick={toggleTokenVisibility}
                className="p-2 hover:bg-gray-200 rounded-md"
                aria-label={isTokenVisible ? "Hide token" : "Show token"}
              >
                {isTokenVisible ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
              <button
                onClick={copyToClipboard}
                className="p-2 hover:bg-gray-200 rounded-md"
                aria-label="Copy token"
              >
                <Copy className="w-5 h-5" />
              </button>
            </div>
            {copySuccess && (
              <div className="flex flex-col gap-2 p-2 bg-green-500 bg-opacity-40 rounded-lg">
                <div>Success!</div>
                <div>Token copied to clipboard.</div>
              </div>
            )}
          </div>
        ) : (
          ""
        )}
        {/* Guidelines Section */}
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 m-3">
          <h2 className="font-semibold mb-2 flex items-center gap-2">
            <Shield className="w-5 h-5 text-yellow-600" />
            Important Guidelines:
          </h2>
          <ul className="space-y-2 list-disc pl-5">
            <li>Keep this token secure and do not share it publicly</li>
            <li>Use this token in the Authorization: Bearer YOUR_TOKEN</li>
            <li>Regenerate your new Access token after a month</li>
          </ul>
        </div>
      </div>

      <div className="space-y-4 pt-3">
        <h2 className="text-xl font-semibold">How to use your Token</h2>
        <div className="border rounded-lg overflow-hidden">
          <div className="flex border-b">
            <button
              className={`flex-1 px-4 py-2 text-center ${
                activeTab === "swagger"
                  ? "bg-indigo-50 text-indigo-600"
                  : "hover:bg-gray-50"
              }`}
              onClick={() => setActiveTab("swagger")}
            >
              Swagger UI
            </button>
            <button
              className={`flex-1 px-4 py-2 text-center ${
                activeTab === "fern"
                  ? "bg-indigo-50 text-indigo-600"
                  : "hover:bg-gray-50"
              }`}
              onClick={() => setActiveTab("fern")}
            >
              Fern Documentation
            </button>
          </div>

          <div className="p-4">
            {activeTab === "swagger" ? (
              <div className="space-y-2">
                <ol className="list-decimal pl-5 space-y-2">
                  <li>Open the Swagger UI documentation</li>
                  <li>
                    Click Authorize (typically found at the top-right corner)
                  </li>
                  <li>Enter your token</li>
                  <li>Click Authorize to start making requests</li>
                </ol>

                <Link
                  href="https://api.subconscious.ai/docs"
                  target="_blank"
                  className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 mt-2"
                >
                  Open Swagger UI <ExternalLink className="w-4 h-4" />
                </Link>
              </div>
            ) : (
              <div className="space-y-2">
                <ol className="list-decimal pl-5 space-y-2">
                  <li>Open the Fern Documentation</li>
                  <li>Select the endpoint you want to call</li>
                  <li>Click the Play button (top-right corner)</li>
                  <li>
                    In the popup, locate the Login to send a real request
                    section and click on it
                  </li>
                  <li>Apply your access Token and save</li>
                  <li>Start sending requests to test the API interactively</li>
                </ol>
                <Link
                  href="https://docs.subconscious.ai/dashboard-api/"
                  target="_blank"
                  className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 mt-2"
                >
                  Open Fern Documentation <ExternalLink className="w-4 h-4" />
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiAccessComponent;
