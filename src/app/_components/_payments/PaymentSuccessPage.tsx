"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

export default function PaymentSuccess() {
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          //   Redirect to logout when countdown reaches 0
          window.location.href = `/api/auth/logout`;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full p-16">
        {/* Success Animation */}
        <div className="text-center mb-8">
          <div className="relative">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
            <Sparkles className="h-6 w-6 text-yellow-400 absolute -right-2 -top-2" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mt-4">
            Welcome to Premium!
          </h1>
          <p className="text-gray-600 mt-2 text-lg">
            Your payment was successful
          </p>
        </div>

        {/* Premium Features */}
        <div className="bg-indigo-50 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-2 mb-4">
            <Rocket className="h-5 w-5 text-indigo-600" />
            <h2 className="font-semibold text-[#312E81]">What&apos;s Next</h2>
          </div>
          <p className="text-[#312E81]">
            You&apos;ll have access to unlimited experiments and advanced
            features after logging back in.
          </p>
        </div>

        {/* Auto Logout Notice */}
        <div className="bg-amber-50 rounded-lg p-6 border border-amber-100">
          <div className="flex flex-col items-center gap-2">
            <div className="text-2xl font-semibold text-amber-900">
              {countdown}
            </div>
            <div className="text-center">
              <p className="text-amber-800 font-medium">
                Automatic logout in progress
              </p>
              <p className="text-sm text-amber-700 mt-1">
                You&apos;ll be redirected to login
              </p>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
          <div
            className="h-1 bg-amber-500 transition-all duration-1000"
            style={{ width: `${(countdown / 10) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}
