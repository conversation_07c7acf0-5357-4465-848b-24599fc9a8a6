"use client";

import React, { useEffect, useState } from "react";
import { AlertCircle, ArrowLeft, HelpCircle } from "lucide-react";
import { useUser } from "@auth0/nextjs-auth0/client";
import { loadStripe } from "@stripe/stripe-js";
import * as Sentry from "@sentry/react";
import { getUserData } from "@/app/actions/user";
export default function PaymentCancelled() {
  const { user } = useUser();
  const stripePromise = loadStripe(
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? ""
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stripeCustomerId, setStripeCustomerId] = useState<string | null>(null);

  // Fetch user data using server action
  useEffect(() => {
    async function fetchUserData() {
      if (user?.sub) {
        try {
          const userData = await getUserData(user.sub);
          if (
            userData?.app_metadata &&
            userData.app_metadata.stripe_customer_id
          ) {
            setStripeCustomerId(userData.app_metadata.stripe_customer_id);
          }
        } catch (error) {
          const errorMessage = `Error fetching user data: ${error instanceof Error ? error.message : "Unknown error"}`;
          console.error(errorMessage);
          Sentry.captureException(new Error(errorMessage), {
            extra: { user },
          });
        }
      }
    }

    fetchUserData();
  }, [user]);

  const handleTryAgain = async () => {
    setIsLoading(true);
    try {
      const stripe = await stripePromise;

      if (!stripeCustomerId) {
        setError("Please wait while we load your account information");
        return;
      }

      const response = await fetch(`/api/stripe`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ stripeCustomerId }),
      });

      if (!response.ok) {
        const errorMessage = await response.text();
        console.error("Error creating checkout session:", errorMessage);
        setError(errorMessage);
        return;
      }

      const data = await response.json();
      const { session_id } = data;

      if (stripe) {
        const result = await stripe.redirectToCheckout({
          sessionId: session_id,
        });
        if (result.error) {
          console.error("Error redirecting to checkout:", result.error.message);
          setError("Failed to redirect to checkout");
        }
      } else {
        setError("Payment system not initialized");
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      setError("Failed to start checkout process");
      Sentry.captureException(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleContactSupport = () => {
    window.open(
      "https://discord.com/channels/1041773620801851402/1223304377364906130",
      "_blank"
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <div className="p-8 space-y-6">
          {/* Header */}
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h1 className="text-2xl font-semibold text-gray-900">
              Payment Not Completed
            </h1>
            <p className="text-gray-600 mt-2">
              No worries! Your payment wasn&apos;t processed and you
              haven&apos;t been charged.
            </p>
          </div>

          {/* Error Message if any */}
          {error && (
            <div className="text-red-600 text-center text-sm bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Actions */}
          <div className="space-y-4">
            <button
              onClick={handleTryAgain}
              disabled={isLoading}
              className="w-full bg-[#312E81] text-white rounded-lg px-4 py-3 flex items-center justify-center gap-2 hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="h-5 w-5" />
              {isLoading ? "Processing..." : "Try Payment Again"}
            </button>

            <button
              onClick={handleContactSupport}
              className="w-full bg-white border border-gray-200 text-gray-700 rounded-lg px-4 py-3 flex items-center justify-center gap-2 hover:bg-gray-50 transition-colors"
            >
              <HelpCircle className="h-5 w-5" />
              Contact Support
            </button>
          </div>

          {/* Help Text */}
          <div className="text-center text-sm text-gray-600 border-t pt-6">
            Having trouble? Email us at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-indigo-600 hover:text-indigo-800 transition-colors"
            >
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
