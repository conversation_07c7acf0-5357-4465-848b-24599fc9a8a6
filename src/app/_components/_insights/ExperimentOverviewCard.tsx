// ExperimentOverviewCard.tsx

import React from "react";
import { ChevronRightIcon } from "@heroicons/react/24/outline";

interface ExperimentOverviewCardProps {
  question: string;
}

const ExperimentOverviewCard: React.FC<ExperimentOverviewCardProps> = ({
  question,
}) => {
  return (
    <div className="flex flex-row p-8 w-full bg-white border border-card-border/70 rounded-xl justify-between items-center">
      {/* INFO */}
      <div className="flex flex-col w-fit items-start gap-4">
        <div>
          <p className="text-text-dark font-roboto text-xl font-medium">
            {question}
          </p>
          <p className="text-text-placeholder font-inter text-lg font-normal">
            Insights go here
          </p>
        </div>
        <div>Label</div>
      </div>
      {/* ICON */}
      <ChevronRightIcon className="h-6 w-6" aria-label="chevron-right-icon" />
    </div>
  );
};

export default ExperimentOverviewCard;
