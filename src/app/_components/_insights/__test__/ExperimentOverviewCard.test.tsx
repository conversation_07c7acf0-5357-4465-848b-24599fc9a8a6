// ExperimentOverviewCard.test.tsx

import React from "react";
import { render, screen } from "@testing-library/react";
import ExperimentOverviewCard from "../ExperimentOverviewCard";

describe("ExperimentOverviewCard Component", () => {
  it("should render the question text", () => {
    const questionText = "What is the impact of the new feature?";
    render(<ExperimentOverviewCard question={questionText} />);
    expect(screen.getByText(questionText)).toBeInTheDocument();
  });

  it("should render the insights placeholder text", () => {
    render(<ExperimentOverviewCard question="Sample question" />);
    expect(screen.getByText("Insights go here")).toBeInTheDocument();
  });

  it("should render the label text", () => {
    render(<ExperimentOverviewCard question="Sample question" />);
    expect(screen.getByText("Label")).toBeInTheDocument();
  });

  it("should render the ChevronRightIcon", () => {
    render(<ExperimentOverviewCard question="Sample question" />);
    const icon = screen.getByLabelText("chevron-right-icon");
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass("h-6 w-6");
  });
});
