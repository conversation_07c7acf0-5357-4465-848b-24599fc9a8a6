interface DetailedRun {
  run_details: {
    run_name: string;
    configs: {
      artifacts: [];
    };
  };
}

interface LinkType {
  data?: { [key: string]: string };
  error?: string;
}

interface ExperimentMappingResults {
  experiment_mappings: string;
}

interface ExperimentSurveyResults {
  experiment_survey_results: string;
}

interface ExperimentAnalyticsResults {
  experiment_analytics_results: string;
}

export type {
  LinkType,
  DetailedRun,
  ExperimentMappingResults,
  ExperimentSurveyResults,
  ExperimentAnalyticsResults,
};
