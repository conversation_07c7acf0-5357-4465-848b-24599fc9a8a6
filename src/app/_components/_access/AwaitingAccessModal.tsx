import { Fragment } from "react";
import Image from "next/image";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface AwaitingAccessModalProps {
  showModal: boolean;
  // eslint-disable-next-line no-unused-vars
  setShowModal: (showModal: boolean) => void;
}

const AwaitingAccessModal = ({
  showModal,
  setShowModal,
}: AwaitingAccessModalProps) => {
  return (
    <Transition.Root show={showModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10"
        onClose={() => setShowModal(false)}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform w-[620px] overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all sm:my-8 sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-0"
                    onMouseDown={() => setShowModal(false)}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 flex flex-col gap-4 text-center sm:mt-0 sm:text-left">
                    <Dialog.Title
                      as="h3"
                      className="text-2xl font-semibold leading-9 text-text-dark font-roboto"
                    >
                      Awaiting access
                    </Dialog.Title>
                    <div className="flex flex-col gap-3 mt-2 justify-center items-start text-lg text-text-dark font-roboto font-normal">
                      <p>
                        Thank you for your interest in running experiments with
                        our platform.
                      </p>
                      <p>
                        Currently, your account does not yet have permission to
                        run experiments.
                      </p>
                      <p>
                        Your patience is appreciated - our team will reach out
                        to you.
                      </p>
                      <p>
                        In the meantime, we encourage you to explore the results
                        of our replicated experiments or follow us on LinkedIn
                        to stay up to date with our rollout.
                      </p>
                      <p>We are excited to work with you soon!</p>
                    </div>
                  </div>
                </div>
                <div className="mt-6 w-full flex flex-row items-center gap-3">
                  <div className="w-full">
                    <a
                      type="button"
                      className="flex w-full justify-center font-roboto rounded-lg bg-white px-6 py-3 text-base font-medium text-text-dark ring-1 ring-inset ring-card-border hover:bg-gray-50"
                      href="https://www.linkedin.com/company/subconscious-ai"
                      target="_blank"
                    >
                      <div className="flex flex-row gap-2 justify-center items-center align-middle">
                        <Image
                          src="/icons/linkedin_mark.svg"
                          height={24}
                          width={24}
                          alt="LinkedIn Logo"
                        />
                        <p>Visit our LinkedIn page</p>
                      </div>
                    </a>
                  </div>
                  <div className="w-full">
                    <a
                      type="button"
                      className="flex w-full justify-center font-roboto rounded-lg bg-primary px-2 py-3 text-base font-medium text-white"
                      href="https://app.gitbook.com/o/jadOkacqKTkHvAmKDCti/s/Hz13MXZLCbJ7iCAVovhQ/product/experimental-results-showcase"
                      target="_blank"
                    >
                      View results showcase
                    </a>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default AwaitingAccessModal;
