import Image from "next/image";

interface AwaitingAccessBannerProps {
  showBanner: boolean;
}

const AwaitingAccessBanner = ({ showBanner }: AwaitingAccessBannerProps) => {
  return (
    <div
      className={`flex flex-col gap-5 ${showBanner ? "block" : "hidden"} bg-[#FDFAFA] rounded-xl p-10 border border-error-red border-opacity-70`}
    >
      <div className="flex flex-col gap-2.5 w-full text-text-dark font-roboto text-lg font-normal">
        <p>
          Currently, your account is on the Free Tier and you have access to run
          1 free experiment.
        </p>
        <p>
          We encourage you to explore the results of our replicated experiments
          or follow us on LinkedIn to stay up to date with our rollout.
        </p>
        <p>We are excited to work with you soon!</p>
      </div>
      <div className="flex flex-row items-start gap-3">
        {/* BUTTONS */}
        <a
          type="button"
          className="flex w-[300px] justify-center font-roboto rounded-lg bg-white px-6 py-3 text-base font-medium text-text-dark ring-1 ring-inset ring-card-border hover:bg-gray-50"
          href="https://www.linkedin.com/company/subconscious-ai"
          target="_blank"
        >
          <div className="flex flex-row gap-2 justify-center items-center align-middle">
            <Image
              src="/icons/linkedin_mark.svg"
              height={24}
              width={24}
              alt="LinkedIn Logo"
            />
            <p>Visit our LinkedIn page</p>
          </div>
        </a>
        <a
          type="button"
          className="flex w-[300px] justify-center font-roboto rounded-lg bg-primary px-2 py-3 text-base font-medium text-white"
          href="https://app.gitbook.com/o/jadOkacqKTkHvAmKDCti/s/Hz13MXZLCbJ7iCAVovhQ/product/experimental-results-showcase"
          target="_blank"
        >
          View results showcase
        </a>
      </div>
    </div>
  );
};

export default AwaitingAccessBanner;
