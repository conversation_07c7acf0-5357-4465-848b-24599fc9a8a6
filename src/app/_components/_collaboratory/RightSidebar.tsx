"use client";

import { useEffect, useState } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline"; // Import proper icons
import { doc, onSnapshot } from "firebase/firestore";
import { db } from "@/app/lib/firebase";

interface Collaborator {
  email: string;
  nickname: string;
  role: string;
}

const RightSidebar = ({ experimentId }: { experimentId: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);

  useEffect(() => {
    const docRef = doc(db, "collaboratories", experimentId);

    // Set up a real-time listener for the document
    const unsubscribe = onSnapshot(
      docRef,
      (docSnap) => {
        if (docSnap.exists()) {
          const data = docSnap.data();
          setCollaborators(data.collaborators || []);
        }
      },
      (error) => {
        console.error("Error fetching collaborators:", error);
      }
    );

    // Cleanup the listener when the component is unmounted
    return () => unsubscribe();
  }, [experimentId]);

  return (
    <div className="relative">
      {/* Sidebar */}
      <div
        className={`fixed top-3 right-0 h-full bg-[#1c1d47] shadow-lg p-4 transition-transform duration-300 ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ width: "300px" }}
      >
        <h2 className="text-lg font-bold text-white mb-4">Collaborators</h2>
        <ul className="space-y-3">
          {collaborators.map((collaborator, index) => (
            <li
              key={index}
              className="flex items-center space-x-3 border p-2 rounded-md shadow-sm"
            >
              {/* User Initials */}
              <div className="w-8 h-8 flex items-center justify-center bg-blue-500 text-white rounded-full">
                {collaborator.nickname.charAt(0).toUpperCase()}
              </div>
              {/* User Details */}
              <div>
                <p className="text-sm text-white font-medium">
                  {collaborator.nickname}
                </p>
                <p className="text-xs text-gray-300">{collaborator.email}</p>
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`fixed top-1/2 -translate-y-1/2 shadow transition-all duration-300 p-[6px] z-10 border border-[#667085] rounded-lg -mr-3 w-7 h-7 bg-[#1C1D47] cursor-pointer flex items-center justify-center ${
          isOpen ? "right-[315px]" : "right-4"
        }`}
      >
        {isOpen ? (
          <ChevronRightIcon className="h-6 w-6 text-white" />
        ) : (
          <ChevronLeftIcon className="h-6 w-6 text-white" />
        )}
      </button>
    </div>
  );
};

export default RightSidebar;
