import { useThreads } from "@liveblocks/react/suspense";
import {
  AnchoredThreads,
  FloatingComposer,
  FloatingThreads,
} from "@liveblocks/react-tiptap";
import { Editor } from "@tiptap/react";
import { usePendoTrack } from "@/hooks/usePendoTrack";
import { useEffect } from "react";

export function Threads({ editor }: { editor: Editor | null }) {
  const { threads } = useThreads({ query: { resolved: false } });
  const { trackFeatureUsage } = usePendoTrack();

  useEffect(() => {
    // Track when threads are loaded/viewed
    if (threads.length > 0) {
      trackFeatureUsage("Collab", "viewed", {
        thread_count: threads.length,
      });
    }
  }, [threads, trackFeatureUsage]);

  return (
    <>
      <div className="anchored-threads">
        <AnchoredThreads editor={editor} threads={threads} />
      </div>
      <FloatingThreads
        editor={editor}
        threads={threads}
        className="floating-threads"
      />
      <FloatingComposer
        editor={editor}
        className="floating-composer"
        onComposerSubmit={() => {
          // Track when a user creates a new comment
          trackFeatureUsage("Collab", "message_sent", {
            action: "new_comment",
          });
        }}
      />
    </>
  );
}
