"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { deleteDoc, doc } from "firebase/firestore";
import { db } from "@/app/lib/firebase";
import { useState } from "react";

interface Experiment {
  id: string;
  name: string;
}

interface CollaboratoryOverviewCardProps {
  experiment: Experiment;
  // eslint-disable-next-line no-unused-vars
  onDelete: (id: string) => void;
}

const CollaboratoryOverviewCard = ({
  experiment,
  onDelete,
}: CollaboratoryOverviewCardProps) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleCardClick = () => {
    router.push(`/collaboratory/${experiment.id}`);
  };

  const handleDeleteClick = async (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click
    setIsDeleting(true);

    try {
      // Delete from Firebase
      const docRef = doc(db, "collaboratories", experiment.id);
      await deleteDoc(docRef);

      // TODO: Add code to delete from Liveblocks (if required)

      onDelete(experiment.id);
    } catch (error) {
      console.error("Error deleting collaboratory:", error);
      alert("Failed to delete collaboratory. Please try again.");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div
      className="bg-white shadow-md rounded-xl p-6 cursor-pointer relative flex justify-between items-center"
      onClick={handleCardClick}
    >
      <div className="flex flex-col flex-grow">
        <h2 className="text-lg font-bold flex-grow">{experiment.name}</h2>
        <p className="text-sm text-gray-600">Experiment ID: {experiment.id}</p>
      </div>
      <div className="flex">
        <button
          className=" text-red-500 hover:text-red-700"
          onClick={handleDeleteClick}
          disabled={isDeleting}
        >
          {isDeleting ? "Deleting..." : "Delete"}
        </button>
      </div>
    </div>
  );
};

export default CollaboratoryOverviewCard;
