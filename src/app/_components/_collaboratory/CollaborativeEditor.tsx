"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  useLiveblocksExtension,
  FloatingComposer,
} from "@liveblocks/react-tiptap";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Download, RefreshCw } from "lucide-react";
import jsPDF from "jspdf";

import { Threads } from "./Threads";
import { CharacterCount } from "@tiptap/extension-character-count";
import ImageResize from "tiptap-extension-resize-image";
import Highlight from "@tiptap/extension-highlight";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import { Toolbar } from "./Toolbar";
import { TextAlign } from "@tiptap/extension-text-align";
import { Typography } from "@tiptap/extension-typography";

interface CollaborativeEditorProps {
  experimentId: string;
  artifactDetails: Record<string, any>;
  isReadOnly: boolean;
  artifactContent: string | null;
  runName?: string;
}

const formatArtifactDetails = (
  artifactDetails: Record<string, any>
): string => {
  if (!artifactDetails) return "<p>No artifact details available.</p>";

  const { AMCE_Results, Attribute_Importance, R_Squared } = artifactDetails;

  const amceResults = AMCE_Results?.map(
    (result: any) => `
    <div style="margin-bottom: 10px;">
      <h4 style="margin: 0;">Attribute: ${result.attribute_text}</h4>
      <ul>
        <li><strong>Level:</strong> ${result.level_text}</li>
        <li><strong>AMCE:</strong> ${result.AMCE}</li>
        <li><strong>Significance:</strong> ${result.significance}</li>
        <li><strong>P-Value:</strong> ${result.p_value}</li>
      </ul>
    </div>
  `
  ).join("");

  const attributeImportance = Attribute_Importance?.map(
    (attr: any, index: number) => `
      <div style="margin-bottom: 10px;">
        <h4 style="margin: 0;">${index + 1}. ${attr.attribute_text}</h4>
        <p><strong>Importance:</strong> ${attr.importance}</p>
      </div>
    `
  ).join("");

  const rSquared = `
    <div>
      <h4>R-Squared:</h4>
      <p>${R_Squared?.[0] || "N/A"}</p>
    </div>
  `;

  return `
    <div>
      <h2 style="text-align: center;">Artifact Details</h2>
      <section style="margin-top: 20px;">
        <h3>AMCE Results</h3>
        ${amceResults || "<p>No AMCE Results available.</p>"}
      </section>
      <section style="margin-top: 20px;">
        <h3>Attribute Importance</h3>
        ${attributeImportance || "<p>No Attribute Importance data available.</p>"}
      </section>
      <section style="margin-top: 20px;">
        ${rSquared}
      </section>
    </div>
  `;
};

const CollaborativeEditor: React.FC<CollaborativeEditorProps> = ({
  artifactDetails,
  isReadOnly,
  runName,
  artifactContent,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [contentProcessed, setContentProcessed] = useState(false);
  const editorInitialized = useRef(false);
  const contentRef = useRef(artifactContent);
  const loadingTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Store the latest artifactContent in a ref to avoid race conditions
  useEffect(() => {
    contentRef.current = artifactContent;
  }, [artifactContent]);

  const processReportContent = (
    content: string | null
  ): { displayContent: string; images: string[]; hasError: boolean } => {
    if (!content) {
      return {
        displayContent: "Loading report content...",
        images: [],
        hasError: false,
      };
    }

    // Check if content contains apology or error message
    const errorPatterns = [
      "I'm sorry, but I cannot generate",
      "I cannot fabricate data",
      "sorry, I can't",
      "cannot generate a detailed report",
      "Sorry, we couldn't generate",
      "The information provided is empty",
    ];

    // If content contains any error pattern, mark as error
    if (errorPatterns.some((pattern) => content.includes(pattern))) {
      return {
        displayContent:
          "We couldn't generate a report based on the available information.",
        images: [],
        hasError: true,
      };
    }

    let displayContent = "";
    let images: string[] = [];

    try {
      // Try to parse as JSON first
      const jsonContent = JSON.parse(content);
      if (jsonContent.report) {
        // Check if the report itself contains an error message
        if (
          errorPatterns.some((pattern) => jsonContent.report.includes(pattern))
        ) {
          return {
            displayContent:
              "We couldn't generate a report based on the available information.",
            images: [],
            hasError: true,
          };
        }

        displayContent = jsonContent.report
          .replace(/^# (.*$)/gm, '<h1 class="text-3xl font-bold my-4">$1</h1>')
          .replace(/^## (.*$)/gm, '<h2 class="text-2xl font-bold my-3">$1</h2>')
          .replace(/^### (.*$)/gm, '<h3 class="text-xl font-bold my-2">$1</h3>')
          .replace(
            /\[(.*?)\]\((.*?)\)/g,
            '<a href="$2" class="text-blue-600 hover:underline" target="_blank">$1</a>'
          )
          .replace(
            /\*\*(.*?)\*\*/g,
            '<strong class="font-semibold">$1</strong>'
          )
          .replace(/\*(.*?)\*/g, "<em>$1</em>")
          .replace(/^---$/gm, '<hr class="my-6 border-t border-gray-300">')
          .replace(
            /^(?!<[h|a])(.+)$/gm,
            '<p class="my-2 leading-relaxed">$1</p>'
          )
          .replace(/<\/h2>/g, '</h2><div class="mb-6">');

        // Get images if available
        if (
          jsonContent.research_images &&
          Array.isArray(jsonContent.research_images)
        ) {
          images = jsonContent.research_images;
        }
      } else {
        // If jsonContent.report doesn't exist, use the JSON as is
        displayContent = JSON.stringify(jsonContent, null, 2);
      }
    } catch (e) {
      // If it's not JSON, use as plain text

      // Check again for error messages in plain text
      if (errorPatterns.some((pattern) => content.includes(pattern))) {
        return {
          displayContent:
            "We couldn't generate a report based on the available information.",
          images: [],
          hasError: true,
        };
      }

      displayContent = content
        .replace(/^# (.*$)/gm, '<h1 class="text-3xl font-bold my-4">$1</h1>')
        .replace(/^## (.*$)/gm, '<h2 class="text-2xl font-bold my-3">$1</h2>')
        .replace(/^### (.*$)/gm, '<h3 class="text-xl font-bold my-2">$1</h3>')
        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
        .replace(/\*(.*?)\*/g, "<em>$1</em>")
        .replace(/\n\n/g, '</p><p class="my-2 leading-relaxed">');
    }

    return { displayContent, images, hasError: false };
  };

  // Create a stable version of the editor setup
  const createEditorContent = (content: string | null) => {
    const { displayContent, images, hasError } = processReportContent(content);

    // Content to show based on whether there's an error or loading state
    let reportContent;

    if (content === null) {
      reportContent = `
        <div class="flex justify-center items-center py-8">
          <div class="animate-pulse flex flex-col items-center">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      `;
    } else if (hasError) {
      // Show error message if we couldn't generate a proper report
      reportContent = `
        <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <div class="flex items-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-700">Report Unavailable</h3>
          </div>
          <p class="text-gray-600">We couldn't generate a report based on the available information. This might be because:</p>
          <ul class="list-disc ml-6 mt-2 text-gray-600">
            <li>Insufficient or missing data</li>
            <li>The requested analysis couldn't be performed</li>
            <li>Required information was not provided</li>
          </ul>
          <p class="mt-4 text-gray-600">Please try again with more specific information or contact support if you need assistance.</p>
        </div>
      `;
    } else {
      // Show normal report content
      reportContent = `
        <div class="prose max-w-none leading-relaxed">
          ${displayContent}
        </div>
        
        ${
          images.length > 0
            ? `
          <div class="mt-12 border-t pt-8">
            <h2 class="text-2xl font-bold mb-6 text-center">Research Images</h2>
            <div class="grid grid-cols-2 gap-6">
              ${images
                .map(
                  (url, index) => `
                <div class="relative hover:transform hover:scale-[1.02] transition-all duration-300">
                  <img 
                    src="${url}" 
                    alt="Research image ${index + 1}"
                    class="w-full h-48 object-cover rounded-lg shadow-md"
                  />
                  <div class="absolute inset-0 bg-black opacity-0 hover:opacity-20 transition-opacity duration-300 rounded-lg"></div>
                </div>
              `
                )
                .join("")}
            </div>
          </div>
        `
            : ""
        }
      `;
    }

    // Always include artifact details and research report section
    return `
      <div>
        ${formatArtifactDetails(artifactDetails)}
        <div class="mt-8 report-section p-6 bg-white rounded-lg shadow">
          <h2 class="text-2xl font-bold mb-6 text-center">Research Report</h2>
          ${reportContent}
        </div>
      </div>
    `;
  };

  // Use liveblocks extension
  const liveblocks = useLiveblocksExtension({
    // Start with content from props if available, otherwise empty
    // This should only be used as initial content when the room is first created
    initialContent: artifactContent ? createEditorContent(artifactContent) : "",
  });

  // Setup initial loading state with timeout
  const setupLoadingTimeout = () => {
    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    loadingTimeoutRef.current = setTimeout(() => {
      setIsLoading(false);
      setContentProcessed(true);
    }, 15000);

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  };

  // Create editor instance with stable configuration
  const editor = useEditor({
    editorProps: {
      attributes: {
        style: "padding-left: 56px; padding-right: 56px",
        class:
          "focus: outline-none print:border-0 bg-white border-[#C7C7C7] border flex flex-col min-h-[1054px] w-[816px] pt-10 pr-14 pb-10 cursor-text",
      },
      editable: () => !isReadOnly,
      handlePaste(view, event: ClipboardEvent) {
        const clipboardData = event.clipboardData;
        if (!clipboardData) return false;

        const items = clipboardData.items;
        for (let i = 0; i < items.length; i++) {
          const item = items[i];
          if (item.type.startsWith("image/")) {
            const file = item.getAsFile();
            if (file) {
              const reader = new FileReader();
              reader.onload = () => {
                const src = reader.result as string;
                editor?.chain().focus().setImage({ src }).run();
              };
              reader.readAsDataURL(file);
              event.preventDefault();
              return true;
            }
          }
        }
        return false;
      },
    },
    extensions: [
      StarterKit.configure({
        blockquote: {
          HTMLAttributes: {
            class: "tiptap-blockquote",
          },
        },
        code: {
          HTMLAttributes: {
            class: "tiptap-code",
          },
        },
        codeBlock: {
          languageClassPrefix: "language-",
          HTMLAttributes: {
            class: "tiptap-code-block",
            spellcheck: false,
          },
        },
        heading: {
          levels: [1, 2, 3],
          HTMLAttributes: {
            class: "tiptap-heading",
          },
        },
        history: false,
        horizontalRule: {
          HTMLAttributes: {
            class: "tiptap-hr",
          },
        },
        listItem: {
          HTMLAttributes: {
            class: "tiptap-list-item",
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: "tiptap-ordered-list",
          },
        },
        paragraph: {
          HTMLAttributes: {
            class: "tiptap-paragraph",
          },
        },
      }),
      CharacterCount,
      Highlight.configure({
        HTMLAttributes: {
          class: "tiptap-highlight",
        },
      }),
      Link.configure({
        HTMLAttributes: {
          class: "tiptap-link",
        },
      }),
      Placeholder.configure({
        placeholder: "Start writing…",
        emptyEditorClass: "tiptap-empty",
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Typography,
      ImageResize,
      liveblocks,
    ],
    // Start with loading placeholder
    content: createEditorContent(null),
  });

  // Handle editor initialization and content updates in a single effect
  useEffect(() => {
    // Skip if editor isn't ready
    if (!editor) return;

    // Mark editor as initialized
    if (!editorInitialized.current) {
      editorInitialized.current = true;

      // Setup loading timeout on first initialization
      const cleanupTimeout = setupLoadingTimeout();

      // Check if Liveblocks already has content before setting new content
      const hasLiveblocksContent = editor.getText().trim().length > 0;

      // Only set content if Liveblocks doesn't already have content
      if (!hasLiveblocksContent && contentRef.current) {
        try {
          const initialContent = createEditorContent(contentRef.current);
          editor.commands.setContent(initialContent);
        } catch (error) {
          console.error("Error setting initial content:", error);
        }
      } else {
        // We already have content from Liveblocks, just finish loading
        setIsLoading(false);
        setContentProcessed(true);

        // Clear loading timeout as we've successfully loaded
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
          loadingTimeoutRef.current = null;
        }
      }

      return cleanupTimeout;
    }

    // If we have content now, update the editor safely
    if (contentRef.current && !contentProcessed) {
      try {
        const newContent = createEditorContent(contentRef.current);

        // Use setTimeout to ensure this happens after any other renders
        setTimeout(() => {
          // Double-check editor still exists before updating
          if (editor && editor.isDestroyed === false) {
            editor.commands.setContent(newContent);
            setIsLoading(false);
            setContentProcessed(true);

            // Clear loading timeout as we've successfully loaded
            if (loadingTimeoutRef.current) {
              clearTimeout(loadingTimeoutRef.current);
              loadingTimeoutRef.current = null;
            }
          }
        }, 0);
      } catch (error) {
        console.error("Error updating editor content:", error);
        setIsLoading(false);
        setContentProcessed(true);
      }
    }
  }, [editor, contentProcessed, artifactDetails]);

  // Handle changes to artifactContent in a separate effect
  useEffect(() => {
    // If we already have the editor and new content arrives, update
    // BUT only if we haven't processed content yet and we don't have Liveblocks content
    if (editor && !contentProcessed) {
      // Check if Liveblocks already has content
      const hasLiveblocksContent = editor.getText().trim().length > 0;

      // Only update if there's no existing content
      if (!hasLiveblocksContent) {
        try {
          const newContent = createEditorContent(artifactContent);

          // Use setTimeout to avoid state updates during render
          setTimeout(() => {
            if (editor && editor.isDestroyed === false) {
              editor.commands.setContent(newContent);
              setIsLoading(false);
              setContentProcessed(true);

              // Clear loading timeout as we've successfully loaded
              if (loadingTimeoutRef.current) {
                clearTimeout(loadingTimeoutRef.current);
                loadingTimeoutRef.current = null;
              }
            }
          }, 0);
        } catch (error) {
          console.error("Error updating editor with new content:", error);
          setIsLoading(false);
          setContentProcessed(true);
        }
      } else {
        // We already have content from Liveblocks, just finish loading
        setIsLoading(false);
        setContentProcessed(true);

        // Clear loading timeout as we've successfully loaded
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
          loadingTimeoutRef.current = null;
        }
      }
    }
  }, [artifactContent, editor, contentProcessed]);

  const handleDownloadPDF = async () => {
    if (!artifactContent) {
      return;
    }

    try {
      let textContent = artifactContent;
      let images: string[] = [];

      try {
        const jsonContent = JSON.parse(artifactContent);
        if (jsonContent.report) {
          textContent = jsonContent.report;
          images = jsonContent.research_images || [];
        }
      } catch (e) {
        console.error("Content is not JSON, using as plain text");
      }

      // Function to load image and convert to base64
      const loadImage = (url: string): Promise<string> => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.crossOrigin = "anonymous";
          img.onload = () => {
            try {
              const canvas = document.createElement("canvas");
              canvas.width = img.width;
              canvas.height = img.height;
              const ctx = canvas.getContext("2d");
              ctx?.drawImage(img, 0, 0);
              resolve(canvas.toDataURL("image/jpeg"));
            } catch (err) {
              reject(err);
            }
          };
          img.onerror = () => reject(new Error("Failed to load image"));
          img.src = url;
        });
      };

      // Create new PDF document
      const pdf = new jsPDF();

      // Set initial formatting
      const margin = 20;
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const maxWidth = pageWidth - 2 * margin;

      let currentY = margin;

      // Add title
      pdf.setFontSize(24);
      pdf.setFont("helvetica", "bold");
      const title = `Research Report${runName ? ` - ${runName}` : ""}`;
      pdf.text(title, margin, currentY);
      currentY += 15;

      // Add date
      pdf.setFontSize(12);
      pdf.setFont("helvetica", "normal");
      const date = new Date().toLocaleDateString();
      pdf.text(`Generated on: ${date}`, margin, currentY);
      currentY += 20;

      // Add content with proper formatting
      const processText = (text: string) => {
        return text
          .replace(/^#\s+/gm, "")
          .replace(/^##\s+/gm, "")
          .replace(/\*\*(.*?)\*\*/g, "$1")
          .replace(/\*(.*?)\*/g, "$1")
          .replace(/\[(.*?)\]\(.*?\)/g, "$1")
          .replace(/^\s*[-*]\s+/gm, "• ");
      };

      const addFormattedText = (text: string) => {
        const processedText = processText(text);
        const lines = pdf.splitTextToSize(processedText, maxWidth);

        lines.forEach((line: string) => {
          if (currentY > pageHeight - margin) {
            pdf.addPage();
            currentY = margin;
          }

          if (line.trim()) {
            pdf.text(line, margin, currentY);
            currentY += 8;
          }
        });
      };

      // Add main content
      addFormattedText(textContent);

      // Add images if available
      if (images.length > 0) {
        pdf.addPage();
        currentY = margin;

        pdf.setFontSize(20);
        pdf.setFont("helvetica", "bold");
        pdf.text("Research Images", margin, currentY);
        currentY += 20;

        let successfulImageCount = 0;

        // Load and add images
        for (let i = 0; i < images.length; i++) {
          try {
            const imageData = await loadImage(images[i]);

            // Only add new page if we've successfully added an image before
            if (successfulImageCount > 0) {
              pdf.addPage();
              currentY = margin;
            }

            // Add image caption
            pdf.setFontSize(12);
            pdf.text(`Image ${successfulImageCount + 1}`, margin, currentY);
            currentY += 10;

            // Calculate image dimensions
            const imageHeight = maxWidth * 0.75;

            // Add image
            pdf.addImage(
              imageData,
              "JPEG",
              margin,
              currentY,
              maxWidth,
              imageHeight
            );

            successfulImageCount++;
            currentY += imageHeight + 15;
          } catch (e) {
            // Silently skip failed images
            console.error(`Error with image ${i + 1}:`, e);
          }
        }

        // If no images were successfully added, remove the "Research Images" page
        if (successfulImageCount === 0) {
          pdf.deletePage(pdf.getNumberOfPages());
        }
      }

      // Save the PDF
      pdf.save(`report_${runName || "download"}.pdf`);
    } catch (error) {
      console.error("Error downloading PDF:", error);
    }
  };

  const handleReloadReport = () => {
    if (!editor || !contentRef.current) return;

    try {
      setIsLoading(true);
      setContentProcessed(false);

      // Create fresh content from the current artifact content
      const reloadedContent = createEditorContent(contentRef.current);

      // Set the content in the editor
      setTimeout(() => {
        if (editor && !editor.isDestroyed) {
          editor.commands.setContent(reloadedContent);
          setIsLoading(false);
          setContentProcessed(true);
        }
      }, 0);
    } catch (error) {
      console.error("Error reloading report content:", error);
      setIsLoading(false);
      setContentProcessed(true);
    }
  };

  return (
    <div className="size-full overflow-x-auto bg-[#f4f4f5] print:p-0 print:bg-white print:overflow-visible">
      <div className="bg-white overflow-x-scroll border-[1px] border-[#dededf] flex justify-between items-center px-2 py-3">
        <div className="flex items-center gap-4">
          <Toolbar editor={editor} />
          <div
            onClick={handleDownloadPDF}
            className="cursor-pointer"
            title="Download as PDF"
          >
            <Download width={20} height={20} />
          </div>
          <div
            onClick={handleReloadReport}
            className="cursor-pointer"
            title="Reload Report"
          >
            <RefreshCw width={20} height={20} />
          </div>
        </div>
      </div>

      <div className="min-w-max flex flex-col justify-center w-[816px] py-4 print:py-0 mx-auto print:w-full print:min-w-0">
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
            <div className="text-center p-4 bg-white rounded-lg shadow-md">
              <p className="font-medium">Loading report content...</p>
              <div className="mt-2 w-12 h-1 bg-gray-200 rounded-full mx-auto overflow-hidden">
                <div className="h-full bg-blue-600 animate-pulse"></div>
              </div>
            </div>
          </div>
        )}
        {editor && <EditorContent editor={editor} className="editor-content" />}
        <FloatingComposer editor={editor} style={{ width: 350 }} />
        <Threads editor={editor} />
      </div>
    </div>
  );
};

export default CollaborativeEditor;
