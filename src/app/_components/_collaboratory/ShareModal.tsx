"use client";

import React, { useEffect, useState } from "react";
import { Dialog } from "@headlessui/react";
import { ChevronDownIcon, LinkIcon } from "@heroicons/react/24/outline";
import { doc, updateDoc, arrayUnion, arrayRemove } from "firebase/firestore";
import { db } from "@/app/lib/firebase";
import { usePendoTrack } from "@/hooks/usePendoTrack";

interface Collaborator {
  nickname: string;
  email: string;
  role: string;
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  experimentId: string;
  owner: {
    nickname: string;
    email: string;
  };
  experimentTitle: string; // Add this prop
  collaborators: Collaborator[];
  onUpdateCollaborators: (newCollaborators: Collaborator[]) => void;
}

const ShareModal = ({
  isOpen,
  onClose,
  experimentId,
  owner,
  experimentTitle,
  collaborators,
  onUpdateCollaborators,
}: ShareModalProps) => {
  const [email, setEmail] = useState("");
  const [permission, setPermission] = useState("Can view");
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [localCollaborators, setLocalCollaborators] = useState<Collaborator[]>(
    []
  );
  const [linkCopied, setLinkCopied] = useState(false);
  const { trackFeatureUsage } = usePendoTrack();

  useEffect(() => {
    // Ensure the owner is always included as a collaborator
    if (!collaborators.some((collab) => collab.email === owner.email)) {
      const updatedCollaborators = [
        { nickname: owner.nickname, email: owner.email, role: "Owner" },
        ...collaborators,
      ];
      setLocalCollaborators(updatedCollaborators);
      onUpdateCollaborators(updatedCollaborators);
    } else {
      setLocalCollaborators(collaborators);
    }
  }, [collaborators, owner, onUpdateCollaborators]);

  const handleInvite = async () => {
    if (email) {
      // Track when a user invites a collaborator
      trackFeatureUsage("Collab", "clicked", {
        action: "invite_collaborator",
        permission: permission,
      });
      const newCollaborator = {
        nickname: email.split("@")[0],
        email,
        role: permission,
      };

      const updatedCollaborators = [...localCollaborators, newCollaborator];

      // Update Firestore
      const docRef = doc(db, "collaboratories", experimentId);
      await updateDoc(docRef, {
        collaborators: arrayUnion(newCollaborator),
      });

      const baseUrl = process.env.AUTH0_BASE_URL;
      const experimentUrl = `${baseUrl}collaboratory/${experimentId}`;

      try {
        const response = await fetch("/api/notifs", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: "collab-invite-in-app",
            to: {
              subscriberId: email,
              firstName: owner.nickname,
              lastName: experimentTitle,
              email,
              avatar: experimentUrl,
            },
            payload: {
              workspaceName: experimentId,
              inviter: owner.nickname,
            },
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to send notification");
        }
      } catch (error) {
        console.error("Error sending notification:", error);
      }

      setLocalCollaborators(updatedCollaborators);
      onUpdateCollaborators(updatedCollaborators);
      setEmail("");
    }
  };

  const handleDelete = async (collaborator: Collaborator) => {
    if (collaborator.role === "Owner") {
      alert("You cannot remove the owner of the experiment.");
      return;
    }

    // Track when a user removes a collaborator
    trackFeatureUsage("Collab", "clicked", {
      action: "remove_collaborator",
      collaboratorRole: collaborator.role,
    });

    const updatedCollaborators = localCollaborators.filter(
      (collab) => collab.email !== collaborator.email
    );

    // Update Firestore to remove the collaborator
    const docRef = doc(db, "collaboratories", experimentId);
    await updateDoc(docRef, {
      collaborators: arrayRemove(collaborator),
    });

    // Update local state and parent component
    setLocalCollaborators(updatedCollaborators);
    onUpdateCollaborators(updatedCollaborators);
  };

  const copyLinkToClipboard = () => {
    // Track when a user copies the link
    trackFeatureUsage("Collab", "clicked", { action: "copy_link" });

    const baseUrl = window.location.origin;
    const collaboratoryLink = `${baseUrl}/collaboratory/${experimentId}`;

    navigator.clipboard
      .writeText(collaboratoryLink)
      .then(() => {
        setLinkCopied(true);
        setTimeout(() => setLinkCopied(false), 2000);
      })
      .catch((err) => {
        console.error("Could not copy link: ", err);
        alert("Failed to copy link to clipboard");
      });
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div
        className="fixed inset-0 bg-black bg-opacity-30"
        aria-hidden="true"
      />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-lg rounded-xl bg-white shadow-lg">
          <div className="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
            <h3 className="text-xl font-semibold text-[#101828]">
              Manage who can view this project
            </h3>
            <button
              onClick={onClose}
              className="text-[#667085] focus:outline-none"
            >
              ✕
            </button>
          </div>

          <div className="px-6 py-4 space-y-4">
            <div className="flex items-center space-x-4">
              <input
                type="email"
                placeholder="Enter email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-grow px-4 py-2 rounded-lg bg-white text-[#101828] focus:ring-1 focus:ring-blue-500 focus:outline-none border-[#D0D5DD] border-2"
              />
              <div className="relative">
                <button
                  className="flex items-center justify-between px-4 py-2 bg-white text-[#101828] rounded-md hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none border-[#D0D5DD] border-2"
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                >
                  {permission}
                  <ChevronDownIcon className="w-5 h-5 ml-2 text-gray-400" />
                </button>
                {dropdownOpen && (
                  <div className="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg z-10">
                    <div
                      onClick={() => {
                        setPermission("Can view");
                        setDropdownOpen(false);
                      }}
                      className="px-4 py-2 text-sm text-[#101828] hover:bg-gray-100 hover:rounded-md cursor-pointer"
                    >
                      Can view
                    </div>
                    <div
                      onClick={() => {
                        setPermission("Can edit");
                        setDropdownOpen(false);
                      }}
                      className="px-4 py-2 text-sm text-[#101828] hover:bg-gray-100 hover:rounded-md cursor-pointer"
                    >
                      Can edit
                    </div>
                  </div>
                )}
              </div>
              <button
                onClick={handleInvite}
                className="px-4 py-2 bg-[#312E81] text-white font-semibold rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              >
                Invite
              </button>
            </div>

            <div className="space-y-4">
              {localCollaborators.map((collaborator, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between text-gray-300"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-[#312E81] text-white rounded-full flex items-center justify-center text-sm font-medium">
                      {collaborator.nickname.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className="text-[#101828] font-medium">
                        {collaborator.nickname}
                      </p>
                      <p className="text-gray-400 text-sm">
                        {collaborator.email}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-400">
                      {collaborator.role}
                    </span>
                    {owner.email === collaborator.email ? null : (
                      <button
                        onClick={() => handleDelete(collaborator)}
                        className="text-red-500 hover:underline text-sm"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <div className="flex items-center justify-end">
              <div className="relative">
                <button
                  onClick={copyLinkToClipboard}
                  className="flex items-center px-4 py-2 text-sm bg-[#f0f0f5] text-[#312E81] rounded-md hover:bg-[#e0e0f0] focus:outline-none transition-colors"
                >
                  <LinkIcon className="w-4 h-4 mr-2" />
                  {linkCopied ? "Copied!" : "Copy link"}
                </button>
                {linkCopied && (
                  <div className="absolute right-0 mt-2 bg-gray-800 text-white text-xs py-1 px-2 rounded shadow-lg">
                    Link copied!
                  </div>
                )}
              </div>
            </div>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default ShareModal;
