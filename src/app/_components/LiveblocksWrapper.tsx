"use client";
import React, { ReactNode } from "react";
import {
  LiveblocksProvider,
  ClientSideSuspense,
} from "@liveblocks/react/suspense";

const LiveblocksWrapper = ({ children }: { children: ReactNode }) => {
  return (
    <LiveblocksProvider authEndpoint="/api/liveblocks-auth">
      <ClientSideSuspense fallback={<div>Loader...</div>}>
        {children}
      </ClientSideSuspense>
    </LiveblocksProvider>
  );
};

export default LiveblocksWrapper;
