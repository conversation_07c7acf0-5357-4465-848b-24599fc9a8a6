// test.tsx

import React from "react";
import { render, screen } from "@testing-library/react";
import MobileView from "../MobileView";

// Mocking the next/image component
jest.mock("next/image", () => ({
  __esModule: true,
  default: ({ src, alt }: { src: string; alt: string }) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img src={src} alt={alt} />;
  },
}));

// Mocking the next/link component
jest.mock("next/link", () => ({
  __esModule: true,
  default: ({
    href,
    children,
  }: {
    href: string;
    children: React.ReactNode;
  }) => {
    return <a href={href}>{children}</a>;
  },
}));

describe("MobileView Component", () => {
  beforeEach(() => {
    render(<MobileView />);
  });

  it("should render the logo image", () => {
    const logoImages = screen.getAllByAltText("logo");
    expect(logoImages[0]).toBeInTheDocument();
    expect(logoImages[0]).toHaveAttribute("src", "/brain_logo_with_name.svg");
  });

  it("should render the desktop image", () => {
    const logoImages = screen.getAllByAltText("logo");
    expect(logoImages[1]).toBeInTheDocument();
    expect(logoImages[1]).toHaveAttribute("src", "/desktop_img.svg");
  });

  it("should display the correct text messages", () => {
    expect(
      screen.getByText("Subconscious AI is not currently available on mobile.")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Please visit us on a desktop for the full experience.")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Thank you for your understanding!")
    ).toBeInTheDocument();
  });

  it("should have a link that redirects to the main website", () => {
    const linkElement = screen.getByRole("link", {
      name: /Back to the website/i,
    });
    expect(linkElement).toBeInTheDocument();
    expect(linkElement).toHaveAttribute("href", "https://www.subconscious.ai/");
  });
});
