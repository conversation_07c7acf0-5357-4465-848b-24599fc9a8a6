import React from "react";
import Image from "next/image";
import Link from "next/link";

const MobileView = () => {
  return (
    <div className="flex flex-col h-screen w-full bg-backgroundMobile bg-cover bg-landing-page-bg">
      <div className="flex w-full justify-center items-cente mt-8">
        <Image
          src="/brain_logo_with_name.svg"
          alt="logo"
          width={200}
          height={100}
        />
      </div>

      <div className="flex w-full mt-12 justify-center items-center">
        <Image src="/desktop_img.svg" alt="logo" width={250} height={100} />
      </div>

      <div className="flex gap-4 flex-col justify-center items-center text-center px-10 text-white font-medium text-sm md:text-base mt-4">
        <div>Subconscious AI is not currently available on mobile. </div>
        <div>Please visit us on a desktop for the full experience. </div>
        <div>Thank you for your understanding! </div>
      </div>

      <div className="flex justify-center items-center mt-8">
        <Link
          href="https://www.subconscious.ai/"
          className="p-1 rounded-full bg-soft-gradient"
        >
          <span className="block text-white text-roboto px-8 py-3 text-base font-semibold rounded-full bg-[#171C3C] hover:bg-transparent transition duration-300">
            Back to the website
          </span>
        </Link>
      </div>
    </div>
  );
};

export default MobileView;
