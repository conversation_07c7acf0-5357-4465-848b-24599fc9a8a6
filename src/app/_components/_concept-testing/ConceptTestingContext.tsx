"use client";
import React from "react";
import {
  Country,
  PopulationTraits,
  DisplayTrait,
  DisplayAttribute,
  BrandAttributeCombination,
  FileState,
  LLMModel,
  Persona,
} from "../_ideation/objects";

// Concept-testing specific types
export interface QuestionWithScale {
  id: string;
  text: string;
  scale?: string[];
}

export interface ConceptTestingContextInterface {
  // Concept-testing specific fields
  image: File | null;
  setImage: (file: File | null) => void;
  imageS3Url: string | null;
  setImageS3Url: (url: string | null) => void;
  imageS3Key: string | null;
  setImageS3Key: (key: string | null) => void;
  description: string;
  setDescription: (desc: string) => void;
  questions: QuestionWithScale[];
  setQuestions: (questions: QuestionWithScale[]) => void;

  // Step management
  currStep: number;
  setCurrStep: (step: number) => void;
  steps: { name: string; status: string }[];
  setSteps: (steps: { name: string; status: string }[]) => void;
  previousStep: number;
  setPreviousStep: (step: number) => void;

  // When/Where fields (needed by WhenWhereStep and WhoComponent)
  when: string;
  setWhen: (when: string) => void;
  where: Country | undefined;
  setWhere: (where: Country) => void;
  selectedState: string | null;
  setSelectedState: (state: string | null) => void;

  // Who component fields (needed by WhoComponent)
  // Step 1: Population demographic traits (age, income, gender, etc.)
  populationTraits: PopulationTraits;
  setPopulationTraits: (populationTrait: PopulationTraits) => void;

  // Step 3: Specialist display traits (from traitValues.js)
  displayTraits: DisplayTrait[];
  setDisplayTraits: (displayTraits: DisplayTrait[]) => void;

  displayAttributes: DisplayAttribute[];
  setDisplayAttributes: (displayAttributes: DisplayAttribute[]) => void;
  realWorlBrandAttributeCombinations: BrandAttributeCombination[];
  setRealWorldBrandAttributeCombinations: (
    brandattrComb: BrandAttributeCombination[]
  ) => void;
  selectedLlmModel: LLMModel;
  setSelectedLlmModel: (model: LLMModel) => void;
  validatedQuestions: Set<string>;
  setValidatedQuestions: (questions: Set<string>) => void;
  personas: Persona[];
  setPersonas: (personas: Persona[]) => void;
  specialistTraits: Record<string, DisplayTrait[]>;
  setSpecialistTraits: (
    specialistTraits: Record<string, DisplayTrait[]>
  ) => void;
  selectedSection: "characteristics" | "personas";
  setSelectedSection: (section: "characteristics" | "personas") => void;
  productExists: boolean;
  setProductExists: (productExists: boolean) => void;
  fileState: FileState;
  setFileState: (fileState: FileState) => void;

  // Fields needed for API calls in WhoComponent
  question: string; // Derived from description or first question
  activeSpecialist: string;
  setActiveSpecialist: (specialist: string) => void;
}

// Default values for concept testing context
const defaultConceptTestingContext: ConceptTestingContextInterface = {
  // Concept-testing specific fields
  image: null,
  setImage: () => {},
  imageS3Url: null,
  setImageS3Url: () => {},
  imageS3Key: null,
  setImageS3Key: () => {},
  description: "",
  setDescription: () => {},
  questions: [],
  setQuestions: () => {},

  // Step management
  currStep: 0,
  setCurrStep: () => {},
  steps: [],
  setSteps: () => {},
  previousStep: 0,
  setPreviousStep: () => {},

  // When/Where fields
  when: new Date().getFullYear().toString(),
  setWhen: () => {},
  where: undefined,
  setWhere: () => {},
  selectedState: null,
  setSelectedState: () => {},

  // Who component fields
  populationTraits: {
    state: null,
    age: [18, 95],
    household_income: [0, 371000],
    gender: ["Female", "Male"],
    education_level: [
      "High School Diploma",
      "High School but no diploma",
      "Some College",
      "Less than high school",
      "Bachelors",
      "Masters",
      "Associates",
      "PhD",
    ],
    number_of_children: ["0", "1", "2", "4+", "3"],
    racial_group: [
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  },
  setPopulationTraits: () => {},
  displayTraits: [],
  setDisplayTraits: () => {},
  displayAttributes: [],
  setDisplayAttributes: () => {},
  realWorlBrandAttributeCombinations: [],
  setRealWorldBrandAttributeCombinations: () => {},
  selectedLlmModel: { name: "gpt4" },
  setSelectedLlmModel: () => {},
  validatedQuestions: new Set<string>(),
  setValidatedQuestions: () => {},
  personas: [],
  setPersonas: () => {},
  specialistTraits: {},
  setSpecialistTraits: () => {},
  selectedSection: "characteristics",
  setSelectedSection: () => {},
  productExists: false,
  setProductExists: () => {},
  fileState: { file: null, data: null, error: null },
  setFileState: () => {},
  question: "", // Derived from description or first question
  activeSpecialist: "",
  setActiveSpecialist: () => {},
};

const ConceptTestingContext =
  React.createContext<ConceptTestingContextInterface>(
    defaultConceptTestingContext
  );

export const ConceptTestingContextProvider = ConceptTestingContext.Provider;
export default ConceptTestingContext;
