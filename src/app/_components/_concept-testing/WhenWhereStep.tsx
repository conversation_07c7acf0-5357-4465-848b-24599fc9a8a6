import React, { useContext } from "react";
import Map from "../_ideation/_whenwhere/Map";
import CountrySelector from "../_ideation/_whenwhere/CountrySelector";
import StateSelector from "../_ideation/_whenwhere/StateSelector";
import ConceptTestingContext from "./ConceptTestingContext";
import Tooltip from "../_util/ToolTip";
import { Info } from "lucide-react";
import countries from "../../../../public/data/countries.json";
import NavigationButtons from "./NavigationButtons";

interface WhenWhereStepProps {
  onComplete: () => void;
  onBack: () => void;
}

const WhenWhereStep: React.FC<WhenWhereStepProps> = ({
  onComplete,
  onBack,
}) => {
  const context = useContext(ConceptTestingContext);

  if (!context) {
    console.error("WhenWhereStep must be used within ConceptTestingContext");
    return null;
  }

  const { when, setWhen, where, setWhere, selectedState, setSelectedState } =
    context;

  const isUSA = where?.name === "United States of America (USA)";

  const isNextDisabled = () => {
    return !where || when === "";
  };

  const handleWhen = (e: React.ChangeEvent<HTMLInputElement>): void => {
    setWhen(e.target.value);
  };

  const handleCountryChange = (country: any) => {
    setWhere(country);
    if (country.name === "United States of America (USA)") {
      setSelectedState(null);
    }
  };

  const handleNext = () => {
    if (!isNextDisabled()) {
      onComplete();
    }
  };

  const handleBack = () => {
    onBack();
  };

  return (
    <div className="flex flex-col gap-6 max-w-4xl w-full mx-auto">
      <h2 className="text-xl font-semibold text-text-default">When & Where</h2>
      <p className="text-text-light mb-1">
        Define when and where your concept will be tested.
      </p>
      {/* WHEN */}
      <div className="flex flex-col items-stretch gap-3 mb-4">
        <div className="flex gap-2 items-center">
          <label className="font-medium text-text-default text-base">
            When
          </label>
          <div className="relative group">
            <Info className="h-4 w-4 text-text-light cursor-help" />
            <Tooltip
              message="Select the year for your concept test. You can choose any year (e.g., 2023, nights, weekends, holidays, early '90s, etc.)"
              position="top"
            />
          </div>
        </div>
        <input
          className="px-3.5 py-2.5 rounded-md border border-input bg-background focus:outline-none focus:ring-2 focus:ring-ring"
          type="text"
          value={when}
          onChange={handleWhen}
          placeholder="e.g., 2024, weeknights, holidays"
        />
      </div>

      {/* WHERE */}
      <div className="mb-4">
        <CountrySelector
          countries={countries}
          selectedCountry={where}
          setSelectedCountry={handleCountryChange}
        />

        {/* State selector for USA */}
        {isUSA && (
          <div className="mt-4">
            <StateSelector
              selectedState={selectedState}
              onStateChange={setSelectedState}
            />
          </div>
        )}
      </div>

      {/* MAP */}
      <div className="w-full mt-4 border rounded-lg overflow-hidden">
        <Map
          selected={where !== undefined}
          x={where?.coords.x}
          y={where?.coords.y}
        />
      </div>
      <NavigationButtons
        onBack={handleBack}
        onNext={handleNext}
        nextDisabled={isNextDisabled()}
      />
    </div>
  );
};

export default WhenWhereStep;
