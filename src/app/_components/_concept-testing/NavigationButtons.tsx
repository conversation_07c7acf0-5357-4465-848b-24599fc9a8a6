"use client";
import { memo } from "react";

interface NavigationButtonsProps {
  onBack: () => void;
  onNext: () => void;
  nextLabel?: string;
  backLabel?: string;
  nextDisabled?: boolean;
  isSubmitting?: boolean;
}

const NavigationButtons = memo(function NavigationButtons({
  onBack,
  onNext,
  nextLabel = "Next",
  backLabel = "Back",
  nextDisabled = false,
  isSubmitting = false,
}: NavigationButtonsProps) {
  return (
    <div className="sticky bottom-0 left-0 bg-background z-30">
      <div className="flex w-full justify-between py-2 ">
        {backLabel ? (
          <button
            className="font-inter text-base font-medium py-2.5 px-6 bg-white hover:bg-secondary-grey border border-card-border shadow-sm rounded-lg"
            onClick={onBack}
            type="button"
          >
            {backLabel}
          </button>
        ) : (
          <div />
        )}
        <button
          className={`font-inter text-base font-semibold py-2.5 flex items-center justify-center ${
            nextDisabled || isSubmitting
              ? "bg-[#ECEDFB] cursor-not-allowed"
              : "bg-primary hover:bg-primary-dark"
          } text-white rounded-lg shadow-sm w-[170px]`}
          onClick={onNext}
          disabled={nextDisabled || isSubmitting}
          type="button"
          aria-label={nextLabel}
        >
          {isSubmitting && (
            <svg
              className="animate-spin h-5 w-5 mr-2 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8v8z"
              ></path>
            </svg>
          )}
          {isSubmitting ? "Loading..." : nextLabel}
        </button>
      </div>
    </div>
  );
});

export default NavigationButtons;
