"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Share2 } from "lucide-react";

interface ShareButtonClientProps {
  /** The URL to share. Defaults to window.location.href if not provided. */
  shareUrl?: string;
  /** The title to use for sharing (e.g., for Web Share API). */
  shareTitle?: string;
  /** The text to use for sharing (e.g., for Web Share API). */
  shareText?: string;
}

export function ShareButton({
  shareUrl,
  shareTitle = "Concept Test Results",
  shareText = "Check out these concept test results!",
}: ShareButtonClientProps) {
  const [buttonText, setButtonText] = useState("Share");
  const [isCopied, setIsCopied] = useState(false);
  const [canShare, setCanShare] = useState(false);

  useEffect(() => {
    // Check if Web Share API is available
    if (typeof navigator.share === "function") {
      setCanShare(true);
    }
  }, []);

  const handleShare = async () => {
    const urlToShare = shareUrl || window.location.href;

    if (canShare) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: urlToShare,
        });
        // Optional: Could add a small toast/message here for successful share
      } catch (error) {
        console.error("Failed to share via Web Share API:", error);
        copyToClipboard(urlToShare);
      }
    } else {
      copyToClipboard(urlToShare);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setButtonText("Copied!");
      setIsCopied(true);
      setTimeout(() => {
        setButtonText("Share");
        setIsCopied(false);
      }, 2000); // Reset text after 2 seconds
    } catch (err) {
      console.error("Failed to copy text: ", err);
      setButtonText("Failed!");
      setTimeout(() => {
        setButtonText("Share");
      }, 2000);
    }
  };

  return (
    <Button
      variant="outline"
      className={`h-10 gap-2 ${isCopied ? "bg-green-50 text-green-700 border-green-300" : ""}`}
      onClick={handleShare}
      disabled={isCopied} // Disable while "Copied!" is shown
    >
      <Share2 className="h-4 w-4" />
      {buttonText}
    </Button>
  );
}
