"use client";
import React from "react";
import { ValidationProvider } from "../_ideation/_who/contexts/ValidationContext";
import { DemographicTraitsProvider } from "../_ideation/_who/contexts/DemographicTraitsContext";
import { useCountrySpecificLogic } from "../_ideation/_who/hooks/useCountrySpecificLogic";
import ConceptTestingContext from "./ConceptTestingContext";

interface ConceptTestingProvidersProps {
  children: React.ReactNode;
  selectedState: string | null;
}

const ConceptTestingProviders: React.FC<ConceptTestingProvidersProps> = ({
  children,
  selectedState,
}) => {
  const context = React.useContext(ConceptTestingContext);
  const { stateData } = useCountrySpecificLogic(
    context?.where?.name || undefined,
    selectedState
  );

  return (
    <ValidationProvider initialValidationResults={null}>
      <DemographicTraitsProvider
        selectedState={selectedState}
        stateData={stateData}
      >
        {children}
      </DemographicTraitsProvider>
    </ValidationProvider>
  );
};

export default ConceptTestingProviders;
