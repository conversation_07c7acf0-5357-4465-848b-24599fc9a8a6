/* eslint-disable no-unused-vars */

import { ErrorBoundary } from "@sentry/react";
import React, { useState } from "react";
import Modal from "react-modal";

const GlobalError = ({ children }: { children: React.ReactNode }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <ErrorBoundary
      fallback={({ error, componentStack, resetError }) => (
        <Modal
          isOpen={isModalOpen}
          onRequestClose={closeModal}
          contentLabel="Error Modal"
          ariaHideApp={false}
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.75)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            },
            content: {
              position: "relative",
              background: "#fff",
              padding: "15px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              width: "400px",
              maxHeight: "90%",
              overflowY: "auto",
              boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
            },
          }}
        >
          <h2 style={{ color: "#1C1D47", textAlign: "center" }}>
            Something went wrong!
          </h2>
          <p style={{ textAlign: "center", margin: "20px 0" }}>
            An unexpected error has occurred. Please try reloading the page.
          </p>
          <button
            onClick={() => {
              setIsModalOpen(false);
              window.location.reload();
            }}
            style={{
              display: "block",
              margin: "0 auto",
              padding: "10px 20px",
              fontSize: "16px",
              color: "#fff",
              backgroundColor: "#1C1D47",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Try again
          </button>
        </Modal>
      )}
      onError={openModal}
    >
      {children}
    </ErrorBoundary>
  );
};

export default GlobalError;
