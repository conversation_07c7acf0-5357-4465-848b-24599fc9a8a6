import React from "react";

interface LoadingIndicatorProps {
  message?: string;
  defaultMessage?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

/**
 * A reusable loading indicator component with bouncing dots
 */
const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  message,
  defaultMessage = "We are processing your request... Please hold on for a moment.",
  size = "md",
  className = "",
}) => {
  // Size classes for the dots
  const sizeClasses = {
    sm: "h-2 w-2",
    md: "h-4 w-4",
    lg: "h-6 w-6",
  };

  // Size classes for the text
  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  };

  return (
    <div className={`flex items-center justify-center gap-2 ${className}`}>
      <span className="sr-only">Loading...</span>
      <div
        className={`${sizeClasses[size]} bg-primary rounded-full animate-bounce [animation-delay:-0.3s]`}
      ></div>
      <div
        className={`${sizeClasses[size]} bg-primary rounded-full animate-bounce [animation-delay:-0.15s]`}
      ></div>
      <div
        className={`${sizeClasses[size]} bg-primary rounded-full animate-bounce`}
      ></div>
      <div className={`${textSizeClasses[size]} text-primary p-2 rounded`}>
        {message || defaultMessage}
      </div>
    </div>
  );
};

export default LoadingIndicator;
