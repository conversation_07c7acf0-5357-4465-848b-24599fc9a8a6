import React, { Fragment } from "react";

interface TooltipProps {
  message: string;
  position: "top" | "bottom" | "left" | "right";
  children?: React.ReactNode;
}

const Tooltip = ({ message, position }: TooltipProps) => {
  let tooltipPosition: React.CSSProperties = {};
  if (position === "top") {
    tooltipPosition = {
      bottom: "100%",
      left: "50%",
      transform: "translateX(-50%) translateY(-8px)",
    };
  } else if (position === "bottom") {
    tooltipPosition = {
      top: "100%",
      left: "50%",
      transform: "translateX(-50%) translateY(8px)",
    };
  } else if (position === "left") {
    tooltipPosition = {
      top: "50%",
      right: "100%",
      transform: "translateY(-50%) translateX(-8px)",
    };
  } else if (position === "right") {
    tooltipPosition = {
      top: "50%",
      left: "100%",
      transform: "translateY(-50%) translateX(8px)",
    };
  }

  return (
    <Fragment>
      <div
        className="absolute z-40 p-2 text-xs rounded text-white bg-[#1C1D47] opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-md pointer-events-none w-64"
        style={tooltipPosition}
      >
        {message}
      </div>
    </Fragment>
  );
};

export default Tooltip;
