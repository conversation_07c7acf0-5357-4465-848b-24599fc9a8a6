"use client";
import React from "react";

import SessionContext from "./SessionContext";
import { Run } from "../_experiments/types";
import { Roles } from "../_access/types";

export default function ContextWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const [runs, setRuns] = React.useState<Run[]>([]);
  const [roles, setRoles] = React.useState<Roles>({ roles: [] });
  const [awaitingAccess, setAwaitingAccess] = React.useState<boolean>(true);

  return (
    <SessionContext.Provider
      value={{
        runs: runs,
        roles: roles,
        awaitingAccess: awaitingAccess,
        setRuns: setRuns,
        setRoles: setRoles,
        setAwaitingAccess: setAwaitingAccess,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
}
