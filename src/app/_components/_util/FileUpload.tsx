import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, X } from "lucide-react";

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  selectedFile: File | null;
  error: string | null;
}

export function FileUpload({
  onFileSelect,
  onFileRemove,
  selectedFile,
  error,
}: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileSelect(file);
    }
  };

  // Drag event handlers
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      // Check if it's a CSV file
      const file = files[0];
      if (file.type === "text/csv" || file.name.endsWith(".csv")) {
        onFileSelect(file);
      } else {
        // You could set an error state here to show the user that only CSV files are accepted
        console.error("Only CSV files are accepted");
      }
    }
  };

  return (
    <div className="space-y-4">
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 text-red-600 rounded-lg">
          {error}
        </div>
      )}

      <div className="grid w-full items-center gap-1.5">
        <Label htmlFor="fileUpload">
          Upload Your own Attributes.CSV File (Optional)
          <br></br>
          <span className="text-text-placeholder pt-2 text-sm">
            Format: AttributeName, level1, level2, level3,...
          </span>
        </Label>
        <div
          className={`relative border-2 border-dashed rounded-lg p-8 ${
            isDragging
              ? "border-blue-500 bg-blue-50"
              : "hover:border-blue-400 hover:bg-blue-50"
          } transition-colors duration-200`}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <Input
            id="fileUpload"
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            className="hidden"
          />
          <label
            htmlFor="fileUpload"
            className="flex flex-col items-center justify-center cursor-pointer"
          >
            <Upload className="h-12 w-12 text-gray-400 mb-4" />
            <span className="text-sm text-gray-600">
              Click to upload or drag and drop CSV file
            </span>
          </label>
        </div>
      </div>

      {selectedFile && (
        <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg animate-fade-in">
          <span className="text-sm text-gray-600 truncate max-w-[80%]">
            {selectedFile.name}
          </span>
          <button
            onClick={onFileRemove}
            className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors duration-200"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      )}
    </div>
  );
}
