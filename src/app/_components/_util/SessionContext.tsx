/* eslint-disable no-unused-vars */
import React from "react";
import { Run } from "../_experiments/types";
import { Roles } from "../_access/types";

interface SessionContextInterface {
  runs: Run[];
  roles: Roles;
  awaitingAccess: boolean;
  setRuns: (runs: Run[]) => void;
  setRoles: (roles: Roles) => void;
  setAwaitingAccess: (awaitingAccess: boolean) => void;
}

const SessionContext = React.createContext<SessionContextInterface>({
  runs: [],
  roles: { roles: [] },
  awaitingAccess: true,
  setRuns: () => {},
  setRoles: () => {},
  setAwaitingAccess: () => {},
});

export const SessionContextProvider = SessionContext.Provider;
export default SessionContext;
