import { CheckIcon } from "@heroicons/react/24/solid";
import { useEffect, useState } from "react";

interface CheckboxProps {
  selected: boolean;
  toggleSelected?: () => void;
}

const Checkbox = ({ selected, toggleSelected }: CheckboxProps) => {
  const [currSelected, setCurrSelected] = useState(selected);

  useEffect(() => {
    setCurrSelected(selected);
  }, [selected]);

  function onSelect() {
    if (toggleSelected) toggleSelected();
  }

  return (
    <div
      role="checkbox"
      aria-checked={currSelected}
      className={`flex w-5 h-5 shrink-0 rounded-sm border justify-center items-center hover:cursor-pointer ${currSelected ? "border-primary bg-[#CCCEFA]/30" : "border-card-border bg-white"}`}
      onMouseDown={() => {
        onSelect();
      }}
    >
      <CheckIcon
        className={`${currSelected ? "block stroke-2 stroke-primary w-4 h-4" : "hidden"}`}
      />
    </div>
  );
};

export default Checkbox;
