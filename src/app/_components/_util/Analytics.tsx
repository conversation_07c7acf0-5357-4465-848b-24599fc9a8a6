"use client";
import Script from "next/script";

const GoogleAnalytics = () => {
  return (
    <>
      <Script src="https://www.googletagmanager.com/gtag/js?id=G-52WK8DDZLF" />
      <Script id="google-analytics">
        {`
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
        
                  gtag('config', 'G-52WK8DDZLF', { 'debug_mode':true });
                `}
      </Script>
      <Script id="google-tag-manager" strategy="afterInteractive">
        {`
                  (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                  })(window,document,'script','dataLayer','GTM-WJGCMSX2}');
                `}
      </Script>
    </>
  );
};

interface TagUserProps {
  email: string | undefined | null;
}

const TagUser = ({ email }: TagUserProps) => {
  return (
    <Script id="tag-user">
      {`
                window.dataLayer = window.dataLayer || [];
                    window.dataLayer.push({
                    user_id: '${email}'
                });
                gtag('config', 'G-52WK8DDZLF', { 'user_id':'${email}' });
            `}
    </Script>
  );
};

const LogAskQuestionEvent = (email: string | null | undefined) => {
  return (
    <Script id="log-question-asked">
      {`gtag('event', 'question_asked', {
                event_category: 'engagement',
                event_label: 'question_asked',
                user_id: '${email}'
            });`}
    </Script>
  );
};

const LogRunExperimentEvent = ({ email }: TagUserProps) => {
  return (
    <Script id="log-experiment-run">
      {`gtag('event', 'run_experiment'), {
                user_id: '${email}'
            };)`}
    </Script>
  );
};

export { LogAskQuestionEvent, LogRunExperimentEvent, TagUser };
export default GoogleAnalytics;
