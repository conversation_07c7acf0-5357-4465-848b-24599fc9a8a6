/* eslint-disable no-unused-vars */
const LABELS: LabelProps[] = [
  {
    type: "Public Policy",
    textColor: "#0C5394",
    bgColor: "#E9F6FF",
  },
  {
    type: "Market Research",
    textColor: "#E45301",
    bgColor: "#FFF2E6",
  },
  {
    type: "Sales Management",
    textColor: "#7900C2",
    bgColor: "#F3EAFF",
  },
  {
    type: "Product Management",
    textColor: "#078B04",
    bgColor: "#E9FCE3",
  },
];

interface LabelProps {
  type: string;
  textColor: string;
  bgColor: string;
}

const Label = ({ type, textColor, bgColor }: LabelProps) => {
  return (
    <div
      className="px-2 py-1 rounded-md"
      style={{ backgroundColor: `${bgColor}` }}
    >
      <p
        className="font-roboto text-base font-medium"
        style={{ color: `${textColor}` }}
      >
        {type}
      </p>
    </div>
  );
};

export default Label;
