import React from "react";
import { render } from "@testing-library/react";

import GoogleAnalytics, {
  TagUser,
  LogAskQuestionEvent,
  LogRunExperimentEvent,
} from "../Analytics"; // Adjust the import path as necessary

describe("GoogleAnalytics Component", () => {
  test("renders Google Analytics script tags", () => {
    const { getByText, getByTestId } = render(<GoogleAnalytics />);

    // Check if the Google Analytics script tag is present
    expect(
      document.querySelector(
        'script[src="https://www.googletagmanager.com/gtag/js?id=G-52WK8DDZLF"]'
      )
    ).toBeInTheDocument();
    expect(
      document.querySelector('script[id="google-analytics"]')
    ).toBeInTheDocument();
    expect(
      document.querySelector('script[id="google-tag-manager"]')
    ).toBeInTheDocument();
  });
});

describe("TagUser Component", () => {
  test("renders script tag with user email", () => {
    const email = "<EMAIL>";
    const { getByText, getByTestId } = render(<TagUser email={email} />);

    expect(document.querySelector('script[id="tag-user"]')).toHaveTextContent(
      `user_id: '${email}'`
    );
  });
});
