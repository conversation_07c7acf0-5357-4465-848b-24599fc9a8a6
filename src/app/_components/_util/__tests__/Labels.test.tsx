import React from "react";
import { render, screen } from "@testing-library/react";
import Label from "../Labels";

describe("Label Component", () => {
  const LABELS = [
    {
      type: "Public Policy",
      textColor: "#0C5394",
      bgColor: "#E9F6FF",
    },
    {
      type: "Market Research",
      textColor: "#E45301",
      bgColor: "#FFF2E6",
    },
    {
      type: "Sales Management",
      textColor: "#7900C2",
      bgColor: "#F3EAFF",
    },
    {
      type: "Product Management",
      textColor: "#078B04",
      bgColor: "#E9FCE3",
    },
  ];

  LABELS.forEach(({ type, textColor, bgColor }) => {
    test(`renders ${type} label with correct styles`, () => {
      render(<Label type={type} textColor={textColor} bgColor={bgColor} />);

      const labelElement = screen.getByText(type);

      // Verify the text content
      expect(labelElement).toBeInTheDocument();
      expect(labelElement).toHaveTextContent(type);

      // Verify the text color
      expect(labelElement).toHaveStyle(`color: ${textColor}`);

      // Verify the background color
      expect(labelElement.parentElement).toHaveStyle(
        `background-color: ${bgColor}`
      );
    });
  });
});
