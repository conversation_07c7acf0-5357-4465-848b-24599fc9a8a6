import React from "react";
import { render, screen } from "@testing-library/react";
import <PERSON><PERSON><PERSON>hart from "../AmceChart"; // Adjust the import path as necessary
import { VegaLite } from "react-vega";

// Mock the VegaLite component
jest.mock("react-vega", () => ({
  VegaLite: jest.fn(() => <div data-testid="vega-lite-mock"></div>),
}));

describe("AmceChart Component", () => {
  const spec = {
    // Sample Vega-Lite specification
    description: "A simple bar chart with embedded data.",
    data: {
      values: [
        { a: "A", b: 28 },
        { a: "B", b: 55 },
        { a: "C", b: 43 },
      ],
    },
    mark: "bar",
    encoding: {
      x: { field: "a", type: "nominal" },
      y: { field: "b", type: "quantitative" },
    },
  };

  test("renders VegaLite component with provided spec", () => {
    render(<AmceChart spec={spec} />);

    const vegaLiteElement = screen.getByTestId("vega-lite-mock");
    expect(vegaLiteElement).toBeInTheDocument();

    expect(VegaLite).toHaveBeenCalledWith({ spec }, {});
  });
});
