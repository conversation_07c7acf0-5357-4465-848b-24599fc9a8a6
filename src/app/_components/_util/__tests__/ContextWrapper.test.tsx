import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";

import Context<PERSON>rapper from "../ContextWrappers"; // Adjust the import path as necessary
import SessionContext from "../SessionContext";

describe("ContextWrapper Component", () => {
  test("provides context values to its children", () => {
    // Dummy child component to consume context values
    const TestComponent = () => {
      const context = React.useContext(SessionContext);
      return (
        <div>
          <div data-testid="runs">{JSON.stringify(context.runs)}</div>
          <div data-testid="roles">{JSON.stringify(context.roles)}</div>
          <div data-testid="awaitingAccess">{`${context.awaitingAccess}`}</div>
        </div>
      );
    };

    render(
      <ContextWrapper>
        <TestComponent />
      </ContextWrapper>
    );

    // Assert initial context values
    expect(screen.getByTestId("runs")).toHaveTextContent("[]");
    expect(screen.getByTestId("roles")).toHaveTextContent('{"roles":[]}');
    expect(screen.getByTestId("awaitingAccess")).toHaveTextContent("true");
  });

  test("provides context setter functions to its children", async () => {
    const TestComponent = () => {
      const context = React.useContext(SessionContext);
      return (
        <div>
          <button
            data-testid="setRunsButton"
            onClick={() => context.setRuns([{ id: 1, name: "Test Run" }])}
          >
            Set Runs
          </button>
          <button
            data-testid="setRolesButton"
            onClick={() => context.setRoles({ roles: ["admin"] })}
          >
            Set Roles
          </button>
          <button
            data-testid="setAwaitingAccessButton"
            onClick={() => context.setAwaitingAccess(false)}
          >
            Set Awaiting Access
          </button>
          <div data-testid="runs">{JSON.stringify(context.runs)}</div>
          <div data-testid="roles">{JSON.stringify(context.roles)}</div>
          <div data-testid="awaitingAccess">{`${context.awaitingAccess}`}</div>
        </div>
      );
    };

    render(
      <ContextWrapper>
        <TestComponent />
      </ContextWrapper>
    );

    // Simulate context setter function calls
    fireEvent.click(screen.getByTestId("setRunsButton"));
    fireEvent.click(screen.getByTestId("setRolesButton"));
    fireEvent.click(screen.getByTestId("setAwaitingAccessButton"));

    // Assert updated context values using waitFor
    await waitFor(() => {
      expect(screen.getByTestId("runs")).toHaveTextContent(
        JSON.stringify([{ id: 1, name: "Test Run" }])
      );
    });
    await waitFor(() => {
      expect(screen.getByTestId("roles")).toHaveTextContent(
        JSON.stringify({ roles: ["admin"] })
      );
    });
    await waitFor(() => {
      expect(screen.getByTestId("awaitingAccess")).toHaveTextContent("false");
    });
  });
});
