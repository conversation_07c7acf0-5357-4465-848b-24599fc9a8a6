import { render, fireEvent } from "@testing-library/react";
import { describe, expect } from "@jest/globals";
import Checkbox from "../Checkbox";

describe("Checkbox", () => {
  it("renders unchecked by default", () => {
    const { getByRole } = render(<Checkbox selected={false} />);
    const checkbox = getByRole("checkbox");
    expect(checkbox).not.toBeChecked();
  });

  it("renders checked when selected is true", () => {
    const { getByRole } = render(<Checkbox selected={true} />);
    const checkbox = getByRole("checkbox");
    expect(checkbox).toBeChecked();
  });
});
