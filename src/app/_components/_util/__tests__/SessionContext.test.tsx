import React, { useContext } from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import SessionContext, { SessionContextProvider } from "../SessionContext";
import { Run } from "../../_experiments/types";
import { Roles } from "../../_access/types";

// Test component to consume the context
const TestComponent = () => {
  const context = useContext(SessionContext);

  return (
    <div>
      <div data-testid="runs">{JSON.stringify(context.runs)}</div>
      <div data-testid="roles">{JSON.stringify(context.roles)}</div>
      <div data-testid="awaitingAccess">
        {JSON.stringify(context.awaitingAccess)}
      </div>
      <button
        data-testid="setRunsButton"
        onClick={() =>
          context.setRuns([
            {
              id: "1",
              name: "Test Run",
              state: "",
              created_at: new Date(),
              question: "",
              task_count: 0,
              confidence: "",
              survey_prompt: "",
              amce_filename: "",
            },
          ])
        }
      >
        Set Runs
      </button>
      <button
        data-testid="setRolesButton"
        onClick={() =>
          context.setRoles({
            roles: ["admin"],
          })
        }
      >
        Set Roles
      </button>
      <button
        data-testid="setAwaitingAccessButton"
        onClick={() => context.setAwaitingAccess(false)}
      >
        Set Awaiting Access
      </button>
    </div>
  );
};

// Test suite
describe("SessionContextProvider Component", () => {
  test("provides default context values to its children", () => {
    render(
      <SessionContextProvider
        value={{
          runs: [],
          roles: { roles: [] },
          awaitingAccess: true,
          setRuns: () => {},
          setRoles: () => {},
          setAwaitingAccess: () => {},
        }}
      >
        <TestComponent />
      </SessionContextProvider>
    );

    // Assert default context values
    expect(screen.getByTestId("runs")).toHaveTextContent("[]");
    expect(screen.getByTestId("roles")).toHaveTextContent(
      JSON.stringify({ roles: [] })
    );
    expect(screen.getByTestId("awaitingAccess")).toHaveTextContent("true");
  });

  test("provides context setter functions to its children", () => {
    const setRuns = jest.fn();
    const setRoles = jest.fn();
    const setAwaitingAccess = jest.fn();

    render(
      <SessionContextProvider
        value={{
          runs: [],
          roles: { roles: [] },
          awaitingAccess: true,
          setRuns,
          setRoles,
          setAwaitingAccess,
        }}
      >
        <TestComponent />
      </SessionContextProvider>
    );

    // Trigger the setter functions
    fireEvent.click(screen.getByTestId("setRunsButton"));
    fireEvent.click(screen.getByTestId("setRolesButton"));
    fireEvent.click(screen.getByTestId("setAwaitingAccessButton"));

    // Assert setter functions were called with correct arguments
    expect(setRuns).toHaveBeenCalledWith([
      {
        id: "1",
        name: "Test Run",
        state: "",
        created_at: expect.any(Date),
        question: "",
        task_count: 0,
        confidence: "",
        survey_prompt: "",
        amce_filename: "",
      },
    ]);
    expect(setRoles).toHaveBeenCalledWith({ roles: ["admin"] });
    expect(setAwaitingAccess).toHaveBeenCalledWith(false);
  });
});
