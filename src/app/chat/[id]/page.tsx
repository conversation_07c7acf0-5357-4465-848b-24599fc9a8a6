"use client";

import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Bot,
  User,
  Loader2,
  AlertCircle,
  Brain,
  BarChart3,
  ArrowLeft,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { PaperAirplaneIcon } from "@heroicons/react/24/outline";

const SimpleMarkdownRenderer = ({ content }: { content: string }) => {
  // Convert markdown tables to HTML
  const processTable = (text: string) => {
    const lines = text.split("\n");
    let inTable = false;
    let tableRows: string[] = [];
    let result: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line.includes("|") && line.length > 1) {
        if (!inTable) {
          inTable = true;
          tableRows = [];
        }
        tableRows.push(line);
      } else {
        if (inTable) {
          // Process the table
          if (tableRows.length > 2) {
            let tableHtml =
              '<div class="overflow-x-auto my-6"><table class="min-w-full border border-gray-200 rounded-lg">';

            // Header
            const headerCells = tableRows[0]
              .split("|")
              .map((cell) => cell.trim())
              .filter((cell) => cell);
            tableHtml += '<thead class="bg-gray-50"><tr>';
            headerCells.forEach((cell) => {
              tableHtml += `<th class="px-4 py-3 text-left text-xs font-semibold text-gray-900 border border-gray-300">${cell.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")}</th>`;
            });
            tableHtml += "</tr></thead><tbody>";

            // Data rows (skip header and separator)
            for (let j = 2; j < tableRows.length; j++) {
              const cells = tableRows[j]
                .split("|")
                .map((cell) => cell.trim())
                .filter((cell) => cell);
              if (cells.length > 0) {
                tableHtml += "<tr>";
                cells.forEach((cell) => {
                  tableHtml += `<td class="px-4 py-3 text-sm text-gray-700 border border-gray-200">${cell.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')}</td>`;
                });
                tableHtml += "</tr>";
              }
            }
            tableHtml += "</tbody></table></div>";
            result.push(tableHtml);
          }
          inTable = false;
          tableRows = [];
        }
        result.push(line);
      }
    }

    return result.join("\n");
  };

  // Basic markdown parsing
  let processedContent = processTable(content)
    // Bold text
    .replace(
      /\*\*(.*?)\*\*/g,
      '<strong class="font-bold text-gray-900">$1</strong>'
    )
    // Headers
    .replace(
      /^### (.*?)$/gm,
      '<h3 class="text-lg font-bold text-gray-900 mt-6 mb-3 pb-2 border-b border-gray-200">$1</h3>'
    )
    .replace(
      /^#### (.*?)$/gm,
      '<h4 class="text-base font-semibold text-gray-800 mt-4 mb-2">$1</h4>'
    )
    // Horizontal rules
    .replace(/^---$/gm, '<hr class="my-6 border-gray-300" />')
    // List items (numbered and bulleted)
    .replace(
      /^\d+\.\s+(.*?)$/gm,
      '<li class="text-gray-700 leading-relaxed">$1</li>'
    )
    .replace(
      /^-\s+(.*?)$/gm,
      '<li class="text-gray-700 leading-relaxed list-disc ml-4">$1</li>'
    )
    // Paragraphs
    .replace(
      /^([^<\n-\d].+)$/gm,
      '<p class="mb-3 text-gray-700 leading-relaxed">$1</p>'
    )
    // Line breaks
    .replace(/\n/g, "<br />");

  // Clean up multiple br tags
  processedContent = processedContent.replace(/(<br\s*\/?>\s*){2,}/g, "<br />");

  return (
    <div
      className="prose prose-sm max-w-none leading-relaxed text-gray-800"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
};

interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  metadata?: {
    answeredAt?: string;
    userQuestionId?: string;
    screenshotCount?: number;
    hasQuestion?: boolean;
  };
}

const ExperimentChatPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const experimentId = params.id as string;

  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingStep, setLoadingStep] = useState(0);
  const [experimentTitle, setExperimentTitle] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Get experiment title from URL parameters
  useEffect(() => {
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);
      const title = urlParams.get("title");
      setExperimentTitle(title || "Untitled Experiment");
    }
  }, []);

  const loadingSteps = [
    { icon: Brain, text: "Analyzing experiment data..." },
    { icon: BarChart3, text: "Processing statistical results..." },
    { icon: Bot, text: "Generating insights..." },
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Auto-start the analysis when page loads
  useEffect(() => {
    if (experimentId && messages.length === 0 && !isLoading) {
      handleGetStarted();
    }
  }, [experimentId]);

  // Loading step animation
  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (isLoading) {
      setLoadingStep(0);
      interval = setInterval(() => {
        setLoadingStep((prev) => (prev + 1) % loadingSteps.length);
      }, 10000); // Change step every 1 min
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isLoading, loadingSteps.length]);

  const analyzeExperiment = async (question?: string) => {
    try {
      setError(null);

      const response = await fetch(
        "https://analyzeexperiment-dvftbudl6q-uc.a.run.app",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            experimentId,
            ...(question && { question }),
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        return {
          analysis: result.analysis,
          metadata: result.metadata,
        };
      } else {
        throw new Error(result.error || "Analysis failed");
      }
    } catch (error) {
      console.error("Request failed:", error);
      throw error;
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    try {
      const { analysis, metadata } = await analyzeExperiment(
        userMessage.content
      );

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: analysis,
        timestamp: new Date(),
        metadata,
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      setError((error as Error).message);

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content:
          "I apologize, but I encountered an error while analyzing your experiment. Please try again later.",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = "auto";
    const scrollHeight = textarea.scrollHeight;
    const maxHeight = 120; // max-h-[120px]
    textarea.style.height = Math.min(scrollHeight, maxHeight) + "px";
  };

  const handleGetStarted = async () => {
    setIsLoading(true);
    setError(null);

    const welcomeMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: "Please analyze this experiment and provide key insights.",
      timestamp: new Date(),
    };

    setMessages([welcomeMessage]);

    try {
      const { analysis, metadata } = await analyzeExperiment();

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: analysis,
        timestamp: new Date(),
        metadata,
      };

      setMessages([welcomeMessage, assistantMessage]);
    } catch (error) {
      setError((error as Error).message);

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content:
          "I apologize, but I encountered an error while analyzing your experiment. Please try again later.",
        timestamp: new Date(),
      };

      setMessages([welcomeMessage, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Markdown renderer component
  const MarkdownContent = ({ content }: { content: string }) => {
    return <SimpleMarkdownRenderer content={content} />;
  };

  const handleBackClick = () => {
    router.push("/experiments");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackClick}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Experiments</span>
            </Button>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Bot className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Experiment Analysis Chat
                </h1>
                <p className="text-sm text-gray-600 truncate max-w-md">
                  {experimentTitle}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[calc(100vh-200px)] flex flex-col">
          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4 min-h-0">
            {messages.length === 0 && !isLoading ? (
              <div className="flex flex-col items-center justify-center h-full text-center space-y-6">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                  <Bot className="h-8 w-8 text-primary" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Starting Analysis...
                  </h3>
                  <p className="text-gray-600 max-w-md">
                    Initializing AI analysis of your experiment results.
                  </p>
                </div>
              </div>
            ) : (
              <>
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex space-x-3",
                      message.type === "user" ? "justify-end" : "justify-start"
                    )}
                  >
                    {message.type === "assistant" && (
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <Bot className="h-4 w-4 text-primary" />
                        </div>
                      </div>
                    )}

                    <Card
                      className={cn(
                        "max-w-3xl",
                        message.type === "user"
                          ? "bg-primary text-white border-primary"
                          : "bg-white"
                      )}
                    >
                      <CardContent className="p-4">
                        <div className="space-y-2">
                          {message.type === "assistant" ? (
                            <div className="text-sm">
                              <MarkdownContent content={message.content} />
                            </div>
                          ) : (
                            <p className="text-sm whitespace-pre-wrap leading-relaxed">
                              {message.content}
                            </p>
                          )}
                          <div className="flex items-center justify-between">
                            <span
                              className={cn(
                                "text-xs",
                                message.type === "user"
                                  ? "text-gray-100"
                                  : "text-gray-500"
                              )}
                            >
                              {formatTime(message.timestamp)}
                            </span>
                            {message.metadata && (
                              <div className="flex items-center space-x-2">
                                {message.metadata.screenshotCount && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {message.metadata.screenshotCount}{" "}
                                    screenshots analyzed
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {message.type === "user" && (
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {isLoading && (
                  <div className="flex justify-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        {React.createElement(loadingSteps[loadingStep].icon, {
                          className: "h-4 w-4 text-primary",
                        })}
                      </div>
                    </div>
                    <Card className="bg-white">
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <Loader2 className="h-4 w-4 animate-spin text-primary" />
                          <div className="space-y-1">
                            <span className="text-sm text-gray-700 font-medium">
                              {loadingSteps[loadingStep].text}
                            </span>
                            <div className="flex space-x-1">
                              {loadingSteps.map((_, index) => (
                                <div
                                  key={index}
                                  className={cn(
                                    "h-1 w-6 rounded-full transition-colors duration-500",
                                    index <= loadingStep
                                      ? "bg-primary"
                                      : "bg-gray-200"
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {error && (
                  <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
                    <AlertCircle className="h-5 w-5" />
                    <span className="text-sm">{error}</span>
                  </div>
                )}
              </>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area - Always visible */}
          <div className="border-t border-gray-200 bg-gray-50 p-4 flex-shrink-0">
            <div className="relative">
              <div className="flex items-end space-x-3 bg-white rounded-xl border border-gray-200 shadow-sm p-3 focus-within:border-primary focus-within:ring-1 focus-within:ring-primary transition-all duration-200">
                <div className="flex-1 min-h-0">
                  <textarea
                    ref={textareaRef}
                    value={inputValue}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyPress}
                    placeholder="Ask a question about your experiment..."
                    className="w-full resize-none border-0 bg-transparent px-0 py-2 text-sm placeholder-gray-400 focus:outline-none focus:ring-0 min-h-[40px] max-h-[120px]"
                    rows={1}
                    disabled={isLoading}
                    style={{
                      scrollbarWidth: "thin",
                      scrollbarColor: "#e5e7eb transparent",
                    }}
                  />
                </div>
                <button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  className="group relative flex h-10 w-10 items-center justify-center rounded-full bg-primary text-white transition-all duration-200 hover:bg-primary-dark disabled:bg-gray-300 disabled:hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <PaperAirplaneIcon className="h-4 w-4 transition-transform group-hover:scale-110" />
                  )}
                </button>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-3 px-1">
              Press Enter to send • Shift+Enter for new line
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExperimentChatPage;
