"use client";

/**
 * React hooks for form validation using Zod schemas
 * These hooks provide easy integration with React components and state management
 */

import { useState, useCallback, useEffect, useMemo } from "react";
import { z } from "zod";
import { validateFormFields } from "./utils";

// Form validation hook
export function useFormValidation<T extends z.ZodTypeAny>(
  schema: T,
  initialValues: Partial<z.infer<T>> = {},
  options: {
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    validateOnSubmit?: boolean;
    resetOnSuccess?: boolean;
    focusOnError?: boolean;
  } = {}
) {
  const {
    validateOnChange = false,
    validateOnBlur = true,
    validateOnSubmit = true,
    resetOnSuccess = false,
    focusOnError = true,
  } = options;

  // Form state
  const [values, setValues] = useState<Partial<z.infer<T>>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(false);

  // Validate form data
  const validate = useCallback(
    (data: Partial<z.infer<T>>) => {
      const result = validateFormFields(data, schema);

      if (result.success) {
        setErrors({});
        setIsValid(true);
        return { isValid: true, data: result.data };
      } else {
        setErrors(result.errors);
        setIsValid(false);
        return { isValid: false, errors: result.errors };
      }
    },
    [schema]
  );

  // Update field value
  const setValue = useCallback(
    (name: keyof z.infer<T>, value: any) => {
      setValues((prev) => ({ ...prev, [name]: value }));

      if (validateOnChange) {
        const newValues = { ...values, [name]: value };
        validate(newValues);
      }
    },
    [values, validateOnChange, validate]
  );

  // Set multiple values
  const setMultipleValues = useCallback(
    (newValues: Partial<z.infer<T>>) => {
      setValues(newValues);

      if (validateOnChange) {
        validate(newValues);
      }
    },
    [validateOnChange, validate]
  );

  // Handle field blur
  const handleBlur = useCallback(
    (name: keyof z.infer<T>) => {
      setTouched((prev) => ({ ...prev, [name]: true }));

      if (validateOnBlur) {
        validate(values);
      }
    },
    [values, validateOnBlur, validate]
  );

  // Handle form submission
  const handleSubmit = useCallback(
    async (onSubmit: (data: z.infer<T>) => Promise<void> | void) => {
      setIsSubmitting(true);

      try {
        if (validateOnSubmit) {
          const result = validate(values);
          if (!result.isValid) {
            if (focusOnError) {
              // Focus on first error field
              const firstErrorField = Object.keys(result.errors || {})[0];
              if (firstErrorField) {
                const element = document.querySelector(
                  `[name="${firstErrorField}"]`
                ) as HTMLElement;
                element?.focus();
              }
            }
            return;
          }
          await onSubmit(result.data);
        } else {
          await onSubmit(values as z.infer<T>);
        }

        if (resetOnSuccess) {
          reset();
        }
      } finally {
        setIsSubmitting(false);
      }
    },
    [values, validate, validateOnSubmit, focusOnError, resetOnSuccess]
  );

  // Reset form
  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsValid(false);
  }, [initialValues]);

  // Get field props for easy integration
  const getFieldProps = useCallback(
    (name: keyof z.infer<T>) => ({
      name: name as string,
      value: values[name] || "",
      onChange: (
        e: React.ChangeEvent<
          HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
        >
      ) => {
        setValue(name, e.target.value);
      },
      onBlur: () => handleBlur(name),
      error: touched[name as string] ? errors[name as string] : undefined,
      "aria-invalid":
        touched[name as string] && errors[name as string] ? "true" : "false",
    }),
    [values, errors, touched, setValue, handleBlur]
  );

  // Get field error
  const getFieldError = useCallback(
    (name: keyof z.infer<T>) => {
      return touched[name as string] ? errors[name as string] : undefined;
    },
    [errors, touched]
  );

  // Check if field has error
  const hasFieldError = useCallback(
    (name: keyof z.infer<T>) => {
      return Boolean(touched[name as string] && errors[name as string]);
    },
    [errors, touched]
  );

  return {
    // State
    values,
    errors,
    touched,
    isSubmitting,
    isValid,

    // Actions
    setValue,
    setValues: setMultipleValues,
    handleBlur,
    handleSubmit,
    reset,
    validate,

    // Helpers
    getFieldProps,
    getFieldError,
    hasFieldError,
  };
}

// Async validation hook
export function useAsyncValidation<T>(
  validationFn: (value: T) => Promise<{ isValid: boolean; error?: string }>,
  debounceMs: number = 300
) {
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [isValid, setIsValid] = useState<boolean | undefined>();

  const validate = useCallback(
    async (value: T) => {
      setIsValidating(true);
      setError(undefined);

      try {
        const result = await validationFn(value);
        setIsValid(result.isValid);
        setError(result.error);
      } catch (err) {
        setIsValid(false);
        setError(err instanceof Error ? err.message : "Validation failed");
      } finally {
        setIsValidating(false);
      }
    },
    [validationFn]
  );

  // Debounced validation
  const debouncedValidate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return (value: T) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => validate(value), debounceMs);
    };
  }, [validate, debounceMs]);

  return {
    isValidating,
    error,
    isValid,
    validate,
    debouncedValidate,
  };
}

// Schema validation hook
export function useSchemaValidation<T extends z.ZodTypeAny>(
  schema: T,
  data: unknown
) {
  const [result, setResult] = useState<{
    isValid: boolean;
    data?: z.infer<T>;
    errors?: Record<string, string>;
  }>({ isValid: false });

  useEffect(() => {
    const validationResult = validateFormFields(data, schema);
    setResult(validationResult);
  }, [data, schema]);

  return result;
}

// Field validation hook for individual fields
export function useFieldValidation<T extends z.ZodTypeAny>(
  schema: T,
  initialValue: z.infer<T> = "" as z.infer<T>,
  options: {
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    debounceMs?: number;
  } = {}
) {
  const {
    validateOnChange = false,
    validateOnBlur = true,
    debounceMs = 300,
  } = options;

  const [value, setValue] = useState<z.infer<T>>(initialValue);
  const [error, setError] = useState<string | undefined>();
  const [touched, setTouched] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Validate field value
  const validate = useCallback(
    (val: z.infer<T>) => {
      setIsValidating(true);

      try {
        schema.parse(val);
        setError(undefined);
        return true;
      } catch (err) {
        if (err instanceof z.ZodError) {
          setError(err.errors[0]?.message || "Invalid value");
        } else {
          setError("Validation failed");
        }
        return false;
      } finally {
        setIsValidating(false);
      }
    },
    [schema]
  );

  // Debounced validation
  const debouncedValidate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return (val: z.infer<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => validate(val), debounceMs);
    };
  }, [validate, debounceMs]);

  // Handle value change
  const handleChange = useCallback(
    (newValue: z.infer<T>) => {
      setValue(newValue);

      if (validateOnChange) {
        if (debounceMs > 0) {
          debouncedValidate(newValue);
        } else {
          validate(newValue);
        }
      }
    },
    [validateOnChange, validate, debouncedValidate, debounceMs]
  );

  // Handle field blur
  const handleBlur = useCallback(() => {
    setTouched(true);

    if (validateOnBlur) {
      validate(value);
    }
  }, [validateOnBlur, validate, value]);

  // Reset field
  const reset = useCallback(() => {
    setValue(initialValue);
    setError(undefined);
    setTouched(false);
    setIsValidating(false);
  }, [initialValue]);

  return {
    value,
    error: touched ? error : undefined,
    touched,
    isValidating,
    isValid: !error && touched,
    setValue: handleChange,
    onBlur: handleBlur,
    reset,
    validate: () => validate(value),
  };
}

// Multi-step form validation hook
export function useMultiStepValidation<T extends Record<string, z.ZodTypeAny>>(
  schemas: T,
  initialData: Partial<{ [K in keyof T]: z.infer<T[K]> }> = {}
) {
  const [currentStep, setCurrentStep] = useState(0);
  const [data, setData] = useState(initialData);
  const [errors, setErrors] = useState<Record<string, Record<string, string>>>(
    {}
  );
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  const steps = Object.keys(schemas);
  const totalSteps = steps.length;

  // Validate specific step
  const validateStep = useCallback(
    (stepIndex: number) => {
      const stepKey = steps[stepIndex];
      const stepSchema = schemas[stepKey];
      const stepData = data[stepKey];

      const result = validateFormFields(stepData, stepSchema);

      if (result.success) {
        setErrors((prev) => ({ ...prev, [stepKey]: {} }));
        setCompletedSteps((prev) => new Set([...prev, stepIndex]));
        return true;
      } else {
        setErrors((prev) => ({ ...prev, [stepKey]: result.errors }));
        setCompletedSteps((prev) => {
          const newSet = new Set(prev);
          newSet.delete(stepIndex);
          return newSet;
        });
        return false;
      }
    },
    [steps, schemas, data]
  );

  // Update step data
  const updateStepData = useCallback(
    (stepKey: keyof T, stepData: Partial<z.infer<T[keyof T]>>) => {
      setData((prev) => ({
        ...prev,
        [stepKey]: { ...prev[stepKey], ...stepData },
      }));
    },
    []
  );

  // Go to next step
  const nextStep = useCallback(() => {
    if (validateStep(currentStep) && currentStep < totalSteps - 1) {
      setCurrentStep((prev) => prev + 1);
      return true;
    }
    return false;
  }, [currentStep, totalSteps, validateStep]);

  // Go to previous step
  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
      return true;
    }
    return false;
  }, [currentStep]);

  // Go to specific step
  const goToStep = useCallback(
    (stepIndex: number) => {
      if (stepIndex >= 0 && stepIndex < totalSteps) {
        setCurrentStep(stepIndex);
        return true;
      }
      return false;
    },
    [totalSteps]
  );

  // Check if form is complete
  const isComplete = completedSteps.size === totalSteps;

  // Get current step info
  const currentStepKey = steps[currentStep];
  const currentStepErrors = errors[currentStepKey] || {};

  return {
    // State
    currentStep,
    currentStepKey,
    data,
    errors,
    completedSteps: Array.from(completedSteps),
    isComplete,
    totalSteps,

    // Actions
    nextStep,
    prevStep,
    goToStep,
    updateStepData,
    validateStep,

    // Current step helpers
    currentStepErrors,
    isCurrentStepValid: completedSteps.has(currentStep),
    canGoNext: currentStep < totalSteps - 1,
    canGoPrev: currentStep > 0,
  };
}
