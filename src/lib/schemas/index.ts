/**
 * Centralized Zod Schema Library
 *
 * This file exports all validation schemas used throughout the application.
 * Schemas are organized by domain and provide both runtime validation and TypeScript types.
 */

// Re-export common schemas first (base schemas)
export * from "./common";

// Re-export form schemas
export * from "./forms";

// Re-export API schemas (for API requests/responses)
export * from "./api";

// Re-export experiment schemas (for experiment-specific logic)
export * from "./experiments";

// Re-export demographic schemas (for demographic-specific logic)
export * from "./demographics";

// Re-export validation schemas (for validation-specific logic)
export * from "./validation";

// Re-export utilities and hooks
export * from "./utils";
export * from "./hooks";

// Note: objects.ts contains duplicate schemas and is excluded to prevent conflicts
// Import specific schemas from objects.ts only when needed in individual files
