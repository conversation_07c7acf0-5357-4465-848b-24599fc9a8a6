/**
 * Example: Form Integration with Zod Validation
 * 
 * This file shows how to integrate the new Zod validation system
 * with existing form components and contexts.
 */

import React, { useState } from 'react';
import { 
  useFormValidation,
  DemographicTraitsFormSchema,
  ResearchQuestionFormSchema,
  ConceptTestingFormSchema,
  validateFormFields
} from '../index';

// Example 1: Enhanced Demographic Traits Form
export function EnhancedDemographicTraitsForm({ 
  onSubmit, 
  initialData = {} 
}: {
  onSubmit: (data: any) => void;
  initialData?: any;
}) {
  const {
    values,
    errors,
    touched,
    isValid,
    isSubmitting,
    setValue,
    getFieldProps,
    getFieldError,
    hasFieldError,
    handleSubmit,
    reset,
  } = useFormValidation(DemographicTraitsFormSchema, initialData, {
    validateOnBlur: true,
    validateOnChange: false,
    focusOnError: true,
  });

  const handleFormSubmit = async (validatedData: any) => {
    try {
      await onSubmit(validatedData);
      // Optionally reset form on success
      // reset();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleSubmit(handleFormSubmit);
    }} className="space-y-6">
      
      {/* Age Range Input */}
      <div className="form-group">
        <label htmlFor="age_range" className="form-label">
          Age Range
        </label>
        <div className="flex gap-2">
          <input
            type="number"
            placeholder="Min Age"
            value={values.age_range?.[0] || ''}
            onChange={(e) => setValue('age_range', [
              parseInt(e.target.value) || 0,
              values.age_range?.[1] || 65
            ])}
            onBlur={() => {}} // Handled by useFormValidation
            className={`form-input ${hasFieldError('age_range') ? 'error' : ''}`}
          />
          <input
            type="number"
            placeholder="Max Age"
            value={values.age_range?.[1] || ''}
            onChange={(e) => setValue('age_range', [
              values.age_range?.[0] || 18,
              parseInt(e.target.value) || 65
            ])}
            className={`form-input ${hasFieldError('age_range') ? 'error' : ''}`}
          />
        </div>
        {getFieldError('age_range') && (
          <span className="error-message">{getFieldError('age_range')}</span>
        )}
      </div>

      {/* Education Levels Multi-Select */}
      <div className="form-group">
        <label className="form-label">Education Levels</label>
        <div className="checkbox-group">
          {[
            'Less than high school',
            'High school graduate',
            'Some college',
            'Associate degree',
            'Bachelor\'s degree',
            'Master\'s degree',
            'Professional degree',
            'Doctoral degree',
          ].map((level) => (
            <label key={level} className="checkbox-label">
              <input
                type="checkbox"
                checked={values.education_levels?.includes(level) || false}
                onChange={(e) => {
                  const current = values.education_levels || [];
                  if (e.target.checked) {
                    setValue('education_levels', [...current, level]);
                  } else {
                    setValue('education_levels', current.filter(l => l !== level));
                  }
                }}
                className="checkbox-input"
              />
              {level}
            </label>
          ))}
        </div>
        {getFieldError('education_levels') && (
          <span className="error-message">{getFieldError('education_levels')}</span>
        )}
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={!isValid || isSubmitting}
        className={`btn-primary ${!isValid || isSubmitting ? 'disabled' : ''}`}
      >
        {isSubmitting ? 'Validating...' : 'Continue'}
      </button>

      {/* Debug Info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="debug-info">
          <details>
            <summary>Debug Info</summary>
            <pre>{JSON.stringify({ values, errors, isValid }, null, 2)}</pre>
          </details>
        </div>
      )}
    </form>
  );
}

// Example 2: Research Question Form with Real-time Validation
export function EnhancedResearchQuestionForm({ 
  onSubmit,
  onValidationChange 
}: {
  onSubmit: (data: any) => void;
  onValidationChange?: (isValid: boolean) => void;
}) {
  const {
    values,
    errors,
    isValid,
    isSubmitting,
    getFieldProps,
    handleSubmit,
  } = useFormValidation(ResearchQuestionFormSchema, {
    question: '',
    context: '',
    industry: '',
    target_audience: '',
  }, {
    validateOnChange: true, // Real-time validation
    validateOnBlur: true,
  });

  // Notify parent of validation state changes
  React.useEffect(() => {
    onValidationChange?.(isValid);
  }, [isValid, onValidationChange]);

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleSubmit(onSubmit);
    }} className="space-y-4">
      
      <div className="form-group">
        <label htmlFor="question" className="form-label required">
          Research Question
        </label>
        <textarea
          {...getFieldProps('question')}
          id="question"
          rows={3}
          placeholder="What would you like to research?"
          className="form-textarea"
        />
        {errors.question && (
          <span className="error-message">{errors.question}</span>
        )}
        <div className="field-hint">
          {values.question?.length || 0}/500 characters
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="context" className="form-label">
          Context (Optional)
        </label>
        <textarea
          {...getFieldProps('context')}
          id="context"
          rows={2}
          placeholder="Additional context about your research"
          className="form-textarea"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="form-group">
          <label htmlFor="industry" className="form-label">
            Industry
          </label>
          <input
            {...getFieldProps('industry')}
            id="industry"
            type="text"
            placeholder="e.g., Technology, Healthcare"
            className="form-input"
          />
        </div>

        <div className="form-group">
          <label htmlFor="target_audience" className="form-label">
            Target Audience
          </label>
          <input
            {...getFieldProps('target_audience')}
            id="target_audience"
            type="text"
            placeholder="e.g., Young professionals"
            className="form-input"
          />
        </div>
      </div>

      <button
        type="submit"
        disabled={!isValid || isSubmitting}
        className="btn-primary w-full"
      >
        {isSubmitting ? 'Processing...' : 'Continue'}
      </button>
    </form>
  );
}

// Example 3: Integration with existing Context/Store pattern
export function FormWithContext() {
  // This shows how to integrate with existing context patterns
  const [contextData, setContextData] = useState({});
  
  const {
    values,
    errors,
    isValid,
    handleSubmit,
    setValue,
  } = useFormValidation(ConceptTestingFormSchema);

  // Sync with existing context when form data changes
  React.useEffect(() => {
    if (isValid) {
      setContextData(values);
    }
  }, [values, isValid]);

  // Handle external data updates (e.g., from context)
  const handleExternalUpdate = (newData: any) => {
    // Validate external data before setting
    const result = validateFormFields(newData, ConceptTestingFormSchema);
    if (result.success) {
      // Update form with validated data
      Object.entries(result.data).forEach(([key, value]) => {
        setValue(key as any, value);
      });
    }
  };

  return (
    <div>
      {/* Form implementation */}
      <form onSubmit={(e) => {
        e.preventDefault();
        handleSubmit((data) => {
          console.log('Validated form data:', data);
          // Update context/store with validated data
          setContextData(data);
        });
      }}>
        {/* Form fields */}
      </form>
      
      {/* Show validation status */}
      <div className="validation-status">
        <span className={`status-indicator ${isValid ? 'valid' : 'invalid'}`}>
          {isValid ? '✓ Valid' : '✗ Invalid'}
        </span>
        {Object.keys(errors).length > 0 && (
          <div className="error-summary">
            <p>Please fix the following errors:</p>
            <ul>
              {Object.entries(errors).map(([field, error]) => (
                <li key={field}>{field}: {error}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

// Example 4: Custom validation hook for specific use cases
export function useCustomValidation() {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState(null);

  const validatePopulationTraits = async (traits: any) => {
    setIsValidating(true);
    
    try {
      // Use schema validation first
      const schemaResult = validateFormFields(traits, DemographicTraitsFormSchema);
      
      if (!schemaResult.success) {
        setValidationResults({
          isValid: false,
          errors: schemaResult.errors,
          source: 'schema'
        });
        return;
      }

      // Then do API validation if schema passes
      const response = await fetch('/api/validate-population-traits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(schemaResult.data),
      });

      const apiResult = await response.json();
      
      setValidationResults({
        isValid: apiResult.success,
        errors: apiResult.errors || {},
        data: apiResult.data,
        source: 'api'
      });
      
    } catch (error) {
      setValidationResults({
        isValid: false,
        errors: { _form: 'Validation failed' },
        source: 'error'
      });
    } finally {
      setIsValidating(false);
    }
  };

  return {
    isValidating,
    validationResults,
    validatePopulationTraits,
  };
}

// CSS classes for styling (add to your CSS file)
const styles = `
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-label.required::after {
  content: " *";
  color: red;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.form-input.error,
.form-textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 1px #ef4444;
}

.error-message {
  display: block;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.field-hint {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator.valid {
  color: #10b981;
}

.status-indicator.invalid {
  color: #ef4444;
}

.debug-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f3f4f6;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
}
`;
