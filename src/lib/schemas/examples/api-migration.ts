/**
 * Example: API Route Migration to Zod Validation
 * 
 * This file shows how to migrate existing API routes from manual validation
 * to use the centralized Zod schema validation system.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAccessToken, withApiAuthRequired } from '@auth0/nextjs-auth0';
import axios from 'axios';
import { 
  ProductCheckRequestSchema,
  ProductCheckResponseSchema,
  withValidation,
  createSuccessResponse,
  createErrorResponse,
  ValidationError
} from '../index';

// BEFORE: Manual validation (existing pattern)
export const POST_OLD = withApiAuthRequired(async function POST(
  req: NextRequest,
  context: {}
) {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());

    const body = await req.json();
    const why_prompt = body.why_prompt;

    // Manual validation - inconsistent and error-prone
    if (!why_prompt) {
      return NextResponse.json(
        { error: "why_prompt is required" },
        { status: 400 }
      );
    }

    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/market-simulator/check-realworld-product`,
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 10_000,
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error("Product Check Error:", error);
    return NextResponse.json(
      { error: "Failed to check product" },
      { status: 500 }
    );
  }
});

// AFTER: Zod validation (new pattern)
export const POST_NEW = withApiAuthRequired(
  withValidation(
    ProductCheckRequestSchema,
    async function POST(
      req: NextRequest,
      validatedData,
      context: {}
    ): Promise<NextResponse> {
      try {
        const { accessToken } = await getAccessToken(req, new NextResponse());

        // Data is already validated by withValidation wrapper
        const { why_prompt } = validatedData;

        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/market-simulator/check-realworld-product`,
          validatedData,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            timeout: 10_000,
            validateStatus: () => true,
          }
        );

        // Validate response data (optional but recommended)
        const validatedResponse = ProductCheckResponseSchema.parse(response.data);

        return createSuccessResponse(validatedResponse);
      } catch (error: unknown) {
        console.error("Product Check Error:", error);
        
        if (axios.isAxiosError(error)) {
          return createErrorResponse(
            "Backend service error",
            error.response?.status || 500,
            error.response?.data?.message || error.message
          );
        }
        
        return createErrorResponse(
          "Failed to check product",
          500,
          error instanceof Error ? error.message : "Unknown error"
        );
      }
    }
  )
);

// Alternative approach: Manual validation with schema
export const POST_MANUAL = withApiAuthRequired(async function POST(
  req: NextRequest,
  context: {}
): Promise<NextResponse> {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Manual validation using schema
    const body = await req.json();
    const validationResult = ProductCheckRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid request data",
          details: validationResult.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/market-simulator/check-realworld-product`,
      validatedData,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 10_000,
        validateStatus: () => true,
      }
    );

    return createSuccessResponse(response.data);
  } catch (error: unknown) {
    console.error("Product Check Error:", error);
    return createErrorResponse(
      "Failed to check product",
      500,
      error instanceof Error ? error.message : "Unknown error"
    );
  }
});

// Example: Form validation migration
import { useFormValidation } from '../hooks';
import { DemographicTraitsFormSchema } from '../forms';

// BEFORE: Manual form validation
function DemographicFormOld() {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  
  const validateForm = () => {
    const newErrors = {};
    
    // Manual validation - repetitive and error-prone
    if (!formData.age_range || formData.age_range.length !== 2) {
      newErrors.age_range = 'Age range is required';
    }
    
    if (!formData.education_levels || formData.education_levels.length === 0) {
      newErrors.education_levels = 'At least one education level is required';
    }
    
    // ... more manual validation
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      // Submit form
    }
  };
  
  // ... rest of component
}

// AFTER: Zod validation with hook
function DemographicFormNew() {
  const {
    values,
    errors,
    isValid,
    isSubmitting,
    getFieldProps,
    handleSubmit,
  } = useFormValidation(DemographicTraitsFormSchema, {
    age_range: [18, 65],
    income_range: [0, 100000],
    education_levels: [],
    genders: [],
    racial_groups: [],
    number_of_children: [],
  });
  
  const onSubmit = async (validatedData) => {
    // Data is already validated
    console.log('Submitting:', validatedData);
    // Submit to API
  };
  
  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleSubmit(onSubmit);
    }}>
      <input
        {...getFieldProps('age_range')}
        type="range"
        placeholder="Age Range"
      />
      {errors.age_range && <span className="error">{errors.age_range}</span>}
      
      {/* Other form fields with automatic validation */}
      
      <button type="submit" disabled={!isValid || isSubmitting}>
        {isSubmitting ? 'Submitting...' : 'Submit'}
      </button>
    </form>
  );
}

// Example: Migration checklist
/*
MIGRATION CHECKLIST:

1. API Routes:
   ✓ Import required schemas from @/lib/schemas
   ✓ Replace manual validation with schema validation
   ✓ Use withValidation wrapper or manual safeParse
   ✓ Standardize error responses using createErrorResponse
   ✓ Validate response data when needed
   ✓ Update error handling to be more specific

2. Forms:
   ✓ Import form schemas and hooks
   ✓ Replace manual validation with useFormValidation hook
   ✓ Update form fields to use getFieldProps
   ✓ Remove manual error state management
   ✓ Use schema-based validation messages

3. Components:
   ✓ Update TypeScript interfaces to use schema types
   ✓ Replace manual type checking with schema validation
   ✓ Use schema types for props and state
   ✓ Update error handling to use schema validation

4. Testing:
   ✓ Update tests to use schema validation
   ✓ Test validation error cases
   ✓ Test form validation flows
   ✓ Test API validation responses

5. Documentation:
   ✓ Update API documentation with schema definitions
   ✓ Document form validation patterns
   ✓ Add examples for common validation scenarios
   ✓ Update type documentation
*/
