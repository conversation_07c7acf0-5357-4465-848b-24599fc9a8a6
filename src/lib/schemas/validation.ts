/**
 * Validation-specific schemas and patterns
 * These schemas handle validation responses, error states, and validation contexts
 */

import { z } from "zod";
import {
  PositiveIntegerSchema,
  ErrorResponseSchema,
  SuccessResponseSchema,
} from "./common";
import {
  FinalSelectedPopulationTraitsSchema,
  OneTraitChangeTraitsSchema,
} from "./demographics";

// Validation option schema
export const ValidationOptionSchema = z.enum([
  "suggestion",
  "original",
  "one_trait_change",
]);

// Validation context schema
export const ValidationContextSchema = z.object({
  selectedOption: ValidationOptionSchema,
  selectedTrait: z.string().nullable(),
  validationResults: z.unknown().nullable(), // Will be refined based on context
  selectedPopulationTraits: FinalSelectedPopulationTraitsSchema.nullable(),
  populationSizeCount: PositiveIntegerSchema,
});

// Validation state schema
export const ValidationStateSchema = z.object({
  isLoading: z.boolean(),
  error: z.string().nullable(),
  hasResults: z.boolean(),
  lastValidated: z.string().datetime().optional(),
});

// Population validation request schema
export const PopulationValidationRequestSchema = z.object({
  age: z.tuple([z.number().int().min(0), z.number().int().min(0)]),
  education_level: z.array(z.string()).min(1),
  gender: z.array(z.string()).min(1),
  household_income: z.tuple([z.number().min(0), z.number().min(0)]),
  number_of_children: z.array(z.string()).min(1),
  number_of_records: PositiveIntegerSchema.default(300),
  racial_group: z.array(z.string()).min(1),
  state: z.string().nullable(),
});

// Population validation response schema
export const PopulationValidationResponseSchema = z.object({
  original: FinalSelectedPopulationTraitsSchema,
  suggestion: FinalSelectedPopulationTraitsSchema,
  one_trait_change: OneTraitChangeTraitsSchema,
});

// Attribute validation result schema
export const AttributeValidationResultSchema = z.object({
  attribute_id: z.string(),
  attribute_name: z.string(),
  is_valid: z.boolean(),
  errors: z.array(z.string()),
  warnings: z.array(z.string()).optional(),
  suggestions: z.array(z.string()).optional(),
});

// Level validation schema
export const LevelValidationSchema = z.object({
  level_id: z.string(),
  level_name: z.string(),
  is_valid: z.boolean(),
  errors: z.array(z.string()),
  warnings: z.array(z.string()).optional(),
});

// Trait validation result schema
export const TraitValidationResultSchema = z.object({
  trait_id: z.string(),
  trait_name: z.string(),
  is_valid: z.boolean(),
  errors: z.array(z.string()),
  warnings: z.array(z.string()).optional(),
  value_count: z.number().int().min(0),
  required_values: z.number().int().min(0),
});

// Form field validation schema
export const FieldValidationSchema = z.object({
  field_name: z.string(),
  is_valid: z.boolean(),
  value: z.unknown(),
  errors: z.array(z.string()),
  warnings: z.array(z.string()).optional(),
  touched: z.boolean().default(false),
  dirty: z.boolean().default(false),
});

// Form validation state schema
export const FormValidationStateSchema = z.object({
  is_valid: z.boolean(),
  is_submitting: z.boolean().default(false),
  has_errors: z.boolean(),
  fields: z.record(z.string(), FieldValidationSchema),
  global_errors: z.array(z.string()).optional(),
  touched_fields: z.array(z.string()).default([]),
  dirty_fields: z.array(z.string()).default([]),
});

// Validation result schema (generic)
export const ValidationResultSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    is_valid: z.boolean(),
    data: dataSchema.optional(),
    errors: z.array(
      z.object({
        field: z.string().optional(),
        message: z.string(),
        code: z.string().optional(),
        severity: z.enum(["error", "warning", "info"]).default("error"),
      })
    ),
    warnings: z
      .array(
        z.object({
          field: z.string().optional(),
          message: z.string(),
          code: z.string().optional(),
        })
      )
      .optional(),
    metadata: z
      .object({
        validation_time: z.string().datetime(),
        validator_version: z.string().optional(),
        rules_applied: z.array(z.string()).optional(),
      })
      .optional(),
  });

// Validation error detail schema
export const ValidationErrorDetailSchema = z.object({
  field: z.string().optional(),
  message: z.string(),
  code: z.string().optional(),
  severity: z.enum(["error", "warning", "info"]).default("error"),
  context: z.record(z.string(), z.unknown()).optional(),
  suggestion: z.string().optional(),
});

// Validation summary schema
export const ValidationSummarySchema = z.object({
  total_fields: z.number().int().min(0),
  valid_fields: z.number().int().min(0),
  invalid_fields: z.number().int().min(0),
  warnings_count: z.number().int().min(0),
  errors_count: z.number().int().min(0),
  validation_score: z.number().min(0).max(1).optional(),
  completion_percentage: z.number().min(0).max(100).optional(),
});

// Real-time validation schema
export const RealTimeValidationSchema = z.object({
  field_name: z.string(),
  value: z.unknown(),
  is_valid: z.boolean(),
  errors: z.array(z.string()),
  debounce_ms: z.number().int().min(0).default(300),
  validate_on_blur: z.boolean().default(true),
  validate_on_change: z.boolean().default(false),
});

// Validation rule schema
export const ValidationRuleSchema = z.object({
  name: z.string(),
  type: z.enum(["required", "format", "range", "custom"]),
  message: z.string(),
  params: z.record(z.string(), z.unknown()).optional(),
  severity: z.enum(["error", "warning", "info"]).default("error"),
  condition: z.string().optional(), // Conditional validation expression
});

// Validation configuration schema
export const ValidationConfigSchema = z.object({
  mode: z.enum(["strict", "lenient", "custom"]).default("strict"),
  stop_on_first_error: z.boolean().default(false),
  validate_on_submit: z.boolean().default(true),
  validate_on_change: z.boolean().default(false),
  validate_on_blur: z.boolean().default(true),
  debounce_ms: z.number().int().min(0).default(300),
  show_warnings: z.boolean().default(true),
  custom_rules: z.array(ValidationRuleSchema).optional(),
});

// Async validation schema
export const AsyncValidationSchema = z.object({
  field_name: z.string(),
  validation_url: z.string().url(),
  method: z.enum(["GET", "POST"]).default("POST"),
  timeout_ms: z.number().int().min(100).default(5000),
  retry_count: z.number().int().min(0).default(2),
  cache_duration_ms: z.number().int().min(0).default(60000),
});

// Validation hook configuration schema
export const ValidationHookConfigSchema = z.object({
  schema: z.unknown(), // Zod schema - will be validated at runtime
  mode: z.enum(["onChange", "onBlur", "onSubmit", "all"]).default("onBlur"),
  revalidate_mode: z
    .enum(["onChange", "onBlur", "onSubmit"])
    .default("onChange"),
  default_values: z.record(z.string(), z.unknown()).optional(),
  reset_on_success: z.boolean().default(false),
  focus_on_error: z.boolean().default(true),
});

// Export types for TypeScript usage
export type ValidationOption = z.infer<typeof ValidationOptionSchema>;
export type ValidationContext = z.infer<typeof ValidationContextSchema>;
export type ValidationState = z.infer<typeof ValidationStateSchema>;
export type PopulationValidationRequest = z.infer<
  typeof PopulationValidationRequestSchema
>;
export type PopulationValidationResponse = z.infer<
  typeof PopulationValidationResponseSchema
>;
export type AttributeValidationResult = z.infer<
  typeof AttributeValidationResultSchema
>;
export type LevelValidation = z.infer<typeof LevelValidationSchema>;
export type TraitValidationResult = z.infer<typeof TraitValidationResultSchema>;
export type FieldValidation = z.infer<typeof FieldValidationSchema>;
export type FormValidationState = z.infer<typeof FormValidationStateSchema>;
export type ValidationErrorDetail = z.infer<typeof ValidationErrorDetailSchema>;
export type ValidationSummary = z.infer<typeof ValidationSummarySchema>;
export type RealTimeValidation = z.infer<typeof RealTimeValidationSchema>;
export type ValidationRule = z.infer<typeof ValidationRuleSchema>;
export type ValidationConfig = z.infer<typeof ValidationConfigSchema>;
export type AsyncValidation = z.infer<typeof AsyncValidationSchema>;
export type ValidationHookConfig = z.infer<typeof ValidationHookConfigSchema>;
