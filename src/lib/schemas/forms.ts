/**
 * Form validation schemas for client-side forms
 * These schemas validate form inputs and provide error messages
 */

import { z } from 'zod';
import { 
  NonEmptyStringSchema, 
  EmailSchema,
  UrlSchema,
  YearSchema,
  PhoneNumberSchema,
  PositiveIntegerSchema,
  TraitCategorySchema
} from './common';
import { 
  AgeRangeSchema,
  IncomeRangeSchema,
  EducationLevelSchema,
  GenderSchema,
  RacialGroupSchema,
  NumberOfChildrenSchema,
  MaritalStatusSchema,
  HomeOwnershipSchema
} from './demographics';

// Contact form schema
export const ContactFormSchema = z.object({
  name: NonEmptyStringSchema,
  email: EmailSchema,
  subject: NonEmptyStringSchema,
  message: z.string().min(10, 'Message must be at least 10 characters long'),
  phone: PhoneNumberSchema.optional(),
  company: z.string().optional(),
});

// User profile form schema
export const UserProfileFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters long'),
  email: EmailSchema,
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  website: UrlSchema.optional(),
  company: z.string().optional(),
  job_title: z.string().optional(),
  phone: PhoneNumberSchema.optional(),
  notifications: z.object({
    email_updates: z.boolean().default(true),
    experiment_completion: z.boolean().default(true),
    weekly_digest: z.boolean().default(false),
  }).optional(),
});

// Research question form schema
export const ResearchQuestionFormSchema = z.object({
  question: z.string()
    .min(10, 'Research question must be at least 10 characters long')
    .max(500, 'Research question must be less than 500 characters'),
  context: z.string().optional(),
  industry: z.string().optional(),
  target_audience: z.string().optional(),
});

// When/Where form schema
export const WhenWhereFormSchema = z.object({
  year: YearSchema,
  country: NonEmptyStringSchema,
  state: z.string().nullable().optional(),
  region: z.string().optional(),
  city: z.string().optional(),
});

// Demographic traits form schema (enhanced)
export const DemographicTraitsFormSchema = z.object({
  age_range: AgeRangeSchema,
  income_range: IncomeRangeSchema,
  education_levels: z.array(EducationLevelSchema).min(1, 'Select at least one education level'),
  genders: z.array(GenderSchema).min(1, 'Select at least one gender'),
  racial_groups: z.array(RacialGroupSchema).min(1, 'Select at least one racial group'),
  number_of_children: z.array(NumberOfChildrenSchema).min(1, 'Select at least one option'),
  
  // Optional demographic fields
  marital_status: z.array(MaritalStatusSchema).optional(),
  home_ownership: z.array(HomeOwnershipSchema).optional(),
  household_size: z.array(z.string()).optional(),
  
  // Advanced traits
  lifestyle_preferences: z.record(z.string(), z.boolean()).optional(),
  psychological_traits: z.record(z.string(), z.boolean()).optional(),
});

// Attribute creation form schema
export const AttributeFormSchema = z.object({
  name: z.string()
    .min(2, 'Attribute name must be at least 2 characters')
    .max(100, 'Attribute name must be less than 100 characters'),
  description: z.string().optional(),
  type: z.enum(['categorical', 'numerical', 'ordinal']),
  category: TraitCategorySchema.optional(),
  levels: z.array(z.object({
    name: z.string().min(1, 'Level name is required'),
    value: z.union([z.string(), z.number()]),
    order: z.number().int().optional(),
  })).min(2, 'At least 2 levels are required'),
});

// Level creation form schema
export const LevelFormSchema = z.object({
  attribute_id: z.string(),
  levels: z.array(z.object({
    name: NonEmptyStringSchema,
    value: z.union([z.string(), z.number()]),
    description: z.string().optional(),
  })).min(1, 'At least one level is required'),
});

// Trait creation form schema
export const TraitFormSchema = z.object({
  name: z.string()
    .min(2, 'Trait name must be at least 2 characters')
    .max(100, 'Trait name must be less than 100 characters'),
  description: z.string().optional(),
  category: TraitCategorySchema,
  values: z.array(z.string()).min(1, 'At least one value is required'),
  measurement_type: z.enum(['categorical', 'ordinal', 'interval', 'ratio']),
});

// Concept testing form schema
export const ConceptTestingFormSchema = z.object({
  concept_name: z.string()
    .min(2, 'Concept name must be at least 2 characters')
    .max(100, 'Concept name must be less than 100 characters'),
  concept_description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must be less than 1000 characters'),
  image: z.instanceof(File).optional(),
  image_name: z.string().optional(),
  statements: z.array(z.object({
    id: z.string(),
    statement: z.string()
      .min(5, 'Statement must be at least 5 characters')
      .max(200, 'Statement must be less than 200 characters'),
    scale: z.enum(['5-point', '7-point', '10-point']).default('5-point'),
  })).min(1, 'At least one statement is required'),
});

// Persona creation form schema
export const PersonaFormSchema = z.object({
  name: NonEmptyStringSchema,
  age: z.string().optional(),
  gender: z.string().optional(),
  location: z.string().optional(),
  occupation: z.string().optional(),
  income: z.string().optional(),
  education: z.string().optional(),
  marital_status: z.string().optional(),
  household_size: z.string().optional(),
  
  // Detailed persona information
  background: z.string().max(1000).optional(),
  goals: z.string().max(500).optional(),
  pain_points: z.string().max(500).optional(),
  personality_traits: z.string().max(500).optional(),
  behaviors: z.string().max(500).optional(),
  
  // Source information
  source_type: z.enum(['manual', 'linkedin', 'pdf', 'description']).default('manual'),
  source_data: z.string().optional(),
  linkedin_url: UrlSchema.optional(),
});

// File upload form schema
export const FileUploadFormSchema = z.object({
  file: z.instanceof(File)
    .refine((file) => file.size <= 10 * 1024 * 1024, 'File size must be less than 10MB')
    .refine(
      (file) => ['text/csv', 'application/json', 'application/pdf'].includes(file.type),
      'File must be CSV, JSON, or PDF format'
    ),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

// Experiment settings form schema
export const ExperimentSettingsFormSchema = z.object({
  name: z.string()
    .min(2, 'Experiment name must be at least 2 characters')
    .max(100, 'Experiment name must be less than 100 characters'),
  description: z.string().max(500).optional(),
  is_private: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  
  // Advanced settings
  sample_size: PositiveIntegerSchema.optional(),
  confidence_level: z.number().min(0.8).max(0.99).default(0.95),
  max_runtime: z.number().int().min(1).max(1440).optional(), // minutes
  
  // Notification settings
  notify_on_completion: z.boolean().default(true),
  notification_email: EmailSchema.optional(),
});

// Search form schema
export const SearchFormSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  filters: z.object({
    experiment_type: z.enum(['conjoint', 'concept_testing']).optional(),
    status: z.enum(['draft', 'running', 'completed', 'failed']).optional(),
    date_range: z.object({
      start: z.string().datetime().optional(),
      end: z.string().datetime().optional(),
    }).optional(),
    tags: z.array(z.string()).optional(),
  }).optional(),
  sort: z.object({
    field: z.enum(['created_at', 'updated_at', 'name', 'status']).default('updated_at'),
    direction: z.enum(['asc', 'desc']).default('desc'),
  }).optional(),
});

// Feedback form schema
export const FeedbackFormSchema = z.object({
  type: z.enum(['bug', 'feature', 'improvement', 'general']),
  title: NonEmptyStringSchema,
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must be less than 1000 characters'),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  category: z.string().optional(),
  
  // Optional contact information
  contact_email: EmailSchema.optional(),
  allow_contact: z.boolean().default(false),
  
  // Technical information
  browser: z.string().optional(),
  os: z.string().optional(),
  screen_resolution: z.string().optional(),
});

// Export types for TypeScript usage
export type ContactForm = z.infer<typeof ContactFormSchema>;
export type UserProfileForm = z.infer<typeof UserProfileFormSchema>;
export type ResearchQuestionForm = z.infer<typeof ResearchQuestionFormSchema>;
export type WhenWhereForm = z.infer<typeof WhenWhereFormSchema>;
export type DemographicTraitsForm = z.infer<typeof DemographicTraitsFormSchema>;
export type AttributeForm = z.infer<typeof AttributeFormSchema>;
export type LevelForm = z.infer<typeof LevelFormSchema>;
export type TraitForm = z.infer<typeof TraitFormSchema>;
export type ConceptTestingForm = z.infer<typeof ConceptTestingFormSchema>;
export type PersonaForm = z.infer<typeof PersonaFormSchema>;
export type FileUploadForm = z.infer<typeof FileUploadFormSchema>;
export type ExperimentSettingsForm = z.infer<typeof ExperimentSettingsFormSchema>;
export type SearchForm = z.infer<typeof SearchFormSchema>;
export type FeedbackForm = z.infer<typeof FeedbackFormSchema>;
