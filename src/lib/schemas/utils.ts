/**
 * Validation utilities and helper functions for Zod schemas
 * These utilities provide consistent error handling and validation patterns
 */

import { z } from 'zod';
import { NextRequest, NextResponse } from 'next/server';
import { ErrorResponseSchema } from './common';

/**
 * Validates request body against a Zod schema
 * Returns parsed data or throws validation error
 */
export async function validateRequestBody<T extends z.ZodTypeAny>(
  request: NextRequest,
  schema: T
): Promise<z.infer<T>> {
  try {
    const body = await request.json();
    return schema.parse(body);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('Invalid request data', error.errors);
    }
    throw new Error('Failed to parse request body');
  }
}

/**
 * Validates query parameters against a Zod schema
 */
export function validateQueryParams<T extends z.ZodTypeAny>(
  request: NextRequest,
  schema: T
): z.infer<T> {
  try {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    return schema.parse(params);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('Invalid query parameters', error.errors);
    }
    throw new Error('Failed to parse query parameters');
  }
}

/**
 * Validates form data against a Zod schema
 */
export async function validateFormData<T extends z.ZodTypeAny>(
  request: NextRequest,
  schema: T
): Promise<z.infer<T>> {
  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData.entries());
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('Invalid form data', error.errors);
    }
    throw new Error('Failed to parse form data');
  }
}

/**
 * Custom validation error class
 */
export class ValidationError extends Error {
  public readonly errors: z.ZodIssue[];
  public readonly statusCode: number = 400;

  constructor(message: string, errors: z.ZodIssue[]) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

/**
 * Formats Zod validation errors for API responses
 */
export function formatValidationErrors(errors: z.ZodIssue[]) {
  return errors.map((error) => ({
    field: error.path.join('.'),
    message: error.message,
    code: error.code,
  }));
}

/**
 * Creates a standardized error response for validation failures
 */
export function createValidationErrorResponse(error: ValidationError): NextResponse {
  const errorResponse = {
    success: false as const,
    error: error.message,
    details: formatValidationErrors(error.errors),
    statusCode: error.statusCode,
  };

  return NextResponse.json(errorResponse, { status: error.statusCode });
}

/**
 * Creates a standardized error response for general errors
 */
export function createErrorResponse(
  message: string,
  statusCode: number = 500,
  details?: string
): NextResponse {
  const errorResponse = {
    success: false as const,
    error: message,
    message: details,
    statusCode,
  };

  return NextResponse.json(errorResponse, { status: statusCode });
}

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  statusCode: number = 200
): NextResponse {
  const successResponse = {
    success: true as const,
    data,
    message,
  };

  return NextResponse.json(successResponse, { status: statusCode });
}

/**
 * Middleware wrapper for API routes with validation
 */
export function withValidation<T extends z.ZodTypeAny>(
  schema: T,
  handler: (
    request: NextRequest,
    validatedData: z.infer<T>,
    context?: any
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      const validatedData = await validateRequestBody(request, schema);
      return await handler(request, validatedData, context);
    } catch (error) {
      if (error instanceof ValidationError) {
        return createValidationErrorResponse(error);
      }
      
      console.error('API Error:', error);
      return createErrorResponse(
        'Internal server error',
        500,
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  };
}

/**
 * Client-side form validation helper
 */
export function validateFormFields<T extends z.ZodTypeAny>(
  data: unknown,
  schema: T
): { success: true; data: z.infer<T> } | { success: false; errors: Record<string, string> } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {};
      
      error.errors.forEach((err) => {
        const fieldPath = err.path.join('.');
        if (!fieldErrors[fieldPath]) {
          fieldErrors[fieldPath] = err.message;
        }
      });
      
      return { success: false, errors: fieldErrors };
    }
    
    return { 
      success: false, 
      errors: { _form: 'Validation failed' } 
    };
  }
}

/**
 * Safe parsing with default values
 */
export function safeParseWithDefaults<T extends z.ZodTypeAny>(
  data: unknown,
  schema: T,
  defaults: Partial<z.infer<T>> = {}
): z.infer<T> {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return result.data;
  }
  
  // Return defaults merged with any successfully parsed fields
  return { ...defaults, ...data } as z.infer<T>;
}

/**
 * Transform function for preprocessing data before validation
 */
export function preprocessString(value: unknown): string {
  if (typeof value === 'string') {
    return value.trim();
  }
  if (typeof value === 'number') {
    return value.toString();
  }
  return '';
}

export function preprocessNumber(value: unknown): number {
  if (typeof value === 'number') {
    return value;
  }
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
}

export function preprocessBoolean(value: unknown): boolean {
  if (typeof value === 'boolean') {
    return value;
  }
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true' || value === '1';
  }
  if (typeof value === 'number') {
    return value !== 0;
  }
  return false;
}

/**
 * Schema composition helpers
 */
export function makeOptional<T extends z.ZodTypeAny>(schema: T) {
  return schema.optional();
}

export function makeNullable<T extends z.ZodTypeAny>(schema: T) {
  return schema.nullable();
}

export function makeArray<T extends z.ZodTypeAny>(schema: T, minLength = 0) {
  return z.array(schema).min(minLength);
}
