# Zod Schema Validation System

This directory contains a comprehensive Zod-based validation system for consistent schema validation across the application. The system provides runtime validation, TypeScript type safety, and standardized error handling.

## 📁 File Structure

```
src/lib/schemas/
├── index.ts              # Main export file
├── common.ts             # Common schemas and utilities
├── api.ts                # API request/response schemas
├── forms.ts              # Form validation schemas
├── experiments.ts        # Experiment-related schemas
├── demographics.ts       # Demographic and population schemas
├── validation.ts         # Validation-specific schemas
├── objects.ts            # Schemas matching existing interfaces
├── utils.ts              # Validation utilities and helpers
├── hooks.ts              # React hooks for form validation
├── examples/             # Usage examples and migration guides
│   ├── api-migration.ts  # API route migration examples
│   └── form-integration.tsx # Form integration examples
└── README.md            # This documentation
```

## 🚀 Quick Start

### 1. Import Schemas

```typescript
import { 
  DemographicTraitsFormSchema,
  ProductCheckRequestSchema,
  useFormValidation,
  withValidation
} from '@/lib/schemas';
```

### 2. API Route Validation

```typescript
// Before: Manual validation
export const POST = async (req: NextRequest) => {
  const body = await req.json();
  if (!body.why_prompt) {
    return NextResponse.json({ error: "Required field missing" }, { status: 400 });
  }
  // ... rest of handler
};

// After: Zod validation
export const POST = withValidation(
  ProductCheckRequestSchema,
  async (req, validatedData) => {
    // Data is already validated
    const { why_prompt } = validatedData;
    // ... rest of handler
  }
);
```

### 3. Form Validation

```typescript
// Before: Manual form validation
const [errors, setErrors] = useState({});
const validateForm = () => {
  const newErrors = {};
  if (!formData.email) newErrors.email = 'Email required';
  // ... more validation
  setErrors(newErrors);
};

// After: Zod form validation
const {
  values,
  errors,
  isValid,
  getFieldProps,
  handleSubmit
} = useFormValidation(ContactFormSchema, initialValues);
```

## 📋 Schema Categories

### Common Schemas (`common.ts`)
- Basic validation patterns (email, URL, UUID)
- Numeric validations (positive integers, percentages)
- Date and time schemas
- Pagination and API response patterns

### API Schemas (`api.ts`)
- Request/response validation for all API routes
- Authentication and user schemas
- Experiment creation and management
- File upload and processing

### Form Schemas (`forms.ts`)
- Client-side form validation
- User profile and settings forms
- Research question and demographic forms
- File upload and search forms

### Experiment Schemas (`experiments.ts`)
- Experiment configuration and metadata
- Attribute and trait definitions
- Results and analytics schemas
- Validation rules for experiment data

### Demographics Schemas (`demographics.ts`)
- Population trait validation
- Age, income, and demographic ranges
- Lifestyle and psychological traits
- Geographic and state data

## 🛠️ Utilities and Helpers

### Validation Utilities (`utils.ts`)

```typescript
// Validate request body
const validatedData = await validateRequestBody(request, MySchema);

// Create standardized responses
return createSuccessResponse(data);
return createErrorResponse("Error message", 400);

// Client-side validation
const result = validateFormFields(formData, MySchema);
if (result.success) {
  // Use result.data
} else {
  // Handle result.errors
}
```

### React Hooks (`hooks.ts`)

```typescript
// Form validation hook
const form = useFormValidation(MySchema, initialValues, {
  validateOnChange: true,
  validateOnBlur: true,
  focusOnError: true
});

// Field-level validation
const field = useFieldValidation(EmailSchema, '', {
  validateOnChange: true,
  debounceMs: 300
});

// Multi-step form validation
const multiStep = useMultiStepValidation({
  step1: Step1Schema,
  step2: Step2Schema,
  step3: Step3Schema
});
```

## 🔄 Migration Guide

### API Routes

1. **Import required schemas and utilities**
   ```typescript
   import { MyRequestSchema, withValidation, createSuccessResponse } from '@/lib/schemas';
   ```

2. **Replace manual validation**
   ```typescript
   // Before
   if (!body.field) return NextResponse.json({ error: "Required" }, { status: 400 });
   
   // After
   export const POST = withValidation(MyRequestSchema, async (req, validatedData) => {
     // validatedData is already validated
   });
   ```

3. **Standardize error responses**
   ```typescript
   // Before
   return NextResponse.json({ error: "Something went wrong" }, { status: 500 });
   
   // After
   return createErrorResponse("Something went wrong", 500, errorDetails);
   ```

### Forms

1. **Replace manual validation with hooks**
   ```typescript
   const { values, errors, handleSubmit } = useFormValidation(MyFormSchema);
   ```

2. **Use getFieldProps for easy integration**
   ```typescript
   <input {...getFieldProps('email')} />
   {errors.email && <span>{errors.email}</span>}
   ```

3. **Handle form submission**
   ```typescript
   const onSubmit = async (validatedData) => {
     // Data is already validated
     await submitToAPI(validatedData);
   };
   ```

## 📝 Best Practices

### 1. Schema Design
- Use descriptive error messages
- Leverage schema composition for reusability
- Add appropriate constraints (min/max, regex patterns)
- Use transforms for data preprocessing

### 2. Error Handling
- Provide specific, actionable error messages
- Use consistent error response formats
- Handle both validation and runtime errors
- Log errors appropriately for debugging

### 3. Performance
- Use schema caching for frequently used schemas
- Implement debounced validation for real-time feedback
- Validate only when necessary (onBlur vs onChange)
- Consider async validation for expensive checks

### 4. Type Safety
- Export and use inferred types from schemas
- Ensure runtime validation matches TypeScript types
- Use schema types in function signatures
- Validate external data at boundaries

## 🧪 Testing

### Schema Testing
```typescript
import { MySchema } from '@/lib/schemas';

describe('MySchema', () => {
  it('should validate correct data', () => {
    const result = MySchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should reject invalid data', () => {
    const result = MySchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    expect(result.error.errors).toContain(/* expected error */);
  });
});
```

### Form Testing
```typescript
import { useFormValidation } from '@/lib/schemas';
import { renderHook, act } from '@testing-library/react';

test('form validation hook', () => {
  const { result } = renderHook(() => 
    useFormValidation(MySchema, initialValues)
  );

  act(() => {
    result.current.setValue('email', 'invalid-email');
  });

  expect(result.current.errors.email).toBeDefined();
});
```

## 🔍 Debugging

### Common Issues

1. **Schema not found**: Ensure proper import path
2. **Validation failing**: Check schema definition and data structure
3. **Type errors**: Verify schema types match usage
4. **Performance issues**: Consider debouncing and validation timing

### Debug Tools

```typescript
// Enable debug mode in development
if (process.env.NODE_ENV === 'development') {
  console.log('Validation result:', result);
  console.log('Schema:', schema);
  console.log('Data:', data);
}
```

## 📚 Examples

See the `examples/` directory for:
- Complete API route migration examples
- Form integration patterns
- Custom validation scenarios
- Error handling strategies

## 🤝 Contributing

When adding new schemas:
1. Follow existing naming conventions
2. Add comprehensive JSDoc comments
3. Export TypeScript types
4. Include usage examples
5. Add tests for validation logic
6. Update this documentation

## 📖 Resources

- [Zod Documentation](https://zod.dev/)
- [TypeScript Integration](https://zod.dev/?id=typescript-first-schema-validation)
- [Error Handling](https://zod.dev/?id=error-handling)
- [Schema Composition](https://zod.dev/?id=composing-schemas)
