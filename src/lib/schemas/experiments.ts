/**
 * Experiment-related validation schemas
 * These schemas validate experiment data, attributes, levels, and related structures
 */

import { z } from 'zod';
import { 
  NonEmptyStringSchema, 
  PositiveIntegerSchema,
  TraitCategorySchema,
  UuidSchema,
  YearSchema
} from './common';

// Attribute level schema
export const AttributeLevelSchema = z.object({
  level: NonEmptyStringSchema,
  active: z.boolean(),
});

// Display attribute schema
export const DisplayAttributeSchema = z.object({
  attribute: NonEmptyStringSchema,
  active: z.boolean(),
  levels: z.array(AttributeLevelSchema),
  category: TraitCategorySchema.optional(),
  attribute_type: z.enum(['categorical', 'numerical', 'ordinal']),
});

// Display trait schema
export const DisplayTraitSchema = z.object({
  title: NonEmptyStringSchema,
  active: z.boolean(),
  values: z.array(z.string()),
  category: TraitCategorySchema.optional(),
});

// Brand attribute combination schema
export const BrandAttributeCombinationSchema = z.record(
  z.string(), // brand name
  z.record(z.string(), z.string()) // attribute -> value mapping
);

// Experiment configuration schema
export const ExperimentConfigSchema = z.object({
  experiment_type: z.enum(['conjoint', 'concept_testing']),
  question: NonEmptyStringSchema,
  year: YearSchema,
  country: NonEmptyStringSchema,
  state: z.string().nullable().optional(),
  is_private: z.boolean().default(false),
  
  // Population configuration
  population_traits: z.array(DisplayTraitSchema),
  
  // Attribute configuration (for conjoint)
  display_attributes: z.array(DisplayAttributeSchema).optional(),
  realworld_products: z.array(BrandAttributeCombinationSchema).optional(),
  
  // Concept testing configuration
  concept_description: z.string().optional(),
  concept_statements: z.array(z.object({
    statement: NonEmptyStringSchema,
    scale: z.enum(['5-point', '7-point', '10-point']).default('5-point'),
  })).optional(),
  image_name: z.string().optional(),
  
  // External personas
  external_personas: z.array(z.object({
    id: UuidSchema,
    name: NonEmptyStringSchema,
    // Add other persona fields as needed
  })).optional(),
});

// Experiment run status schema
export const ExperimentStatusSchema = z.enum([
  'draft',
  'queued',
  'running',
  'completed',
  'failed',
  'cancelled',
]);

// Experiment metadata schema
export const ExperimentMetadataSchema = z.object({
  id: UuidSchema,
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  created_by: UuidSchema,
  status: ExperimentStatusSchema,
  progress: z.number().min(0).max(100).optional(),
  estimated_completion: z.string().datetime().optional(),
  error_message: z.string().optional(),
});

// Full experiment schema
export const ExperimentSchema = ExperimentConfigSchema.extend({
  ...ExperimentMetadataSchema.shape,
});

// Experiment results schema
export const ExperimentResultsSchema = z.object({
  experiment_id: UuidSchema,
  run_id: UuidSchema,
  status: ExperimentStatusSchema,
  results: z.object({
    // Conjoint results
    utilities: z.record(z.string(), z.number()).optional(),
    importance: z.record(z.string(), z.number()).optional(),
    market_share: z.record(z.string(), z.number()).optional(),
    
    // Concept testing results
    survey_results: z.object({
      avg_score: z.number(),
      net_sentiment: z.number(),
      responses: z.record(z.string(), z.number()),
    }).optional(),
    
    // Common results
    sample_size: PositiveIntegerSchema,
    confidence_interval: z.object({
      lower: z.number(),
      upper: z.number(),
      confidence_level: z.number().min(0).max(1),
    }).optional(),
  }),
  artifacts: z.array(z.object({
    name: NonEmptyStringSchema,
    type: z.enum(['chart', 'table', 'report', 'data']),
    url: z.string().url(),
    size: z.number().optional(),
  })).optional(),
});

// Attribute validation schema
export const AttributeValidationSchema = z.object({
  attributes: z.array(DisplayAttributeSchema),
}).refine(
  (data) => {
    // At least one attribute must be active
    return data.attributes.some(attr => attr.active);
  },
  { message: 'At least one attribute must be selected' }
).refine(
  (data) => {
    // Each active attribute must have at least 2 active levels
    const activeAttributes = data.attributes.filter(attr => attr.active);
    return activeAttributes.every(attr => 
      attr.levels.filter(level => level.active).length >= 2
    );
  },
  { message: 'Each selected attribute must have at least 2 levels' }
);

// Trait validation schema
export const TraitValidationSchema = z.object({
  traits: z.array(DisplayTraitSchema),
}).refine(
  (data) => {
    // At least one trait must be active
    return data.traits.some(trait => trait.active);
  },
  { message: 'At least one trait must be selected' }
).refine(
  (data) => {
    // Each active trait must have at least one value
    const activeTraits = data.traits.filter(trait => trait.active);
    return activeTraits.every(trait => trait.values.length > 0);
  },
  { message: 'Each selected trait must have at least one value' }
);

// Experiment validation schema (combines all validations)
export const ExperimentValidationSchema = z.object({
  config: ExperimentConfigSchema,
  attributes: z.array(DisplayAttributeSchema).optional(),
  traits: z.array(DisplayTraitSchema),
}).refine(
  (data) => {
    // For conjoint experiments, attributes are required
    if (data.config.experiment_type === 'conjoint') {
      return data.attributes && data.attributes.length > 0;
    }
    return true;
  },
  { message: 'Conjoint experiments require at least one attribute' }
).refine(
  (data) => {
    // For concept testing, concept description and statements are required
    if (data.config.experiment_type === 'concept_testing') {
      return data.config.concept_description && 
             data.config.concept_statements && 
             data.config.concept_statements.length > 0;
    }
    return true;
  },
  { message: 'Concept testing experiments require description and statements' }
);

// File state schema for file uploads
export const FileStateSchema = z.object({
  file: z.instanceof(File).nullable(),
  data: z.array(z.unknown()).nullable(),
  error: z.string().nullable(),
});

// LLM model schema
export const LLMModelSchema = z.object({
  name: z.enum(['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'claude-3-sonnet', 'claude-3-haiku']),
  provider: z.enum(['openai', 'anthropic']).optional(),
  version: z.string().optional(),
});

// Query history schema for experiment creation
export const QueryHistorySchema = z.object({
  id: UuidSchema,
  query: NonEmptyStringSchema,
  timestamp: z.string().datetime(),
  experiment_type: z.enum(['conjoint', 'concept_testing']).optional(),
  results: z.object({
    causal_questions: z.array(z.string()).optional(),
    suggestions: z.array(z.string()).optional(),
  }).optional(),
});

// Causal question schema
export const CausalQuestionSchema = z.object({
  id: UuidSchema,
  question: NonEmptyStringSchema,
  confidence: z.number().min(0).max(1).optional(),
  category: z.string().optional(),
  suggested_attributes: z.array(z.string()).optional(),
});

// Export types for TypeScript usage
export type AttributeLevel = z.infer<typeof AttributeLevelSchema>;
export type DisplayAttribute = z.infer<typeof DisplayAttributeSchema>;
export type DisplayTrait = z.infer<typeof DisplayTraitSchema>;
export type BrandAttributeCombination = z.infer<typeof BrandAttributeCombinationSchema>;
export type ExperimentConfig = z.infer<typeof ExperimentConfigSchema>;
export type ExperimentStatus = z.infer<typeof ExperimentStatusSchema>;
export type ExperimentMetadata = z.infer<typeof ExperimentMetadataSchema>;
export type Experiment = z.infer<typeof ExperimentSchema>;
export type ExperimentResults = z.infer<typeof ExperimentResultsSchema>;
export type AttributeValidation = z.infer<typeof AttributeValidationSchema>;
export type TraitValidation = z.infer<typeof TraitValidationSchema>;
export type ExperimentValidation = z.infer<typeof ExperimentValidationSchema>;
export type FileState = z.infer<typeof FileStateSchema>;
export type LLMModel = z.infer<typeof LLMModelSchema>;
export type QueryHistory = z.infer<typeof QueryHistorySchema>;
export type CausalQuestion = z.infer<typeof CausalQuestionSchema>;
