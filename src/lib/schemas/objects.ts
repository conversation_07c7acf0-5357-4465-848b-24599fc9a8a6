/**
 * Zod schemas corresponding to existing TypeScript interfaces in objects.tsx
 * These schemas provide runtime validation for the existing type definitions
 */

import { z } from "zod";
import {
  NonEmptyStringSchema,
  PositiveIntegerSchema,
  TraitCategorySchema,
  CoordinatesSchema,
  UuidSchema,
  YearSchema,
  StateSchema,
  CountrySchema,
} from "./common";
import {
  DisplayAttributeSchema,
  DisplayTraitSchema,
  AttributeLevelSchema,
  BrandAttributeCombinationSchema,
  FileStateSchema,
  LLMModelSchema,
} from "./experiments";
import {
  FinalSelectedPopulationTraitsSchema,
  OneTraitChangeTraitsSchema,
  PopulationTraitsSchema,
  StateDataSchema,
  ValidationResponseSchema,
} from "./demographics";
import {
  CreateExperimentRequestSchema,
  CreateLevelsRequestSchema,
  PersonaSchema,
  ProductLevelsRequestSchema,
} from "./api";

// Base attribute schema
export const AttributeSchema = z.object({
  attribute: NonEmptyStringSchema,
  active: z.boolean(),
  levels: z.array(z.string()),
  category: TraitCategorySchema.optional(),
  attribute_type: z.string(),
});

// File state schema - removed duplicate, use from experiments.ts

// Attribute response schema
export const AttributeResponseSchema = z.object({
  attribute: NonEmptyStringSchema,
  levels: z.array(z.string()),
  category: TraitCategorySchema.optional(),
  attribute_type: z.string(),
});

// Attribute level schema - removed duplicate, use from experiments.ts
// Brand attribute combination schema - removed duplicate, use from experiments.ts

// Real world attributes response schema
export const RealWorldAttributesResponseSchema = z.object({
  attributes: z.array(AttributeResponseSchema),
  success: z.boolean(),
  message: z.string().optional(),
});

// Orthogonal attributes levels request schema
export const OrthogonalAttributesLevelsRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  country: z.string().optional(),
  level_count: PositiveIntegerSchema,
  attribute_count: PositiveIntegerSchema,
  year: YearSchema.optional(),
  category: TraitCategorySchema.optional(),
});

// Add new attribute card levels object schema
export const AddNewAttributeCardLevelsObjectSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  new_attribute: NonEmptyStringSchema,
  existing_attributes: z.array(z.string()),
  levels_count: PositiveIntegerSchema,
  category: TraitCategorySchema.optional(),
});

// Add new trait labels object schema
export const AddNewTraitLabelsObjectSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  new_trait: NonEmptyStringSchema,
  existing_traits: z.array(z.string()),
  levels_count: PositiveIntegerSchema,
  category: TraitCategorySchema.optional(),
});

// State schema (geographic) - removed duplicate, use from common.ts
// Country schema (geographic) - removed duplicate, use from common.ts

// Level schema for display attributes
export const LevelSchema = z.object({
  level: NonEmptyStringSchema,
  active: z.boolean(),
});

// Display attribute schema - removed duplicate, use from experiments.ts
// Display trait schema - removed duplicate, use from experiments.ts

// Population traits schema (comprehensive)
export const PopulationTraitsSchema = z.object({
  // US Specific Traits
  state: z.string().nullable(),
  census_division: z.string().optional(),
  marital_status: z.string().optional(),
  family_size: z.string().optional(),
  hispanic_latino: z.boolean().optional(),
  veteran_status: z.string().optional(),
  migration_status: z.string().optional(),
  speaks_english: z.string().optional(),
  health_insurance: z.boolean().optional(),
  vehicles_in_household: z.string().optional(),

  // Non-US Demographics
  gender: z.array(z.string()).min(1),
  age: z.tuple([z.number().int().min(0), z.number().int().min(0)]),
  education_level: z.array(z.string()).min(1),
  racial_group: z.array(z.string()).min(1),
  hispanic_latino_origin: z.string().optional(),
  home_ownership: z.string().optional(),
  household_size: z.string().optional(),
  household_vehicles: z.string().optional(),
  household_with_children: z.boolean().optional(),
  household_income: z.tuple([z.number().min(0), z.number().min(0)]),
  number_of_children: z.array(z.string()).min(1),

  // Lifestyle Traits
  lifestyle_traits: z
    .object({
      health_consciousness: z.string().optional(),
      price_sensitivity: z.string().optional(),
      brand_loyalty: z.string().optional(),
      environmental_concern: z.string().optional(),
      innovation_adoption: z.string().optional(),
      quality_orientation: z.string().optional(),
      convenience_priority: z.string().optional(),
    })
    .optional(),

  // Psychological Traits
  psychological_traits: z
    .object({
      need_for_uniqueness: z.string().optional(),
      self_monitoring: z.string().optional(),
      risk_aversion: z.string().optional(),
      impulsiveness: z.string().optional(),
      need_for_cognition: z.string().optional(),
      time_orientation: z.string().optional(),
      self_construal: z.string().optional(),
      materialism: z.string().optional(),
    })
    .optional(),
});

// Create experiment request schema - using imported schema from api.ts

// Attributes levels request schema
export const AttributesLevelsRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  country: z.string().optional(),
  level_count: PositiveIntegerSchema,
  attribute_count: PositiveIntegerSchema,
  year: YearSchema.optional(),
  category: TraitCategorySchema.optional(),
});

// Create levels request schema - using imported schema from api.ts

// Real world product check request schema
export const RealWorldProductCheckRequestSchema = z.object({
  question: NonEmptyStringSchema,
});

// Real world product attribute request schema
export const RealWorldProductAttributeRequestSchema = z.object({
  number_of_attributes: PositiveIntegerSchema,
  country: NonEmptyStringSchema,
  why_prompt: NonEmptyStringSchema,
  category: TraitCategorySchema.optional(),
});

// Trait schema
export const TraitSchema = z.object({
  id: UuidSchema,
  set_type: z.string().nullable(),
  type: NonEmptyStringSchema,
  short_description: NonEmptyStringSchema,
  long_description: NonEmptyStringSchema,
  measurement_type: z.enum(["categorical", "ordinal", "interval", "ratio"]),
  ordinal_rank: z.number().nullable(),
  category: TraitCategorySchema.optional(),
});

// LLM model schema - removed duplicate, use from experiments.ts

// State data schema - using imported schema from demographics.ts

// Final selected population traits schema - using imported schema from demographics.ts
// One trait change traits schema - using imported schema from demographics.ts
// Validation response schema - using imported schema from demographics.ts

// Product levels request schema - using imported schema from api.ts
// Persona schema - using imported schema from api.ts

// Export types that match the original interfaces
export type Attribute = z.infer<typeof AttributeSchema>;
export type FileState = z.infer<typeof FileStateSchema>;
export type AttributeResponse = z.infer<typeof AttributeResponseSchema>;
export type AttributeLevel = z.infer<typeof AttributeLevelSchema>;
export type BrandAttributeCombination = z.infer<
  typeof BrandAttributeCombinationSchema
>;
export type RealWorldAttributesResponse = z.infer<
  typeof RealWorldAttributesResponseSchema
>;
export type OrthogonalAttributesLevelsRequest = z.infer<
  typeof OrthogonalAttributesLevelsRequestSchema
>;
export type AddNewAttributeCardLevelsObject = z.infer<
  typeof AddNewAttributeCardLevelsObjectSchema
>;
export type AddNewTraitLabelsObject = z.infer<
  typeof AddNewTraitLabelsObjectSchema
>;
export type State = z.infer<typeof StateSchema>;
export type Country = z.infer<typeof CountrySchema>;
export type Level = z.infer<typeof LevelSchema>;
export type DisplayAttribute = z.infer<typeof DisplayAttributeSchema>;
export type DisplayTrait = z.infer<typeof DisplayTraitSchema>;
export type PopulationTraits = z.infer<typeof PopulationTraitsSchema>;
export type CreateExperimentRequest = z.infer<
  typeof CreateExperimentRequestSchema
>;
export type AttributesLevelsRequest = z.infer<
  typeof AttributesLevelsRequestSchema
>;
export type CreateLevelsRequest = z.infer<typeof CreateLevelsRequestSchema>;
export type RealWorldProductCheckRequest = z.infer<
  typeof RealWorldProductCheckRequestSchema
>;
export type RealWorldProductAttributeRequest = z.infer<
  typeof RealWorldProductAttributeRequestSchema
>;
export type Trait = z.infer<typeof TraitSchema>;
export type LLMModel = z.infer<typeof LLMModelSchema>;
export type StateData = z.infer<typeof StateDataSchema>;
export type FinalSelectedPopulationTraits = z.infer<
  typeof FinalSelectedPopulationTraitsSchema
>;
export type OneTraitChangeTraits = z.infer<typeof OneTraitChangeTraitsSchema>;
export type ValidationResponse = z.infer<typeof ValidationResponseSchema>;
export type ProductLevelsRequest = z.infer<typeof ProductLevelsRequestSchema>;
export type Persona = z.infer<typeof PersonaSchema>;
