/**
 * API validation schemas for all API routes
 * These schemas validate request/response data for API endpoints
 */

import { z } from "zod";
import {
  NonEmptyStringSchema,
  PositiveIntegerSchema,
  NonNegativeIntegerSchema,
  UuidSchema,
  TraitCategorySchema,
  YearSchema,
  UrlSchema,
  EmailSchema,
} from "./common";

// Auth and user schemas
export const UserProfileSchema = z.object({
  id: UuidSchema,
  email: EmailSchema,
  name: z.string().optional(),
  picture: UrlSchema.optional(),
  roles: z.array(z.string()).default([]),
});

// Experiment creation schemas
export const CreateExperimentRequestSchema = z.object({
  question: NonEmptyStringSchema,
  population_traits: z.array(
    z.object({
      title: z.string(),
      active: z.boolean(),
      values: z.array(z.string()),
      category: TraitCategorySchema.optional(),
    })
  ),
  populationTraits: z
    .object({
      state: z.string().nullable(),
      gender: z.array(z.string()).min(1),
      age: z.tuple([z.number(), z.number()]),
      education_level: z.array(z.string()).min(1),
      racial_group: z.array(z.string()).min(1),
      household_income: z.tuple([z.number(), z.number()]),
      number_of_children: z.array(z.string()).min(1),
    })
    .optional(),
  displayAttributes: z.array(
    z.object({
      attribute: z.string(),
      active: z.boolean(),
      levels: z.array(
        z.object({
          level: z.string(),
          active: z.boolean(),
        })
      ),
      category: TraitCategorySchema.optional(),
      attribute_type: z.string(),
    })
  ),
  realworld_products: z.array(
    z.record(z.string(), z.record(z.string(), z.string()))
  ),
  year: YearSchema,
  country: NonEmptyStringSchema,
  is_private: z.boolean().optional().default(false),
  state: z.string().nullable().optional(),
  external_personas: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        age: z.string(),
        gender: z.string(),
        // Add other persona fields as needed
      })
    )
    .optional(),
  experiment_type: z.enum(["conjoint", "concept_testing"]),
  concept_description: z.string().optional(),
  concept_statements: z.array(z.unknown()).optional(),
  image_name: z.string().optional(),
});

// Conjoint statement schemas
export const ConjointStatementRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  suggestion_count: z.number().int().min(1).max(5),
  attribute_count: z.number().int().min(2).max(10),
  statement_history: z.array(z.string()),
});

export const ConjointStatementResponseSchema = z.object({
  is_causal: z.boolean(),
  suggestions: z.array(
    z.object({
      attributes: z.array(z.string()),
      statement: z.string(),
    })
  ),
});

// Product validation schemas
export const ProductCheckRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
});

export const ProductCheckResponseSchema = z.object({
  exists: z.boolean(),
  products: z.array(z.string()).optional(),
  message: z.string().optional(),
});

// Moderation schemas
export const ModerationRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
});

export const ModerationResponseSchema = z.object({
  flagged: z.boolean(),
  categories: z.array(z.string()).optional(),
  message: z.string().optional(),
});

// Causality check schemas
export const CausalityRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
});

export const CausalityResponseSchema = z.object({
  is_causal: z.boolean(),
  suggestions: z.array(z.string()).optional(),
  message: z.string().optional(),
});

// Attribute and level generation schemas
export const AttributeLevelsRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  country: z.string().optional(),
  level_count: PositiveIntegerSchema,
  attribute_count: PositiveIntegerSchema,
  year: YearSchema.optional(),
  category: TraitCategorySchema.optional(),
});

export const CreateLevelsRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  country: z.string().optional(),
  level_count: PositiveIntegerSchema,
  values: z.array(z.string()).min(1),
  category: TraitCategorySchema.optional(),
});

export const ProductLevelsRequestSchema = z.object({
  attribute_count: PositiveIntegerSchema,
  level_count: PositiveIntegerSchema,
  country: z.string().optional(),
  why_prompt: NonEmptyStringSchema,
  num_levels: PositiveIntegerSchema,
  num_attrs: PositiveIntegerSchema,
  category: TraitCategorySchema.optional(),
});

// Trait schemas
export const NewTraitRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  new_trait: NonEmptyStringSchema,
  existing_traits: z.array(z.string()),
  levels_count: PositiveIntegerSchema,
  category: TraitCategorySchema.optional(),
});

export const TraitResponseSchema = z.object({
  id: z.string(),
  set_type: z.string().nullable(),
  type: z.string(),
  short_description: z.string(),
  long_description: z.string(),
  measurement_type: z.string(),
  ordinal_rank: z.number().nullable(),
  category: TraitCategorySchema.optional(),
});

// Run and experiment management schemas
export const RunRequestSchema = z.object({
  runID: UuidSchema,
});

export const UpdateConfigRequestSchema = z.object({
  config_update: z.object({
    set_privacy: z.boolean(),
  }),
});

export const ArtifactRequestSchema = z.object({
  artifactName: NonEmptyStringSchema,
});

// Persona schemas
export const PersonaSchema = z.object({
  id: z.string(),
  name: z.string(),
  age: z.string(),
  gender: z.string(),
  maritalStatus: z.string(),
  income: z.string(),
  education: z.string(),
  racialGroup: z.string(),
  homeOwnership: z.string(),
  vehiclesOwned: z.string(),
  hasDrivingLicense: z.string(),
  location: z.string(),
  occupation: z.string(),
  background: z.string(),
  goals: z.string(),
  painPoints: z.string(),
  personalityTraits: z.string(),
  behaviors: z.string(),
  isDescriptionBased: z.boolean().optional(),
  description: z.string().optional(),
  linkedInSource: z.string().optional(),
});

export const AnalyzePersonaRequestSchema = z.object({
  text: NonEmptyStringSchema,
  type: z.enum(["single", "multiple"]).default("single"),
});

// Persona analysis request schema (matches the API route expectations)
export const PersonaAnalysisRequestSchema = z.object({
  content: NonEmptyStringSchema,
  extractMultiple: z.boolean().default(false),
});

export const AnalyzePersonaResponseSchema = z.object({
  name: z.string().nullish(),
  age: z.string().nullish(),
  gender: z.string().nullish(),
  maritalStatus: z.string().nullish(),
  income: z.string().nullish(),
  education: z.string().nullish(),
  racialGroup: z.string().nullish(),
  homeOwnership: z.string().nullish(),
  vehiclesOwned: z.string().nullish(),
  hasDrivingLicense: z.string().nullish(),
  location: z.string().nullish(),
  occupation: z.string().nullish(),
  background: z.string().nullish(),
  goals: z.string().nullish(),
  painPoints: z.string().nullish(),
  personalityTraits: z.string().nullish(),
  behaviors: z.string().nullish(),
});

// Concept testing schemas
export const LikertLabelRequestSchema = z.object({
  description: NonEmptyStringSchema,
  image_name: NonEmptyStringSchema,
  statements: z
    .array(
      z.object({
        statement: NonEmptyStringSchema,
        scale: z.enum(["5-point", "7-point", "10-point"]).default("5-point"),
      })
    )
    .min(1),
});

export const LikertLabelResponseSchema = z.object({
  labels: z.array(z.string()),
  statement: z.string(),
});

// Subscription and payment schemas
export const SubscriptionStatusSchema = z.object({
  subscription_status: z.object({
    status: z.string(),
    current_period_end: z.number().optional(),
  }),
});

export const CheckoutRequestSchema = z.object({
  stripeCustomerId: NonEmptyStringSchema,
});

// File upload schemas
export const FileUploadRequestSchema = z.object({
  file: z.instanceof(File),
});

// Query parameter schemas for GET requests
export const PaginationQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().int().min(1)).default("1"),
  limit: z
    .string()
    .transform(Number)
    .pipe(z.number().int().min(1).max(100))
    .default("10"),
});

export const ExperimentQuerySchema = z.object({
  type: z.enum(["conjoint", "concept_testing"]).optional(),
  status: z.enum(["draft", "running", "completed", "failed"]).optional(),
  ...PaginationQuerySchema.shape,
});

// Export types for TypeScript usage
export type UserProfile = z.infer<typeof UserProfileSchema>;
export type CreateExperimentRequest = z.infer<
  typeof CreateExperimentRequestSchema
>;
export type ConjointStatementRequest = z.infer<
  typeof ConjointStatementRequestSchema
>;
export type ConjointStatementResponse = z.infer<
  typeof ConjointStatementResponseSchema
>;
export type ProductCheckRequest = z.infer<typeof ProductCheckRequestSchema>;
export type ProductCheckResponse = z.infer<typeof ProductCheckResponseSchema>;
export type ModerationRequest = z.infer<typeof ModerationRequestSchema>;
export type ModerationResponse = z.infer<typeof ModerationResponseSchema>;
export type CausalityRequest = z.infer<typeof CausalityRequestSchema>;
export type CausalityResponse = z.infer<typeof CausalityResponseSchema>;
export type AttributeLevelsRequest = z.infer<
  typeof AttributeLevelsRequestSchema
>;
export type CreateLevelsRequest = z.infer<typeof CreateLevelsRequestSchema>;
export type ProductLevelsRequest = z.infer<typeof ProductLevelsRequestSchema>;
export type NewTraitRequest = z.infer<typeof NewTraitRequestSchema>;
export type TraitResponse = z.infer<typeof TraitResponseSchema>;
export type RunRequest = z.infer<typeof RunRequestSchema>;
export type UpdateConfigRequest = z.infer<typeof UpdateConfigRequestSchema>;
export type ArtifactRequest = z.infer<typeof ArtifactRequestSchema>;
export type Persona = z.infer<typeof PersonaSchema>;
export type AnalyzePersonaRequest = z.infer<typeof AnalyzePersonaRequestSchema>;
export type AnalyzePersonaResponse = z.infer<
  typeof AnalyzePersonaResponseSchema
>;
export type LikertLabelRequest = z.infer<typeof LikertLabelRequestSchema>;
export type LikertLabelResponse = z.infer<typeof LikertLabelResponseSchema>;
export type SubscriptionStatus = z.infer<typeof SubscriptionStatusSchema>;
export type CheckoutRequest = z.infer<typeof CheckoutRequestSchema>;
export type FileUploadRequest = z.infer<typeof FileUploadRequestSchema>;
export type PaginationQuery = z.infer<typeof PaginationQuerySchema>;
export type ExperimentQuery = z.infer<typeof ExperimentQuerySchema>;
