/**
 * Common Zod schemas used across the application
 * These schemas define basic patterns and reusable validation logic
 */

import { z } from 'zod';

// Basic primitive schemas with enhanced validation
export const NonEmptyStringSchema = z.string().min(1, 'This field is required');
export const EmailSchema = z.string().email('Please enter a valid email address');
export const UrlSchema = z.string().url('Please enter a valid URL');
export const UuidSchema = z.string().uuid('Invalid UUID format');

// Numeric schemas with validation
export const PositiveIntegerSchema = z.number().int().positive('Must be a positive integer');
export const NonNegativeIntegerSchema = z.number().int().min(0, 'Must be non-negative');
export const PercentageSchema = z.number().min(0).max(100, 'Must be between 0 and 100');

// Date schemas
export const DateStringSchema = z.string().refine(
  (date) => !isNaN(Date.parse(date)),
  'Invalid date format'
);
export const YearSchema = z.string().regex(/^\d{4}$/, 'Year must be in YYYY format');

// Array schemas
export const NonEmptyArraySchema = <T extends z.ZodTypeAny>(schema: T) =>
  z.array(schema).min(1, 'At least one item is required');

// Range schemas for numeric ranges
export const RangeSchema = z.object({
  min: z.number(),
  max: z.number(),
}).refine(
  (data) => data.min <= data.max,
  { message: 'Minimum value must be less than or equal to maximum value' }
);

// Pagination schemas
export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  offset: z.number().int().min(0).optional(),
});

export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: z.array(dataSchema),
    pagination: z.object({
      page: z.number().int(),
      limit: z.number().int(),
      total: z.number().int(),
      totalPages: z.number().int(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  });

// API Response schemas
export const SuccessResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.literal(true),
    data: dataSchema,
    message: z.string().optional(),
  });

export const ErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  message: z.string().optional(),
  details: z.array(z.object({
    field: z.string().optional(),
    message: z.string(),
    code: z.string().optional(),
  })).optional(),
  statusCode: z.number().int().optional(),
});

// Generic API response that can be either success or error
export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.union([
    SuccessResponseSchema(dataSchema),
    ErrorResponseSchema,
  ]);

// File upload schemas
export const FileUploadSchema = z.object({
  name: z.string(),
  size: z.number().positive(),
  type: z.string(),
  lastModified: z.number().optional(),
});

// Coordinates schema for geographic data
export const CoordinatesSchema = z.object({
  x: z.number(),
  y: z.number(),
});

// Country and State schemas
export const StateSchema = z.object({
  name: z.string(),
  code: z.string(),
  flag: z.string(),
  coords: CoordinatesSchema,
});

export const CountrySchema = z.object({
  name: z.string(),
  flag: z.string(),
  coords: CoordinatesSchema,
  states: z.array(StateSchema).optional(),
});

// Trait category enum
export const TraitCategorySchema = z.enum([
  'usTraits',
  'nonUsTraits',
  'lifestyleTraits',
  'psychologicalTraits',
  'customTraits',
  'personalityTraits',
]);

// Common validation patterns
export const PhoneNumberSchema = z.string().regex(
  /^\+?[\d\s\-\(\)]+$/,
  'Please enter a valid phone number'
);

export const SlugSchema = z.string().regex(
  /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  'Slug must contain only lowercase letters, numbers, and hyphens'
);

// Export types for TypeScript usage
export type NonEmptyString = z.infer<typeof NonEmptyStringSchema>;
export type Email = z.infer<typeof EmailSchema>;
export type Url = z.infer<typeof UrlSchema>;
export type Uuid = z.infer<typeof UuidSchema>;
export type PositiveInteger = z.infer<typeof PositiveIntegerSchema>;
export type NonNegativeInteger = z.infer<typeof NonNegativeIntegerSchema>;
export type Percentage = z.infer<typeof PercentageSchema>;
export type DateString = z.infer<typeof DateStringSchema>;
export type Year = z.infer<typeof YearSchema>;
export type Range = z.infer<typeof RangeSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;
export type Coordinates = z.infer<typeof CoordinatesSchema>;
export type State = z.infer<typeof StateSchema>;
export type Country = z.infer<typeof CountrySchema>;
export type TraitCategory = z.infer<typeof TraitCategorySchema>;
export type PhoneNumber = z.infer<typeof PhoneNumberSchema>;
export type Slug = z.infer<typeof SlugSchema>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
