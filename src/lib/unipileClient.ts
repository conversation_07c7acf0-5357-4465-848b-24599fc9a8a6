import { UnipileClient } from "unipile-node-sdk";

// Define the RequestInit type if it's not available
type RequestInit = {
  method?: string;
  headers?: Record<string, string>;
  body?: string | FormData;
  [key: string]: any;
};

class UnipileClientSingleton {
  private static instance: UnipileClient | null = null;
  private static BASE_URL =
    process.env.UNIPILE_BASE_URL || "https://api11.unipile.com:14110";
  private static ACCESS_TOKEN = process.env.UNIPILE_ACCESS_TOKEN || "";
  private static DEFAULT_ACCOUNT_ID =
    process.env.UNIPILE_DEFAULT_ACCOUNT_ID || "";

  private constructor() {}

  public static getInstance(): UnipileClient {
    if (!UnipileClientSingleton.instance) {
      if (!UnipileClientSingleton.ACCESS_TOKEN) {
        throw new Error("UNIPILE_ACCESS_TOKEN environment variable is not set");
      }
      UnipileClientSingleton.instance = new UnipileClient(
        UnipileClientSingleton.BASE_URL,
        UnipileClientSingleton.ACCESS_TOKEN
      );
    }
    return UnipileClientSingleton.instance;
  }

  /**
   * Performs a fetch request to the Unipile API
   * @param endpoint - The API endpoint to call (without the base URL)
   * @param queryParams - Query parameters to include in the URL
   * @param options - Fetch options including method, headers, body, etc.
   * @returns Promise with the response data
   */
  public static async fetch(
    endpoint: string,
    queryParams: Record<string, string> & {
      account_id?: string;
    } = {},
    options: RequestInit = {}
  ): Promise<any> {
    try {
      // Ensure endpoint starts with a slash
      const normalizedEndpoint = endpoint.startsWith("/")
        ? endpoint
        : `/${endpoint}`;

      // Use default account_id if not provided
      const params = {
        ...queryParams,
        account_id:
          queryParams.account_id || UnipileClientSingleton.DEFAULT_ACCOUNT_ID,
      };

      // Merge default headers with provided headers
      const headers = {
        accept: "application/json",
        "X-API-KEY": UnipileClientSingleton.ACCESS_TOKEN,
        "Content-Type": "application/json",
        ...options.headers,
      };

      // Create the full URL with query parameters
      const queryString = new URLSearchParams(params).toString();
      const url = `${UnipileClientSingleton.BASE_URL}${normalizedEndpoint}?${queryString}`;

      // Perform the fetch request
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Check if the response is OK
      if (!response.ok) {
        throw new Error(
          `Unipile API error: ${response.status} ${response.statusText}`
        );
      }

      // Parse and return the JSON response
      return response.json();
    } catch (error) {
      console.log(endpoint, error);
      throw error;
    }
  }
}

// Export both the SDK client instance and the fetch method
export const unipileClient = UnipileClientSingleton.getInstance();
export const unipileFetch = UnipileClientSingleton.fetch;
