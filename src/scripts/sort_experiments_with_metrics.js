#!/usr/bin/env node
// This script will sort the experiments in showcaseHB_Data.json based on average Manhattan similarity
// with average SpearmanR as a tiebreaker using the metrics from hb.txt or from a metrics object

const fs = require("fs");
const path = require("path");

// Path to the showcaseHB_Data.json file
const showcaseDataPath = "src/app/_components/showcaseHB_Data.json";

// Path to the hb.txt file (optional)
const hbTxtPath = "hb.txt";

// Read the showcaseHB_Data.jison file
const showcaseData = JSON.parse(fs.readFileSync(showcaseDataPath, "utf8"));

// Metrics can be provided directly in this file if hb.txt is not available
// Format: { "hb_folder": { "avgManhattan": value, "avgSpearman": value } }
const hardcodedMetrics = {
  // You can paste the metrics here from experiment_metrics.csv or from the console output
  // Example:
  // "Parsons2014": { "avgManhattan": 0.7500, "avgSpearman": 0.8500 },
  // "Hess2012": { "avgManhattan": 0.7200, "avgSpearman": 0.7500 },
};

// Check if hb.txt exists
let hbTxt = "";
let useHardcodedMetrics = false;

try {
  if (fs.existsSync(hbTxtPath)) {
    // Read the hb.txt file if it exists
    hbTxt = fs.readFileSync(hbTxtPath, "utf8");
    console.log("Using metrics from hb.txt");
  } else {
    // Use hardcoded metrics if hb.txt doesn't exist
    useHardcodedMetrics = true;
    console.log("hb.txt not found, using hardcoded metrics");

    // Check if hardcoded metrics are available
    if (Object.keys(hardcodedMetrics).length === 0) {
      console.error(
        "Error: No hardcoded metrics available. Please add metrics to the hardcodedMetrics object in the script."
      );
      process.exit(1);
    }
  }
} catch (err) {
  console.error("Error checking for hb.txt:", err);
  process.exit(1);
}

// Initialize the experiment metrics object
let experimentMetrics = {};

if (useHardcodedMetrics) {
  // Use the hardcoded metrics directly
  experimentMetrics = hardcodedMetrics;
} else {
  // Parse the hb.txt file to extract the metrics for each experiment
  // Split the file by experiment
  const experiments = hbTxt.split(/\}\{/);

  for (const experiment of experiments) {
    // Extract the title (which is the hb_folder)
    const titleMatch = experiment.match(/"title":\s*"([^"]+)"/);
    if (titleMatch) {
      const hbFolder = titleMatch[1];

      // Extract all Manhattan similarity values
      const manhattanValuesMatches =
        experiment.match(/"Manhattan_similarity":\s*([\d\.]+)/g) || [];
      const manhattanValues = manhattanValuesMatches
        .map((match) =>
          parseFloat(match.replace(/"Manhattan_similarity":\s*/, ""))
        )
        .filter((val) => val !== 0);

      // Extract all SpearmanR values
      const spearmanValuesMatches =
        experiment.match(/"spearmanR":\s*([\d\.\-]+)/g) || [];
      const spearmanValues = spearmanValuesMatches
        .map((match) => parseFloat(match.replace(/"spearmanR":\s*/, "")))
        .filter((val) => val !== 0);

      // Calculate average Manhattan similarity and average SpearmanR
      const avgManhattan =
        manhattanValues.length > 0
          ? manhattanValues.reduce((sum, val) => sum + val, 0) /
            manhattanValues.length
          : 0;

      const avgSpearman =
        spearmanValues.length > 0
          ? spearmanValues.reduce((sum, val) => sum + val, 0) /
            spearmanValues.length
          : 0;

      experimentMetrics[hbFolder] = {
        avgManhattan,
        avgSpearman,
        numModels: manhattanValues.length,
      };
    }
  }

  // Save the extracted metrics to a file for future use
  const metricsOutputPath = "experiment_metrics.json";
  fs.writeFileSync(
    metricsOutputPath,
    JSON.stringify(experimentMetrics, null, 2)
  );
  console.log(`Extracted metrics saved to ${metricsOutputPath} for future use`);
  console.log(
    "You can copy these metrics into the hardcodedMetrics object in this script"
  );
}

// Create a backup of the original file
const backupPath = `${showcaseDataPath}.bak`;
fs.writeFileSync(backupPath, JSON.stringify(showcaseData, null, 2));
console.log(`Created backup of original file at ${backupPath}`);

// Sort the experiments based on average Manhattan similarity and average SpearmanR
const sortedExperiments = [...showcaseData].sort((a, b) => {
  const aHbFolder = a.hb_folder || "";
  const bHbFolder = b.hb_folder || "";

  const aMetrics = experimentMetrics[aHbFolder] || {
    avgManhattan: -Infinity,
    avgSpearman: -Infinity,
  };
  const bMetrics = experimentMetrics[bHbFolder] || {
    avgManhattan: -Infinity,
    avgSpearman: -Infinity,
  };

  // Compare average Manhattan similarity
  if (aMetrics.avgManhattan !== bMetrics.avgManhattan) {
    return bMetrics.avgManhattan - aMetrics.avgManhattan;
  }

  // If average Manhattan similarity is the same, use average SpearmanR as tiebreaker
  return bMetrics.avgSpearman - aMetrics.avgSpearman;
});

// Write the sorted experiments back to the file
fs.writeFileSync(showcaseDataPath, JSON.stringify(sortedExperiments, null, 2));

console.log("Experiments sorted successfully!");

// Print the sorted order
console.log("\nSorted order of experiments:");
sortedExperiments.forEach((experiment, index) => {
  const hbFolder = experiment.hb_folder || "";
  const title = experiment.title || "";
  const metrics = experimentMetrics[hbFolder] || {
    avgManhattan: -Infinity,
    avgSpearman: -Infinity,
    numModels: 0,
  };

  if (hbFolder in experimentMetrics) {
    console.log(
      `${index + 1}. ${hbFolder} - ${title} (Avg Manhattan: ${metrics.avgManhattan.toFixed(4)}, Avg SpearmanR: ${metrics.avgSpearman.toFixed(4)}, Models: ${metrics.numModels})`
    );
  } else {
    console.log(`${index + 1}. ${hbFolder} - ${title} (No metrics available)`);
  }
});
