/* eslint-disable prettier/prettier */
import { test,expect, BrowserContext,Page } from '@playwright/test';
import runExperiment from "./runExperiment";

let context: BrowserContext;
let page: Page;

test.beforeEach(async ({ browser }) => {
    context = await browser.newContext();
    page = await context.newPage();
  });

  test.afterEach(async () => {
    await context.close();
  });

test("replicating the existing experiment", async()=>{
    await page.goto(`/experiments`)
    await page.waitForTimeout(3000);
    const pageOpen=await page.waitForSelector("text=Your Experiments");
    await expect(pageOpen).toBeTruthy();

    const noExperiments = await page.isVisible('text="You have not created any experiments yet."');

    if(noExperiments){
        await page.close();
    }
    const experimentCard =await page.waitForSelector("#experiments-guide-2")
    await experimentCard.click();

    await page.locator('text=Replicate Experiment').click();

    await page.waitForURL("/ideation/*")

    await runExperiment(page,true)

    await page.waitForTimeout(2000);
})
