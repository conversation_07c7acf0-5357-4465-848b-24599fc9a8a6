/* eslint-disable prettier/prettier */
import {expect} from "@playwright/test";

export default async function runExperiment(page,replicating) {

    if(!replicating){
          // Step 1: Provide a question in the "Why" section
        const whyInput = page.locator('textarea[placeholder="Ask a causal question about human behavior!"]');
        await whyInput.fill("What factors influence people's choice of mobile phones?");
    }
    const whyContinueButton = page.locator("#ideation-guide-6");
    await whyContinueButton.click();
    await page.waitForSelector('#whenAndWhere-Heading');
    if(!replicating){
          // Step 2: Fill out the "When and Where" section
            const whenInput = page.locator("input:nth-of-type(1)");
            await whenInput.fill("2023");

            // Change country to "Germany"
            const countryDropdown = page.locator('button').nth(4);
            await countryDropdown.click();
            await page.locator('text=Germany').click();

            // Change LLM Model to "GPT-3"
            const modelDropdown = page.locator('button').nth(5);
            await modelDropdown.click();
            await page.locator("text=gpt3").click();
    }
    await page.locator("text=Continue").click();

    // Step 3: Select traits in the "Who" section
    await page.locator("text=Economist").click();
    if(!replicating){
        const economistTraits = page.getByRole("checkbox");
        await economistTraits.nth(8).check();
      
        await page.locator("text=Political Scientist").click();
        const politicalTraits = page.getByRole("checkbox");
        await politicalTraits.nth(6).check();
        await politicalTraits.nth(10).check();
      
        await page.locator("text=Affective Scientist").click();
        const affectiveTraits = page.getByRole("checkbox");
        await affectiveTraits.nth(6).check();
      
        // Wait for the continue button to become enabled and click it
        // await page.waitForFunction(() => {
        //   const button = document.querySelector('#ideation-who-guide-8');
        //   return button && !button.disabled;
        // });
        const apiResponse1 = page.waitForResponse('**/api/attributes-levels'); // Replace with the actual endpoint
        const apiResponse2 = page.waitForResponse('**/api/attributes-levels/orthogonal');
    }
    await page.locator('#ideation-who-guide-8').click();

// Step 4: Configure "What" section and run the experiment
  await page.locator("text=Run experiment").click();
  await page.locator("text=keep public").click();
  
  // Wait for navigation to the experiments page
  await page.waitForURL(/experiments/);
  await expect(page).toHaveURL("/experiments");
}
