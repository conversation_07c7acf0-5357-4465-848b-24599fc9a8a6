/* eslint-disable prettier/prettier */
const { chromium } = require( '@playwright/test');
const baseUrl = require('./config.ts');
const http = require('http');
const https = require('https');

interface Cookie {
  name: string,
  value:string,
  domain:string,
  path:string,
  expires:Number,
  httpOnly:Boolean,
  secure:Boolean,
  sameSite:string
}


async function checkServer(): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const protocol = baseUrl.startsWith('https') ? https : http;
    protocol.get(baseUrl, (res: { statusCode: number; }) => {
      if (res.statusCode === 200) {
        resolve(true);
      } else {
        reject(new Error(`Server responded with status code: ${res.statusCode}`));
      }
    }).on('error', () => {
      reject(new Error(`Server is not up`));
    });
  });
};

  
( async ()=>{
    const browser = await chromium.launch({headless:true});
  const context = await browser.newContext();
  const page = await context.newPage();
try{
  await checkServer();
  //Navigate to the target page
  await page.goto(baseUrl, { waitUntil: 'load' });

  await page.waitForSelector('input[type="email"]', { visible: true });
  await page.type('input[type="email"]', '<EMAIL>');

  await page.waitForSelector('input[type="password"]', { visible: true });
  await page.type('input[type="password"]', 'C@u$al1ty#!');

  await page.locator('#\\31-submit').click();
  await page.waitForNavigation({ waitUntil: 'domcontentloaded' });

  //Verify successful login (you can check for an element that only appears when logged in)
  const loggedInElement = await page.$('text=Home');
  if (!loggedInElement) {
    console.error('Login failed or Welcome element not found.');
  } else {
    console.info('Login successful.');
  }
  
  const cookies = await context.cookies();
  const appSessionCookie = cookies.find((cookie:Cookie) => cookie.name === 'appSession');

  if (appSessionCookie) {
    const authData = {
        cookies: [appSessionCookie]
    };
    // Save the authentication state to a file
    const fs = require('fs');
    fs.writeFileSync('auth.json', JSON.stringify(authData, null, 2));
    console.log('Authentication state saved to auth.json');
  } else {
      console.log('No AppSession Cookie found');
  }

}catch(error){
      await browser.close();
      process.exit(1);
}finally{
      await browser.close();
      process.exit(0);
  }
})();
