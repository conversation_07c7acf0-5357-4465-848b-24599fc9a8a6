/* eslint-disable prettier/prettier */
const dotenv = require("dotenv");
dotenv.config({ path: ".env.local" });

interface BaseUrls {
  local: string;
  dev: string;
  test: string;
  cloud: string;
  prod: string;
}

const ENV = process.env.ENVIRONMENT?.trim() || "dev";

const baseUrls: BaseUrls = {
  local: "http://localhost:3000",
  dev: "https://app.dev.subconscious.ai",
  test: "https://app.test.subconscious.ai",
  cloud: "https://app.cloud.subconscious.ai",
  prod: "https://app.subconscious.ai",
};

const environmentUrl: string =
  ENV in baseUrls ? baseUrls[ENV as keyof BaseUrls] : baseUrls.dev;

export { environmentUrl, baseUrls };
